const {VertexAI} = require('@google-cloud/vertexai');


const projectId = 'receitasdb-mk-d7df1';
const agentId = 'f00f6ed4-4c29-4806-a583-fcb0b774a9c1';

// Initialize Vertex with your Cloud project and location
const vertex_ai = new VertexAI({project: projectId, 
    
    location: 'us-central1'});
const model = 'gemini-1.5-flash-002';

const systemInstruction_txt = 
'Você é um assistente do aplicativo lucro na confeitaria.' +
'Os usuários podem tirar dúvidas sobre o aplicativo acessando o instagram lucro.na.confeitaria'+
'nos destaques do instagram tem vídeos ensinando a usar o aplicativo e outras dúvidas ' +
'podem enviar no direct do instagram. '
+ 'Você também é um assistente especializado em confeitaria e sua missão é ajudar os usuários nos preparos na cozinha em geral.' 

+'Para qualquer dúvida relacionada a utilização do app responda que nos destaques do instagram @lucro.na.confeitaria tem vídeos com passo a passo.';


// Instantiate the models
const generativeModel = vertex_ai.preview.getGenerativeModel({
    model: model,
    generationConfig: {
      'maxOutputTokens': 8192,
      'temperature': 1,
      'topP': 0.95,
    },
    safetySettings: [
      {
        'category': 'HARM_CATEGORY_HATE_SPEECH',
        'threshold': 'OFF',
      },
      {
        'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
        'threshold': 'OFF',
      },
      {
        'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        'threshold': 'OFF',
      },
      {
        'category': 'HARM_CATEGORY_HARASSMENT',
        'threshold': 'OFF',
      }
    ],
    systemInstruction: {

      parts: [{"text": systemInstruction_txt}]
    },
  });
  
  const chat = generativeModel.startChat({});

  

  async function sendMessage(message) {

    const streamResult = await chat.sendMessageStream(message);
    return JSON.stringify((await streamResult.response).candidates[0].content);


}

  module.exports = {sendMessage}
  