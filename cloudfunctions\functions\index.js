const vert = require('./chatlucro.js');
const functions = require("firebase-functions");
const admin = require('firebase-admin');
const https = require('https');
const { Console } = require("console");
const { resolve } = require("path");
const { resolveNaptr } = require("dns");
//const gcs = require('@google-cloud/storage')

admin.initializeApp(functions.config().firebase);


exports.chatdalucro =  functions.https.onRequest(async (req,res) => {


    const retorno = await vert.sendMessage(req.body.mensagem ?? "Bom dia");

    res.send(retorno);
    
   });


async function getlastlogin( id, datacorte)  {

   var stuff = {}; 

   var db = admin.firestore();

   var docnome = id+"/Data";

   await db.doc(docnome).get().then(snapshot => {

    if (snapshot.exists) {
    } else {
      //  console.log('Document não existe',docnome);
       stuff= {"id":id,
            "status": "não existe"
           };
    //   db.doc(id).delete();    
        return stuff;
    }

    var _l = snapshot.data().LastLogin ?? "";

    if (_l == "") {
        stuff = {"id":id,
                 "lastlogin": _l
                };
        deletefolder(id);        
      //  db.collection(id).delete();          
        console.log(id+" deletado");           
    } else {
        let _data = _l.substr(0, 10);
     //   console.log(id+" / lastlogin: "+ _data);

       if (_data < datacorte ){
        stuff = {"id":id,
                  "lastlogin": _l
                 };
    //    console.log(id+" adicionado");        
        deletefolder(id);
    }else {
        stuff = {}
        console.log(id+" não incluso "+_data+" < data corte:"+ datacorte);
       }

    }
        
   });

   return stuff;

}   
   
exports.limparantigos =  functions.https.onRequest(async (req,res) => {

    var datacorte = req.body.datacorte ?? "";
    var emails = req.body.emails ?? [];

    console.log("emails",emails);

    if (datacorte == "" )  return res.status(401).send('sem data');
    if (datacorte > "2024-01-01" )  return res.status(401).send('data de corte inválida.');
  
   var stuff = [];
    console.log('inicio list  collections') ;

    for (var i=0;i< emails.length;i++){
        console.log(emails[i]);
        var _r = await getlastlogin(emails[i],datacorte);
        if (_r != {}){
        stuff.push(_r);
        }
    }
    
    return res.status(200).send({"verificados": stuff});
    
   });

   async function deletefolder(folderPath) {
    
    const bucketName = 'receitasdb-mk-d7df1.appspot.com'; // Substitua pelo nome do seu bucket
   // const folderPath = req.body.folderPath ?? ""; // Caminho da pasta a ser deletada

        // Validação básica do cabeçalho de autorização
        if (folderPath == "") {
            return res.status(401).send('enviar dados');
        }
try{
    const bucket = admin.storage().bucket(bucketName);

    // Listar todos os arquivos na pasta
    const [files] = await bucket.getFiles({ prefix: folderPath+ "/"});
   // console.log("files: "+ files);
    // Deletar cada arquivo
    const deletePromises = files.map(file => {
        console.log("deletando "+file);
        return file.delete();
      
        

    });

    if (files != []) {
        await Promise.all(deletePromises);

    }


   
}catch(reason) {
    console.log(reason);
   
};       
}



   exports.deleteFolder = functions.https.onRequest(async (req,res) => {

    const authorizationHeader = req.headers.authorization;

    // Validação básica do cabeçalho de autorização
    if (!authorizationHeader || !authorizationHeader.startsWith('@MayrinckAuth')) {
        return res.status(401).send('Unauthorized OK ?');
    }



    const bucketName = 'receitasdb-mk-d7df1.appspot.com'; // Substitua pelo nome do seu bucket
    const folderPath = req.body.folderPath ?? ""; // Caminho da pasta a ser deletada

        // Validação básica do cabeçalho de autorização
        if (folderPath == "") {
            return res.status(401).send('enviar dados');
        }
try{
    const bucket = admin.storage().bucket(bucketName);

    // Listar todos os arquivos na pasta
    const [files] = await bucket.getFiles({ prefix: folderPath+ "/"});
    console.log("files: "+ files);
    // Deletar cada arquivo
    const deletePromises = files.map(file => {
        console.log("deletando "+file);
        return file.delete();
      
    });

    if (files != []) {
        await Promise.all(deletePromises);

    }

    return res.status(200).send({"ERRO": 'Pasta deletada com sucesso'});
 

}catch(reason) {
    console.log(reason);
    return res.status(401).send({"ERRO": reason});
};       
});   


exports.scheduledupdateEduzz = functions.pubsub.schedule('every 60 minutes').onRun((context) => {
 
    getTokenEduzz();
    return null;
  });



exports.updateEduzz = functions.https.onRequest((req,res) => {

res.send(getTokenEduzz());

});
//// functions for Eduzz


var token = "";
var contratos = "";

function getToken(body) {

    var _p = JSON.parse(body);
    token = _p["data"]["token"];

}

function getContratos(body) {

    contratos = "";
    var _p = JSON.parse(body);
    contratos = _p["data"];

    if(contratos != "") {

        contratos.forEach((e) => {
            tratarContrato(e);
        });
    }

}

function tratarContrato(_e) {
 
    var dataValidade = "";
    var _cancelado = false;
    var isPro = false;

    switch(_e["product_id"]) {
//    assinatura mensal
    case 905465:
    case 815498:
      if (_e["contract_status"] == "Em dia") {
        dataValidade =formatDate(addDays(Date.parse(_e["payment_last_date"]),32));
        dataValidade += "T23:00:00.000";
        isPro = true;
      }
    break;
//    assinatura anual
    case 905469:
    case 936810:
      if (_e["contract_status"] == "Em dia"){
        dataValidade =formatDate(addDays(Date.parse(_e["payment_last_date"]),370));
      dataValidade += "T23:00:00.000";
        isPro = true;
      }  
      else if((_e["contract_status"] == "Trial") ) {
        dataValidade =formatDate(addDays(Date.parse(_e["payment_last_date"]),8));      
        dataValidade += "T23:00:00.000";
        isPro = true;
      }
    break;
    }

    var _cont = _e["contract_invoice"].toString();

    _cont = "Eduzz:" + _cont;

    if ( isPro) {
    updateLicense(_cancelado,
        _e["client_email"],
        formatDate(Date.parse(_e["payment_last_date"]))+ "T23:00:00.000",
        dataValidade,
        dataValidade,
        isPro,
        "Eduzz",
        _e["product_name"],
        _cont) ;
    }
}


function subDays(date, days) {
    var result = new Date(date);
    result.setDate(result.getDate() - days);
    return result;
  }

  function addDays(date, days) {
    var result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

function formatDate(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2) 
        month = '0' + month;
    if (day.length < 2) 
        day = '0' + day;

    return [year, month, day].join('-');
}

function getDataEduzz(_type) {
    const mHostname ='api2.eduzz.com';
    var rData ="";
    var dados = "";

    var mPath  = '/subscription/get_contract_list/';
    mPath += "?start_date="+formatDate(subDays(Date(),1));
    mPath += "&end_date=" + formatDate(Date());
    mPath += "&date_type="+ _type; // creation update
    
    const options = {
                     hostname: mHostname,
                     path: mPath ,
                     method: 'GET',
                     headers: {
                        "Content-type": "application/json",
                         'Token' : token
                     }
             };
   
     function getcode() {
        return new  Promise((resolve,reject) => {
        var body = [];
        https.request(options, (response) =>{
            response.setEncoding('utf8');
            response.on('data',(d) =>{
                            body.push(d);
                       });
            response.on('end',function(){
                            resolve(body);                
                        });
             }
        ).end();

        });
    }

    getcode().then((body)=> {
        getContratos(body);

    });
   
    return rData;

    }

function getTokenEduzz() {
    const publicKey = "15994944";
    const apiKey = "09018412E9";
    const mHostname ='api2.eduzz.com';
    const url = "https://api2.eduzz.com/credential/generate_token";
    var rData ="";
    var dados = "";

    var mPath  = '/credential/generate_token';
    mPath += "?email=<EMAIL>";
    mPath += "&publickey="+publicKey;
    mPath += "&apikey="+apiKey;
    
    const options = {
                     hostname: mHostname,
                     path: mPath ,
                     method: 'POST',
             };
             

    callback = function (response) {
                    response.setEncoding('utf8');
                    response.on('data',(d) =>{
                                    resolve(rData);
                               });
                    response.on('end',function(){
                                });
      };

     function getcode() {
        return new  Promise((resolve,reject) => {
        var body = [];
        https.request(options, (response) =>{
            response.setEncoding('utf8');
            response.on('data',(d) =>{
                            body.push(d);
                       });
            response.on('end',function(){
                            resolve(body);                
                        });
                     
             }
        ).end();

        });
    }

    getcode().then((body)=> {
        getToken(body);
        console.log("contratos creation");
        getDataEduzz("creation");
        console.log("contratos update");
        getDataEduzz("update");
        console.log("contratos fim");


    });
  
    return rData;

    }



function updateLicense(_cancelado,
    _email,
    _dataInicio,
    _dataTrial,
    _dataValidade,
    _isPro,
    _plataforma,
    _tipo,
    _transaction) {

   // _email = "<EMAIL>";
   // _isPro = true;
    const mPath = '/_Configuracao/config/Assinantes/' + _email;

    var db = admin.firestore();

    data = {
        cancelado : _cancelado,
        dataAlteracao : formatDate(Date())+ "T23:00:00.000",
        dataInicio : _dataInicio,
        dataTrial: _dataTrial,
        dataValidade : _dataValidade,
        isProUser : _isPro,
        plataforma : _plataforma,
        tipoLicenca :_tipo,
        transactionId : _transaction
    };
  
                       
      try {  
            db.doc(mPath).set({"License":data});
            console.log(_email + " isPro: " + _isPro + "Validade: " + _dataValidade);

       }catch(reason) {
           console.log(reason);
          
       };       
       
}    




exports.scheduledRankingUpdate = functions.pubsub.schedule('every 60 minutes').onRun((context) => {
 
    const mHostname ='us-central1-receitasdb-mk-d7df1.cloudfunctions.net';
    var rData ="";
    var dados = "";

    var mPath  = '/updateRanking';
    
    const options = {
                     hostname: mHostname,
                     path: mPath ,
                     method: 'GET',
             };
   
     function getcode() {
        return new  Promise((resolve,reject) => {
        var body = [];
        https.request(options, (response) =>{
            response.setEncoding('utf8');
            response.on('data',(d) =>{
                            body.push(d);
                       });
            response.on('end',function(){
                            resolve(body);                
                        });
             }
        ).end();

        });
    }

    getcode().then((body)=> {

        console.log("end http request");

    });
   
    return null;
  });




exports.updateRanking =  functions.https.onRequest((req,res) => {

    
    updateRank();
    res.send("ok");
    
   });

 async function updateRank() {
    
    function compare(a, b) {
        if (a.pontos > b.pontos) {
            return -1;
        } else if (a.pontos < b.pontos) {
            return 1;
        } else {
            return 0;
        }
    }

    function addzero(_i) {
        if (_i < 10)
        return "0" + _i.toString();
        else
        return _i.toString();
    }
    
    function getDateTime() {

        var date = new Date();
        var yyyy = date.getFullYear().toString();
        var mm = addzero(date.getMonth()+1);
        var dd = addzero(date.getDate());
        var hh = addzero(date.getHours());
        var mmm = addzero(date.getMinutes());
        var period =  yyyy + '-' + mm + '-' + dd + 'T'+hh+':'+ mmm ;

        return period;
    
    }


    var date = new Date();
    var yyyy = date.getFullYear().toString();
    var mm = (date.getMonth()+1).toString();
    var mmChars = mm.split('');
    var period =  yyyy + '-' + (mmChars[1]?mm:"0"+mmChars[0]);

    period = '/000000_Ranking/Mensal/' + period;

    var stuff = [];
    var _ret = "Ranking started";

    var db = admin.firestore();
    
    console.log("Ranking update - Started");
    await db.collection(period).get().then(snapshot => {

            snapshot.forEach(doc => {

                try {
                    var _id = doc.id;
                    } catch(reason) {console.log(doc.id + " erro id");}
                    try {
                    var _ap = doc.data().apelido;
                   } catch(reason) {console.log(doc.id + "erro apelido");}
                   try {
                       var _po = doc.data().Pontos;
                   } catch(reason) {console.log(doc.id + "erro pontos");}
                   try {
                       var _pa = doc.data().participar;
                   } catch(reason) {console.log(doc.id + "erro participar");}
       


                if (_id != "Ranking")
                if (_pa) {
               var newelement = {
                   "id": _id,
                   "apelido": _ap,
                   "pontos": _po,
               }

               if (stuff.length == 0) {
                   stuff.push(newelement);
               }else 
                  stuff = stuff.concat(newelement);
               console.log(doc.id + " ok");

            }
           
        });

            stuff = stuff.sort(compare);
            db.collection(period).doc("Ranking").set({"Ranking":stuff,"data":getDateTime()});
            console.log("Ranking update - Sucessfull");
           
                       
        return "ok";
       }).catch(reason => {
        return "Ranking Update fail - " + reason;
       });       
    
}


exports.emailList =  functions.https.onRequest(async (req,res) => {

    var stuff = [];

    var db = admin.firestore();
    
    console.log("Ranking update - Started");
    
    await db.listCollections().then(collections => {
        for (let collection of collections) {
            stuff = stuff.concat(collection.id);

        //  console.log(`Found subcollection with id: ${collection.id}`);
        }
      });

    res.send(stuff);
    
   });

exports.assinantes =  functions.https.onRequest(async (req,res) => {

    var stuff = [];
//   /_Configuracao/config/Assinantes/<EMAIL>
    var db = admin.firestore();
    console.log("assinantes até " + getStartOfToday());
    await db.collection("/_Configuracao/config/Assinantes/").where('License.dataValidade', '>=', getStartOfToday()).get().then(snapshot => {

if (snapshot.empty) {
  console.log('Não retornou documentos');
  return;
}

        snapshot.forEach(doc => {
            var newelement = "";
            try {
                var _id = doc.id;
                } catch(reason) {console.log(doc.id + " erro id");}
                try {
                var _ap = doc.data().License;

                if (_ap.dataValidade > getStartOfToday()) {
                    console.log("maior");
                }else {
                    console.log("menor");
                }


                newelement ="{"+ doc.id + "|"+_ap.dataInicio +"|"+ _ap.dataValidade +"|"+ _ap.plataforma + "|"+_ap.tipoLicenca+"|" + _ap.transactionId +"}";
                
                _ap.email = doc.id;
                
               } catch(reason) {console.log(doc.id + "erro apelido");}

           if (stuff.length == 0) {
               stuff.push(_ap); //   stuff.push(newelement); 
           }else 
              stuff = stuff.concat(_ap);//  stuff = stuff.concat(newelement);
           console.log(doc.id + " ok");

    });
        
        });

    res.send(JSON.stringify(stuff));
    
   });

function getStartOfToday() {
  return formatDate(Date()) + "T23:00:00.000";
}

