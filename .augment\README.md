# Augment AI Project-Specific Guidelines

This directory contains project-specific guidelines and configuration for the Precificacao Flutter application.

## Files Overview

### `project-guidelines.md`
Comprehensive guidelines specific to this Flutter project, including:
- **Correct Technology Stack**: Flutter/Dart (not React/TypeScript)
- **Architecture Patterns**: Provider state management, Material Design
- **Database Integration**: Supabase + Firebase Authentication
- **Internationalization**: Flutter's built-in i18n system
- **Code Conventions**: Dart naming conventions and best practices
- **Specific Features**: Recipe auto-update, cost calculation patterns

### `config.json`
Configuration file that tells Augment AI to prioritize these project-specific guidelines over generic ones.

## How It Works

When you interact with Augment AI in this project, it will:

1. **Read these guidelines first** before providing suggestions
2. **Follow Flutter/Dart patterns** instead of generic web development patterns
3. **Use correct technology references** (Flutter widgets, not React components)
4. **Apply project-specific conventions** for naming, structure, and patterns
5. **Consider the existing codebase** patterns and architecture

## Key Corrections Made

### ❌ Previous Generic Guidelines (Incorrect for this project)
- TypeScript as primary language
- React components and hooks
- React Query for state management
- Wouter for routing
- Tailwind CSS for styling
- react-i18next for internationalization

### ✅ Correct Project-Specific Guidelines
- **Dart** as primary language
- **Flutter widgets** and StatefulWidget/StatelessWidget
- **Provider pattern** for state management
- **Flutter navigation** (Navigator.push, etc.)
- **Material Design** with custom theming
- **Flutter i18n** with .arb files

## Usage Examples

### Before (Generic Guidelines)
```typescript
// Incorrect suggestion based on generic guidelines
const ProductCard: React.FC<ProductProps> = ({ product }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3>{t('product.name')}</h3>
    </div>
  );
};
```

### After (Project-Specific Guidelines)
```dart
// Correct suggestion based on project guidelines
class ProductCard extends StatelessWidget {
  final Produto produto;
  
  const ProductCard({Key? key, required this.produto}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Text(
          AppLocalizations.of(context)?.nomeProduto ?? "Nome do Produto",
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
    );
  }
}
```

## Benefits

1. **Accurate Suggestions**: AI provides Flutter-specific code instead of React code
2. **Consistent Patterns**: Follows established project conventions
3. **Proper Technology Stack**: Uses correct libraries and frameworks
4. **Project Context**: Understands the bakery/confectionery business domain
5. **Internationalization**: Correctly implements Flutter i18n patterns

## Updating Guidelines

To update these guidelines:

1. Edit `project-guidelines.md` with new patterns or conventions
2. Update `config.json` if technology stack changes
3. Test with Augment AI to ensure guidelines are being followed
4. Document any new patterns discovered in the codebase

## Validation

You can validate that the guidelines are working by asking Augment AI to:
- Create a new Flutter widget
- Implement internationalization
- Add a new data model
- Suggest state management patterns

The responses should now be Flutter/Dart specific rather than React/TypeScript.

## Notes

- These guidelines override the general Augment AI guidelines for this project only
- Other projects will continue to use their respective guidelines
- The guidelines are based on actual analysis of the existing codebase
- They reflect the real technology stack and patterns already in use

## Support

If you notice Augment AI still providing incorrect suggestions:
1. Check that the guidelines are being read correctly
2. Update the guidelines with more specific examples
3. Provide feedback to improve the pattern recognition
