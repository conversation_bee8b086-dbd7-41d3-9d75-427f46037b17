{"version": 3, "file": "index.cjs.js", "sources": ["../src/component.ts", "../src/constants.ts", "../src/provider.ts", "../src/component_container.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide mulitple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide mulitple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/can not be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier = this.normalizeInstanceIdentifier(\n        instanceIdentifier\n      );\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(\n        instanceIdentifier\n      );\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return (this.providers.get(name) as unknown) as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, (provider as unknown) as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n"], "names": ["Deferred", "__values", "__read"], "mappings": ";;;;;;;AAyBA;;;;;;;;;;IAoBE,mBACW,IAAO,EACP,eAAmC,EACnC,IAAmB;QAFnB,SAAI,GAAJ,IAAI,CAAG;QACP,oBAAe,GAAf,eAAe,CAAoB;QACnC,SAAI,GAAJ,IAAI,CAAe;QAnB9B,sBAAiB,GAAG,KAAK,CAAC;;;;QAI1B,iBAAY,GAAe,EAAE,CAAC;QAE9B,sBAAiB,qBAA0B;QAE3C,sBAAiB,GAAwC,IAAI,CAAC;KAY1D;IAEJ,wCAAoB,GAApB,UAAqB,IAAuB;QAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;KACb;IAED,wCAAoB,GAApB,UAAqB,iBAA0B;QAC7C,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,OAAO,IAAI,CAAC;KACb;IAED,mCAAe,GAAf,UAAgB,KAAiB;QAC/B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,IAAI,CAAC;KACb;IAED,8CAA0B,GAA1B,UAA2B,QAAsC;QAC/D,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;QAClC,OAAO,IAAI,CAAC;KACb;IACH,gBAAC;AAAD,CAAC;;ACtED;;;;;;;;;;;;;;;;AAiBO,IAAM,kBAAkB,GAAG,WAAW;;ACjB7C;;;;;;;;;;;;;;;;AA6BA;;;;;IAaE,kBACmB,IAAO,EACP,SAA6B;QAD7B,SAAI,GAAJ,IAAI,CAAG;QACP,cAAS,GAAT,SAAS,CAAoB;QAVxC,cAAS,GAAwB,IAAI,CAAC;QAC7B,cAAS,GAAuC,IAAI,GAAG,EAAE,CAAC;QAC1D,sBAAiB,GAG9B,IAAI,GAAG,EAAE,CAAC;QACN,oBAAe,GAAwC,IAAI,GAAG,EAAE,CAAC;KAKrE;;;;;IAMJ,sBAAG,GAAH,UAAI,UAAmB;;QAErB,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE;YACrD,IAAM,QAAQ,GAAG,IAAIA,aAAQ,EAAyB,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;YAE3D,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;gBACxC,IAAI,CAAC,oBAAoB,EAAE,EAC3B;;gBAEA,IAAI;oBACF,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;wBAC3C,kBAAkB,EAAE,oBAAoB;qBACzC,CAAC,CAAC;oBACH,IAAI,QAAQ,EAAE;wBACZ,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;qBAC5B;iBACF;gBAAC,OAAO,CAAC,EAAE;;;iBAGX;aACF;SACF;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAE,CAAC,OAAO,CAAC;KAClE;IAkBD,+BAAY,GAAZ,UAAa,OAGZ;;;QAEC,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,CACpB,CAAC;QACF,IAAM,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,KAAK,CAAC;QAE5C,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACxC,IAAI,CAAC,oBAAoB,EAAE,EAC3B;YACA,IAAI;gBACF,OAAO,IAAI,CAAC,sBAAsB,CAAC;oBACjC,kBAAkB,EAAE,oBAAoB;iBACzC,CAAC,CAAC;aACJ;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,QAAQ,EAAE;oBACZ,OAAO,IAAI,CAAC;iBACb;qBAAM;oBACL,MAAM,CAAC,CAAC;iBACT;aACF;SACF;aAAM;;YAEL,IAAI,QAAQ,EAAE;gBACZ,OAAO,IAAI,CAAC;aACb;iBAAM;gBACL,MAAM,KAAK,CAAC,aAAW,IAAI,CAAC,IAAI,sBAAmB,CAAC,CAAC;aACtD;SACF;KACF;IAED,+BAAY,GAAZ;QACE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,+BAAY,GAAZ,UAAa,SAAuB;;QAClC,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YAChC,MAAM,KAAK,CACT,2BAAyB,SAAS,CAAC,IAAI,sBAAiB,IAAI,CAAC,IAAI,MAAG,CACrE,CAAC;SACH;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,KAAK,CAAC,mBAAiB,IAAI,CAAC,IAAI,+BAA4B,CAAC,CAAC;SACrE;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;QAG3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAChC,OAAO;SACR;;QAGD,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;YAC/B,IAAI;gBACF,IAAI,CAAC,sBAAsB,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,CAAC;aACzE;YAAC,OAAO,CAAC,EAAE;;;;;aAKX;SACF;;;;;YAKD,KAGK,IAAA,KAAAC,eAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAH5B,IAAA,KAAAC,yBAGV,EAFC,kBAAkB,QAAA,EAClB,gBAAgB,QAAA;gBAEhB,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,kBAAkB,CACnB,CAAC;gBAEF,IAAI;;oBAEF,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;wBAC3C,kBAAkB,EAAE,oBAAoB;qBACzC,CAAE,CAAC;oBACJ,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;iBACpC;gBAAC,OAAO,CAAC,EAAE;;;iBAGX;aACF;;;;;;;;;KACF;IAED,gCAAa,GAAb,UAAc,UAAuC;QAAvC,2BAAA,EAAA,+BAAuC;QACnD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KACnC;;;IAIK,yBAAM,GAAZ;;;;;;wBACQ,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;wBAErD,qBAAM,OAAO,CAAC,GAAG,0DACZ,QAAQ;iCACR,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,IAAI,OAAO,GAAA,CAAC;;iCAExC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAC,OAAe,CAAC,QAAS,CAAC,MAAM,EAAE,GAAA,CAAC,iBACnD,QAAQ;iCACR,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,SAAS,IAAI,OAAO,GAAA,CAAC;;iCAEvC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAC,OAAe,CAAC,OAAO,EAAE,GAAA,CAAC,GAC7C,EAAA;;wBATF,SASE,CAAC;;;;;KACJ;IAED,iCAAc,GAAd;QACE,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;KAC/B;IAED,gCAAa,GAAb,UAAc,UAAuC;QAAvC,2BAAA,EAAA,+BAAuC;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACvC;IAED,6BAAU,GAAV,UAAW,IAA4B;;QAA5B,qBAAA,EAAA,SAA4B;QAC7B,IAAA,KAAiB,IAAI,QAAT,EAAZ,OAAO,mBAAG,EAAE,KAAA,CAAU;QAC9B,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACF,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE;YAC5C,MAAM,KAAK,CACN,IAAI,CAAC,IAAI,SAAI,oBAAoB,mCAAgC,CACrE,CAAC;SACH;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,MAAM,KAAK,CAAC,eAAa,IAAI,CAAC,IAAI,iCAA8B,CAAC,CAAC;SACnE;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC3C,kBAAkB,EAAE,oBAAoB;YACxC,OAAO,SAAA;SACR,CAAE,CAAC;;;YAGJ,KAGK,IAAA,KAAAD,eAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAH5B,IAAA,KAAAC,yBAGV,EAFC,kBAAkB,QAAA,EAClB,gBAAgB,QAAA;gBAEhB,IAAM,4BAA4B,GAAG,IAAI,CAAC,2BAA2B,CACnE,kBAAkB,CACnB,CAAC;gBACF,IAAI,oBAAoB,KAAK,4BAA4B,EAAE;oBACzD,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;iBACpC;aACF;;;;;;;;;QAED,OAAO,QAAQ,CAAC;KACjB;;;;;;;;;IAUD,yBAAM,GAAN,UAAO,QAA2B,EAAE,UAAmB;;QACrD,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAM,iBAAiB,GACrB,MAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,mCAC9C,IAAI,GAAG,EAAqB,CAAC;QAC/B,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;QAElE,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClE,IAAI,gBAAgB,EAAE;YACpB,QAAQ,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;SAClD;QAED,OAAO;YACL,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SACpC,CAAC;KACH;;;;;IAMO,wCAAqB,GAA7B,UACE,QAA+B,EAC/B,UAAkB;;QAElB,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;;YACD,KAAuB,IAAA,cAAAD,eAAA,SAAS,CAAA,oCAAA,2DAAE;gBAA7B,IAAM,QAAQ,sBAAA;gBACjB,IAAI;oBACF,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;iBAChC;gBAAC,WAAM;;iBAEP;aACF;;;;;;;;;KACF;IAEO,yCAAsB,GAA9B,UAA+B,EAM9B;YALC,kBAAkB,wBAAA,EAClB,eAAY,EAAZ,OAAO,mBAAG,EAAE,KAAA;QAKZ,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;gBACxD,kBAAkB,EAAE,6BAA6B,CAAC,kBAAkB,CAAC;gBACrE,OAAO,SAAA;aACR,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;;;;;;YAOjD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;;;;;;YAOzD,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;gBACpC,IAAI;oBACF,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,EACd,kBAAkB,EAClB,QAAQ,CACT,CAAC;iBACH;gBAAC,WAAM;;iBAEP;aACF;SACF;QAED,OAAO,QAAQ,IAAI,IAAI,CAAC;KACzB;IAEO,8CAA2B,GAAnC,UACE,UAAuC;QAAvC,2BAAA,EAAA,+BAAuC;QAEvC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,GAAG,kBAAkB,CAAC;SAC3E;aAAM;YACL,OAAO,UAAU,CAAC;SACnB;KACF;IAEO,uCAAoB,GAA5B;QACE,QACE,CAAC,CAAC,IAAI,CAAC,SAAS;YAChB,IAAI,CAAC,SAAS,CAAC,iBAAiB,gCAChC;KACH;IACH,eAAC;AAAD,CAAC,IAAA;AAED;AACA,SAAS,6BAA6B,CAAC,UAAkB;IACvD,OAAO,UAAU,KAAK,kBAAkB,GAAG,SAAS,GAAG,UAAU,CAAC;AACpE,CAAC;AAED,SAAS,gBAAgB,CAAiB,SAAuB;IAC/D,OAAO,SAAS,CAAC,iBAAiB,yBAA6B;AACjE;;ACnXA;;;;;;;;;;;;;;;;AAqBA;;;;IAME,4BAA6B,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QAFxB,cAAS,GAAG,IAAI,GAAG,EAA0B,CAAC;KAElB;;;;;;;;;;IAW7C,yCAAY,GAAZ,UAA6B,SAAuB;QAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;YAC7B,MAAM,IAAI,KAAK,CACb,eAAa,SAAS,CAAC,IAAI,0CAAqC,IAAI,CAAC,IAAM,CAC5E,CAAC;SACH;QAED,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAClC;IAED,oDAAuB,GAAvB,UAAwC,SAAuB;QAC7D,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;;YAE7B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC9B;;;;;;;;IASD,wCAAW,GAAX,UAA4B,IAAO;QACjC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,OAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAA4B,CAAC;SAC7D;;QAGD,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAI,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAG,QAAsC,CAAC,CAAC;QAElE,OAAO,QAAuB,CAAC;KAChC;IAED,yCAAY,GAAZ;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;KAC5C;IACH,yBAAC;AAAD,CAAC;;;;;;"}