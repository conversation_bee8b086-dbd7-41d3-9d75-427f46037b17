{"project": {"name": "Precificacao Flutter App", "type": "flutter", "description": "Flutter application for bakery cost calculation and pricing management", "guidelines": {"file": ".augment/project-guidelines.md", "priority": "high"}}, "technology_stack": {"framework": "Flutter", "language": "Dart", "database": ["Firebase"], "authentication": "Firebase Auth", "state_management": "Provider", "ui_framework": "Material Design", "internationalization": "Flutter i18n"}, "development_rules": {"design_system": "Material Design with custom theming", "code_style": "Dart conventions", "file_naming": "PascalCase for classes, camelCase for utilities", "internationalization_required": true, "default_language": "Portuguese (Brazil)", "supported_languages": ["pt", "en"]}, "specific_patterns": {"auto_update_recipes": true, "cost_calculation": "automatic", "pdf_generation": "template_based", "image_optimization": true, "error_logging": "Firebase Crashlytics"}}