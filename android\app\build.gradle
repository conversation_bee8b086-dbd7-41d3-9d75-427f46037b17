plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id 'com.google.firebase.crashlytics'
    
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '2'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
   def keystorePropertiesFile = rootProject.file('key.properties')
   if (keystorePropertiesFile.exists()) {
       keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
   }

android {
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    ndkVersion "22.1.7171670"

splits {

    // Configures multiple APKs based on ABI.
    abi {

      // Enables building multiple APKs.
      enable true

      // By default all ABIs are included, so use reset() and include to specify that we only
      // want APKs for x86, armeabi-v7a, and mips.
      //reset()

      // Specifies a list of ABIs that Gradle should create APKs for.
     // include "x86", "armeabi-v7a", "mips"

      // Specify that we want to also generate a universal APK that includes all ABIs.
      universalApk true
    }
  }
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.mayrinck.receitas"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 145 //flutterVersionCode.toInteger() 
        versionName flutterVersionName
        multiDexEnabled true

        ndk { abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64','x86' }
    }
   signingConfigs {
       release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
           storePassword keystoreProperties['storePassword']
       }
   }
    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources false
        
            ndk { abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64' }
            //proguardFiles 'proguard-rules.txt', getDefaultProguardFile('proguard-android.txt')
        }
    }
}

flutter {
    source '../..'
}

dependencies {
 // Add the In-App Messaging dependency:
 
    implementation 'androidx.work:work-runtime-ktx:2.9.1'
    implementation platform('com.google.firebase:firebase-bom:33.4.0')
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-appcheck-debug'
    implementation 'com.google.firebase:firebase-crashlytics-ndk'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-appcheck-debug:16.0.0-beta01'
    implementation 'com.google.firebase:firebase-vertexai:16.0.0-beta06'
    implementation 'com.google.firebase:firebase-messaging:20.1.6'

    implementation 'com.google.android.gms:play-services-auth:20.4.0'
    implementation 'com.google.android.gms:play-services-basement:18.1.0'

    def billing_version = "5.0.0"
    def multidex_version = "2.0.1"
    implementation "androidx.multidex:multidex:$multidex_version"
    implementation "com.android.billingclient:billing:$billing_version"
    implementation 'com.parse.bolts:bolts-tasks:1.4.0'
    implementation 'com.google.android.recaptcha:recaptcha:18.4.0'


}
apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.firebase.crashlytics'
//apply plugin: 'kotlin-android-extensions'