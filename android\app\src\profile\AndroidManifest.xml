<manifest xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools"
    package="com.mayrinck.receitas">
    <!-- Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="com.android.vending.CHECK_LICENSE"/>     
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.USE_EXACT_ALARM"/>
    <uses-permission
    android:name="android.permission.USE_FULL_SCREEN_INTENT"
    tools:node="remove" />
 
</manifest>
