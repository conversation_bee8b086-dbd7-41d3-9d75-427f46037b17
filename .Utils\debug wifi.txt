
------------- Conectar Wireless debug  ----------

Setting Up Wireless Debugging in Flutter

To get started with wireless debugging in Flutter, follow these steps:

1. Connect the Device via USB

Initially, you will need to connect your physical device to your computer via USB to enable developer mode and debugging. Make sure you have Flutter and Dart installed, and your development environment is properly configured.

2. Enable Developer Mode on the Device

On your physical device, navigate to the developer settings and enable USB debugging. Additionally, you should enable the option to allow USB debugging when the device is connected via USB.

3. Check for Device Connectivity

Open a terminal window and run the following command to ensure your device is properly connected:

flutter devices
This command should display your connected device’s information.

4. Find the Device IP Address

Next, you need to find your device’s IP address. Run the following command to get the IP address of your connected device:

adb shell ip route
Look for the line that starts with “default via” and note the IP address associated with it.

5. Connect Wirelessly

Now, disconnect the USB cable from your device and run the following command to connect to your device wirelessly using the previously noted IP address:

flutter connect <device-ip-address>:<port>
Replace `<device-ip-address>` with the IP address of your device and `<port>` with the desired port number (e.g., 5555).

Your Flutter development environment should now be connected to your device wirelessly. You can use all the usual debugging commands like `flutter run`, `flutter attach`, and `flutter debug` to debug your app wirelessly.

parear o dispositivo com um codigo de pareamento
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
>adb pair **************:33647
Enter pairing code: 299654 ---- > do celular 

Successfully paired to **************:33647 [guid=adb-RXCWB0149TF-oFXvnE]
