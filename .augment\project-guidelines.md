# Project-Specific Guidelines: Precificacao Flutter App

## Project Overview
This is a **Flutter/Dart** application for bakery cost calculation and pricing management. The app helps confectionery businesses calculate costs, manage recipes, products, and sales.

## Technology Stack

### Core Technologies
- **Framework**: Flutter (Dart)
- **Database**: Supabase (PostgreSQL) + Firebase Authentication
- **State Management**: Provider pattern with ChangeNotifier
- **Internationalization**: Flutter's built-in i18n with .arb files
- **Build System**: Flutter build system with build_runner for code generation

### Key Dependencies
- **Firebase**: Authentication, Analytics, Crashlytics, Storage, Messaging
- **Supabase**: Primary database and storage
- **UI Components**: Material Design with custom styling
- **PDF Generation**: pdf package for reports
- **Image Handling**: image_picker, image_cropper
- **Local Storage**: SharedPreferences via various packages

## Architecture Patterns

### Project Structure
```
lib/
├── Data/                 # Data models and business logic
│   ├── Banco.dart       # Database connection utilities
│   ├── Config/          # App configuration
│   ├── Receitas/        # Recipe models and logic
│   ├── Produtos/        # Product models and logic
│   ├── Clientes/        # Customer models
│   └── Venda/           # Sales models
├── screens/             # UI screens/pages
├── componentes/         # Reusable UI components
├── DoceMenu/           # DoceMenu specific features
├── templates/          # PDF and report templates
└── l10n/               # Internationalization files
```

### Data Layer Patterns
- **Models**: Use `json_annotation` with `@JsonSerializable` for serialization
- **Database**: Centralized database access through `Banco.dart`
- **State Management**: Provider pattern for global state
- **Data Persistence**: Firestore collections with batch operations

## Development Guidelines

### Code Style & Conventions
- **Language**: Dart (not TypeScript)
- **Naming**: 
  - Classes: PascalCase (e.g., `ViewReceitas`, `Produto`)
  - Files: PascalCase for classes, camelCase for utilities
  - Variables: camelCase
  - Constants: UPPER_SNAKE_CASE

### UI/UX Guidelines
- **Design System**: Material Design with custom theming
- **Colors**: Use `AppTheme.dart` for consistent color scheme
- **Typography**: Custom fonts (Felisya, Roboto, OpenSans)
- **Responsive**: Support for mobile, tablet, and web
- **Accessibility**: Follow Flutter accessibility guidelines

### Internationalization (i18n)
- **System**: Flutter's built-in internationalization
- **Files**: `lib/l10n/intl_pt.arb` (Portuguese), `lib/l10n/intl_en.arb` (English)
- **Usage**: `AppLocalizations.of(context)?.keyName`
- **Generation**: Run `flutter gen-l10n` to generate localization classes
- **Default Language**: Portuguese (Brazil)

### Database Integration
- **Primary DB**: Supabase (PostgreSQL)
- **Authentication**: Firebase Authentication
- **Data Models**: 
  - Use `@JsonSerializable` for automatic serialization
  - Implement `fromJson()` and `toJson()` methods
  - Follow established patterns in existing models

### State Management
- **Pattern**: Provider with ChangeNotifier
- **Global State**: Use Provider for app-wide state
- **Local State**: Use StatefulWidget for component-specific state
- **Data Fetching**: Async methods with proper error handling

### Error Handling
- **Logging**: Use `consoleLog()` utility function
- **User Feedback**: Show user-friendly messages with `showMessage()`
- **Crash Reporting**: Firebase Crashlytics integration
- **Validation**: Input validation on both client and server side

### Performance Guidelines
- **Images**: Optimize and compress images
- **Lists**: Use ListView.builder for large datasets
- **Caching**: Implement proper caching strategies
- **Memory**: Dispose controllers and listeners properly

### Security Practices
- **Authentication**: Firebase Auth with proper token validation
- **Data Validation**: Validate all user inputs
- **Permissions**: Request only necessary permissions
- **Sensitive Data**: Never store sensitive data in plain text

## Specific Implementation Patterns

### Recipe Management
- **Auto-update**: When recipes are modified, automatically update dependent recipes and products
- **Cost Calculation**: Implement automatic cost propagation
- **Versioning**: Track recipe changes for audit purposes

### Product Management
- **Pricing**: Automatic price calculation based on ingredients and costs
- **Categories**: Support for product categorization
- **Images**: Product image management with compression

### Sales & Reports
- **PDF Generation**: Use templates for consistent report formatting
- **Analytics**: Track sales metrics and performance
- **Export**: Support CSV and PDF export functionality

## Testing Guidelines
- **Unit Tests**: Test business logic and data models
- **Widget Tests**: Test UI components
- **Integration Tests**: Test complete user flows
- **Test Files**: Place in `test/` directory

## Build & Deployment
- **Development**: `flutter run`
- **Production**: `flutter build` for respective platforms
- **Code Generation**: `flutter packages pub run build_runner build`
- **Localization**: `flutter gen-l10n`

## Common Patterns to Follow

### Data Model Example
```dart
@JsonSerializable(explicitToJson: true)
class Receita {
  String id;
  String nome;
  List<Ingrediente> ingredientes;
  
  Receita({required this.id, required this.nome, required this.ingredientes});
  
  factory Receita.fromJson(Map<String, dynamic> json) => _$ReceitaFromJson(json);
  Map<String, dynamic> toJson() => _$ReceitaToJson(this);
}
```

### Screen Structure
```dart
class ViewReceitas extends StatefulWidget {
  @override
  _ViewReceitasState createState() => _ViewReceitasState();
}

class _ViewReceitasState extends State<ViewReceitas> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar("${AppLocalizations.of(context)?.receitas}", context),
      body: // Screen content
    );
  }
}
```

### Internationalization Usage
```dart
Text(AppLocalizations.of(context)?.receitas ?? "Receitas")
```

## Important Notes
- This is a **Flutter/Dart project**, not React/TypeScript
- Use Flutter's built-in patterns and conventions
- Follow Material Design guidelines
- Maintain consistency with existing codebase patterns
- Always test on multiple screen sizes and orientations
