{"name": "@firebase/component", "version": "0.5.5", "description": "Firebase Component Platform", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/index.esm.js", "module": "dist/index.esm.js", "esm2017": "dist/index.esm2017.js", "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c", "build:deps": "lerna run --scope @firebase/component --include-dependencies build", "dev": "rollup -c -w", "test": "run-p lint test:all", "test:all": "run-p test:browser test:node", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:browser": "karma start --single-run", "test:node": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' nyc --reporter lcovonly -- mocha src/**/*.test.ts --config ../../config/mocharc.node.js"}, "dependencies": {"@firebase/util": "1.2.0", "tslib": "^2.1.0"}, "license": "Apache-2.0", "devDependencies": {"rollup": "2.52.2", "rollup-plugin-typescript2": "0.30.0", "typescript": "4.2.2"}, "repository": {"directory": "packages/component", "type": "git", "url": "https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "dist/index.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}