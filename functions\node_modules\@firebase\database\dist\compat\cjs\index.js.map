{"version": 3, "file": "index.js", "sources": ["../../../src/core/storage/DOMStorageWrapper.ts", "../../../src/core/storage/MemoryStorage.ts", "../../../src/core/storage/storage.ts", "../../../src/core/util/util.ts", "../../../src/core/util/Path.ts", "../../../src/core/util/validation.ts", "../../../src/core/snap/Node.ts", "../../../src/core/snap/indexes/Index.ts", "../../../src/core/snap/snap.ts", "../../../src/core/snap/LeafNode.ts", "../../../src/core/snap/indexes/PriorityIndex.ts", "../../../src/core/view/QueryParams.ts", "../../../src/api/onDisconnect.ts", "../../../src/api/TransactionResult.ts", "../../../src/api/Reference.ts", "../../../src/api/Database.ts", "../../../src/realtime/Constants.ts", "../../../src/core/RepoInfo.ts", "../../../src/core/stats/StatsCollection.ts", "../../../src/core/stats/StatsManager.ts", "../../../src/realtime/polling/PacketReceiver.ts", "../../../src/realtime/BrowserPollConnection.ts", "../../../src/core/version.ts", "../../../src/realtime/WebSocketConnection.ts", "../../../src/realtime/TransportManager.ts", "../../../src/realtime/Connection.ts", "../../../src/core/ServerActions.ts", "../../../src/core/util/EventEmitter.ts", "../../../src/core/util/OnlineMonitor.ts", "../../../src/core/util/VisibilityMonitor.ts", "../../../src/core/PersistentConnection.ts", "../../../src/core/stats/StatsListener.ts", "../../../src/core/stats/StatsReporter.ts", "../../../src/core/Repo.ts", "../../../src/api/internal.ts", "../../../src/api/test_access.ts", "../../../compat/index.node.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { jsonEval, stringify } from '@firebase/util';\n\n/**\n * Wraps a DOM Storage object and:\n * - automatically encode objects as JSON strings before storing them to allow us to store arbitrary types.\n * - prefixes names with \"firebase:\" to avoid collisions with app data.\n *\n * We automatically (see storage.js) create two such wrappers, one for sessionStorage,\n * and one for localStorage.\n *\n */\nexport class DOMStorageWrapper {\n  // Use a prefix to avoid collisions with other stuff saved by the app.\n  private prefix_ = 'firebase:';\n\n  /**\n   * @param domStorage_ - The underlying storage object (e.g. localStorage or sessionStorage)\n   */\n  constructor(private domStorage_: Storage) {}\n\n  /**\n   * @param key - The key to save the value under\n   * @param value - The value being stored, or null to remove the key.\n   */\n  set(key: string, value: unknown | null) {\n    if (value == null) {\n      this.domStorage_.removeItem(this.prefixedName_(key));\n    } else {\n      this.domStorage_.setItem(this.prefixedName_(key), stringify(value));\n    }\n  }\n\n  /**\n   * @returns The value that was stored under this key, or null\n   */\n  get(key: string): unknown {\n    const storedVal = this.domStorage_.getItem(this.prefixedName_(key));\n    if (storedVal == null) {\n      return null;\n    } else {\n      return jsonEval(storedVal);\n    }\n  }\n\n  remove(key: string) {\n    this.domStorage_.removeItem(this.prefixedName_(key));\n  }\n\n  isInMemoryStorage: boolean;\n\n  prefixedName_(name: string): string {\n    return this.prefix_ + name;\n  }\n\n  toString(): string {\n    return this.domStorage_.toString();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { contains } from '@firebase/util';\n\n/**\n * An in-memory storage implementation that matches the API of DOMStorageWrapper\n * (TODO: create interface for both to implement).\n */\nexport class MemoryStorage {\n  private cache_: { [k: string]: unknown } = {};\n\n  set(key: string, value: unknown | null) {\n    if (value == null) {\n      delete this.cache_[key];\n    } else {\n      this.cache_[key] = value;\n    }\n  }\n\n  get(key: string): unknown {\n    if (contains(this.cache_, key)) {\n      return this.cache_[key];\n    }\n    return null;\n  }\n\n  remove(key: string) {\n    delete this.cache_[key];\n  }\n\n  isInMemoryStorage = true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DOMStorageWrapper } from './DOMStorageWrapper';\nimport { MemoryStorage } from './MemoryStorage';\n\ndeclare const window: Window;\n\n/**\n * Helper to create a DOMStorageWrapper or else fall back to MemoryStorage.\n * TODO: Once MemoryStorage and DOMStorageWrapper have a shared interface this method annotation should change\n * to reflect this type\n *\n * @param domStorageName - Name of the underlying storage object\n *   (e.g. 'localStorage' or 'sessionStorage').\n * @returns Turning off type information until a common interface is defined.\n */\nconst createStoragefor = function (\n  domStorageName: string\n): DOMStorageWrapper | MemoryStorage {\n  try {\n    // NOTE: just accessing \"localStorage\" or \"window['localStorage']\" may throw a security exception,\n    // so it must be inside the try/catch.\n    if (\n      typeof window !== 'undefined' &&\n      typeof window[domStorageName] !== 'undefined'\n    ) {\n      // Need to test cache. Just because it's here doesn't mean it works\n      const domStorage = window[domStorageName];\n      domStorage.setItem('firebase:sentinel', 'cache');\n      domStorage.removeItem('firebase:sentinel');\n      return new DOMStorageWrapper(domStorage);\n    }\n  } catch (e) {}\n\n  // Failed to create wrapper.  Just return in-memory storage.\n  // TODO: log?\n  return new MemoryStorage();\n};\n\n/** A storage object that lasts across sessions */\nexport const PersistentStorage = createStoragefor('localStorage');\n\n/** A storage object that only lasts one session */\nexport const SessionStorage = createStoragefor('sessionStorage');\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger, LogLevel } from '@firebase/logger';\nimport {\n  assert,\n  base64,\n  Sha1,\n  stringToByteArray,\n  stringify,\n  isNodeSdk\n} from '@firebase/util';\n\nimport { SessionStorage } from '../storage/storage';\n\n// TODO: revert to import { QueryContext } from '../view/EventRegistration'; once the modular SDK goes GA\n/**\n * This is part of a workaround for an issue in the no-modular '@firebase/database' where its typings\n * reference types from `@firebase/app-exp`.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype QueryContext = any;\ndeclare const window: Window;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ndeclare const Windows: any;\n\nconst logClient = new Logger('@firebase/database');\n\n/**\n * Returns a locally-unique ID (generated by just incrementing up from 0 each time its called).\n */\nexport const LUIDGenerator: () => number = (function () {\n  let id = 1;\n  return function () {\n    return id++;\n  };\n})();\n\n/**\n * Sha1 hash of the input string\n * @param str - The string to hash\n * @returns {!string} The resulting hash\n */\nexport const sha1 = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  const sha1 = new Sha1();\n  sha1.update(utf8Bytes);\n  const sha1Bytes = sha1.digest();\n  return base64.encodeByteArray(sha1Bytes);\n};\n\nconst buildLogMessage_ = function (...varArgs: unknown[]): string {\n  let message = '';\n  for (let i = 0; i < varArgs.length; i++) {\n    const arg = varArgs[i];\n    if (\n      Array.isArray(arg) ||\n      (arg &&\n        typeof arg === 'object' &&\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        typeof (arg as any).length === 'number')\n    ) {\n      message += buildLogMessage_.apply(null, arg);\n    } else if (typeof arg === 'object') {\n      message += stringify(arg);\n    } else {\n      message += arg;\n    }\n    message += ' ';\n  }\n\n  return message;\n};\n\n/**\n * Use this for all debug messages in Firebase.\n */\nexport let logger: ((a: string) => void) | null = null;\n\n/**\n * Flag to check for log availability on first log message\n */\nlet firstLog_ = true;\n\n/**\n * The implementation of Firebase.enableLogging (defined here to break dependencies)\n * @param logger_ - A flag to turn on logging, or a custom logger\n * @param persistent - Whether or not to persist logging settings across refreshes\n */\nexport const enableLogging = function (\n  logger_?: boolean | ((a: string) => void) | null,\n  persistent?: boolean\n) {\n  assert(\n    !persistent || logger_ === true || logger_ === false,\n    \"Can't turn on custom loggers persistently.\"\n  );\n  if (logger_ === true) {\n    logClient.logLevel = LogLevel.VERBOSE;\n    logger = logClient.log.bind(logClient);\n    if (persistent) {\n      SessionStorage.set('logging_enabled', true);\n    }\n  } else if (typeof logger_ === 'function') {\n    logger = logger_;\n  } else {\n    logger = null;\n    SessionStorage.remove('logging_enabled');\n  }\n};\n\nexport const log = function (...varArgs: unknown[]) {\n  if (firstLog_ === true) {\n    firstLog_ = false;\n    if (logger === null && SessionStorage.get('logging_enabled') === true) {\n      enableLogging(true);\n    }\n  }\n\n  if (logger) {\n    const message = buildLogMessage_.apply(null, varArgs);\n    logger(message);\n  }\n};\n\nexport const logWrapper = function (\n  prefix: string\n): (...varArgs: unknown[]) => void {\n  return function (...varArgs: unknown[]) {\n    log(prefix, ...varArgs);\n  };\n};\n\nexport const error = function (...varArgs: string[]) {\n  const message = 'FIREBASE INTERNAL ERROR: ' + buildLogMessage_(...varArgs);\n  logClient.error(message);\n};\n\nexport const fatal = function (...varArgs: string[]) {\n  const message = `FIREBASE FATAL ERROR: ${buildLogMessage_(...varArgs)}`;\n  logClient.error(message);\n  throw new Error(message);\n};\n\nexport const warn = function (...varArgs: unknown[]) {\n  const message = 'FIREBASE WARNING: ' + buildLogMessage_(...varArgs);\n  logClient.warn(message);\n};\n\n/**\n * Logs a warning if the containing page uses https. Called when a call to new Firebase\n * does not use https.\n */\nexport const warnIfPageIsSecure = function () {\n  // Be very careful accessing browser globals. Who knows what may or may not exist.\n  if (\n    typeof window !== 'undefined' &&\n    window.location &&\n    window.location.protocol &&\n    window.location.protocol.indexOf('https:') !== -1\n  ) {\n    warn(\n      'Insecure Firebase access from a secure page. ' +\n        'Please use https in calls to new Firebase().'\n    );\n  }\n};\n\nexport const warnAboutUnsupportedMethod = function (methodName: string) {\n  warn(\n    methodName +\n      ' is unsupported and will likely change soon.  ' +\n      'Please do not use.'\n  );\n};\n\n/**\n * Returns true if data is NaN, or +/- Infinity.\n */\nexport const isInvalidJSONNumber = function (data: unknown): boolean {\n  return (\n    typeof data === 'number' &&\n    (data !== data || // NaN\n      data === Number.POSITIVE_INFINITY ||\n      data === Number.NEGATIVE_INFINITY)\n  );\n};\n\nexport const executeWhenDOMReady = function (fn: () => void) {\n  if (isNodeSdk() || document.readyState === 'complete') {\n    fn();\n  } else {\n    // Modeled after jQuery. Try DOMContentLoaded and onreadystatechange (which\n    // fire before onload), but fall back to onload.\n\n    let called = false;\n    const wrappedFn = function () {\n      if (!document.body) {\n        setTimeout(wrappedFn, Math.floor(10));\n        return;\n      }\n\n      if (!called) {\n        called = true;\n        fn();\n      }\n    };\n\n    if (document.addEventListener) {\n      document.addEventListener('DOMContentLoaded', wrappedFn, false);\n      // fallback to onload.\n      window.addEventListener('load', wrappedFn, false);\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    } else if ((document as any).attachEvent) {\n      // IE.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (document as any).attachEvent('onreadystatechange', () => {\n        if (document.readyState === 'complete') {\n          wrappedFn();\n        }\n      });\n      // fallback to onload.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (window as any).attachEvent('onload', wrappedFn);\n\n      // jQuery has an extra hack for IE that we could employ (based on\n      // http://javascript.nwbox.com/IEContentLoaded/) But it looks really old.\n      // I'm hoping we don't need it.\n    }\n  }\n};\n\n/**\n * Minimum key name. Invalid for actual data, used as a marker to sort before any valid names\n */\nexport const MIN_NAME = '[MIN_NAME]';\n\n/**\n * Maximum key name. Invalid for actual data, used as a marker to sort above any valid names\n */\nexport const MAX_NAME = '[MAX_NAME]';\n\n/**\n * Compares valid Firebase key names, plus min and max name\n */\nexport const nameCompare = function (a: string, b: string): number {\n  if (a === b) {\n    return 0;\n  } else if (a === MIN_NAME || b === MAX_NAME) {\n    return -1;\n  } else if (b === MIN_NAME || a === MAX_NAME) {\n    return 1;\n  } else {\n    const aAsInt = tryParseInt(a),\n      bAsInt = tryParseInt(b);\n\n    if (aAsInt !== null) {\n      if (bAsInt !== null) {\n        return aAsInt - bAsInt === 0 ? a.length - b.length : aAsInt - bAsInt;\n      } else {\n        return -1;\n      }\n    } else if (bAsInt !== null) {\n      return 1;\n    } else {\n      return a < b ? -1 : 1;\n    }\n  }\n};\n\n/**\n * @returns {!number} comparison result.\n */\nexport const stringCompare = function (a: string, b: string): number {\n  if (a === b) {\n    return 0;\n  } else if (a < b) {\n    return -1;\n  } else {\n    return 1;\n  }\n};\n\nexport const requireKey = function (\n  key: string,\n  obj: { [k: string]: unknown }\n): unknown {\n  if (obj && key in obj) {\n    return obj[key];\n  } else {\n    throw new Error(\n      'Missing required key (' + key + ') in object: ' + stringify(obj)\n    );\n  }\n};\n\nexport const ObjectToUniqueKey = function (obj: unknown): string {\n  if (typeof obj !== 'object' || obj === null) {\n    return stringify(obj);\n  }\n\n  const keys = [];\n  // eslint-disable-next-line guard-for-in\n  for (const k in obj) {\n    keys.push(k);\n  }\n\n  // Export as json, but with the keys sorted.\n  keys.sort();\n  let key = '{';\n  for (let i = 0; i < keys.length; i++) {\n    if (i !== 0) {\n      key += ',';\n    }\n    key += stringify(keys[i]);\n    key += ':';\n    key += ObjectToUniqueKey(obj[keys[i]]);\n  }\n\n  key += '}';\n  return key;\n};\n\n/**\n * Splits a string into a number of smaller segments of maximum size\n * @param str - The string\n * @param segsize - The maximum number of chars in the string.\n * @returns The string, split into appropriately-sized chunks\n */\nexport const splitStringBySize = function (\n  str: string,\n  segsize: number\n): string[] {\n  const len = str.length;\n\n  if (len <= segsize) {\n    return [str];\n  }\n\n  const dataSegs = [];\n  for (let c = 0; c < len; c += segsize) {\n    if (c + segsize > len) {\n      dataSegs.push(str.substring(c, len));\n    } else {\n      dataSegs.push(str.substring(c, c + segsize));\n    }\n  }\n  return dataSegs;\n};\n\n/**\n * Apply a function to each (key, value) pair in an object or\n * apply a function to each (index, value) pair in an array\n * @param obj - The object or array to iterate over\n * @param fn - The function to apply\n */\nexport function each(obj: object, fn: (k: string, v: unknown) => void) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn(key, obj[key]);\n    }\n  }\n}\n\n/**\n * Like goog.bind, but doesn't bother to create a closure if opt_context is null/undefined.\n * @param callback - Callback function.\n * @param context - Optional context to bind to.\n *\n */\nexport const bindCallback = function (\n  callback: (a: unknown) => void,\n  context?: object | null\n): (a: unknown) => void {\n  return context ? callback.bind(context) : callback;\n};\n\n/**\n * Borrowed from http://hg.secondlife.com/llsd/src/tip/js/typedarray.js (MIT License)\n * I made one modification at the end and removed the NaN / Infinity\n * handling (since it seemed broken [caused an overflow] and we don't need it).  See MJL comments.\n * @param v - A double\n *\n */\nexport const doubleToIEEE754String = function (v: number): string {\n  assert(!isInvalidJSONNumber(v), 'Invalid JSON number'); // MJL\n\n  const ebits = 11,\n    fbits = 52;\n  const bias = (1 << (ebits - 1)) - 1;\n  let s, e, f, ln, i;\n\n  // Compute sign, exponent, fraction\n  // Skip NaN / Infinity handling --MJL.\n  if (v === 0) {\n    e = 0;\n    f = 0;\n    s = 1 / v === -Infinity ? 1 : 0;\n  } else {\n    s = v < 0;\n    v = Math.abs(v);\n\n    if (v >= Math.pow(2, 1 - bias)) {\n      // Normalized\n      ln = Math.min(Math.floor(Math.log(v) / Math.LN2), bias);\n      e = ln + bias;\n      f = Math.round(v * Math.pow(2, fbits - ln) - Math.pow(2, fbits));\n    } else {\n      // Denormalized\n      e = 0;\n      f = Math.round(v / Math.pow(2, 1 - bias - fbits));\n    }\n  }\n\n  // Pack sign, exponent, fraction\n  const bits = [];\n  for (i = fbits; i; i -= 1) {\n    bits.push(f % 2 ? 1 : 0);\n    f = Math.floor(f / 2);\n  }\n  for (i = ebits; i; i -= 1) {\n    bits.push(e % 2 ? 1 : 0);\n    e = Math.floor(e / 2);\n  }\n  bits.push(s ? 1 : 0);\n  bits.reverse();\n  const str = bits.join('');\n\n  // Return the data as a hex string. --MJL\n  let hexByteString = '';\n  for (i = 0; i < 64; i += 8) {\n    let hexByte = parseInt(str.substr(i, 8), 2).toString(16);\n    if (hexByte.length === 1) {\n      hexByte = '0' + hexByte;\n    }\n    hexByteString = hexByteString + hexByte;\n  }\n  return hexByteString.toLowerCase();\n};\n\n/**\n * Used to detect if we're in a Chrome content script (which executes in an\n * isolated environment where long-polling doesn't work).\n */\nexport const isChromeExtensionContentScript = function (): boolean {\n  return !!(\n    typeof window === 'object' &&\n    window['chrome'] &&\n    window['chrome']['extension'] &&\n    !/^chrome/.test(window.location.href)\n  );\n};\n\n/**\n * Used to detect if we're in a Windows 8 Store app.\n */\nexport const isWindowsStoreApp = function (): boolean {\n  // Check for the presence of a couple WinRT globals\n  return typeof Windows === 'object' && typeof Windows.UI === 'object';\n};\n\n/**\n * Converts a server error code to a Javascript Error\n */\nexport function errorForServerCode(code: string, query: QueryContext): Error {\n  let reason = 'Unknown Error';\n  if (code === 'too_big') {\n    reason =\n      'The data requested exceeds the maximum size ' +\n      'that can be accessed with a single request.';\n  } else if (code === 'permission_denied') {\n    reason = \"Client doesn't have permission to access the desired data.\";\n  } else if (code === 'unavailable') {\n    reason = 'The service is unavailable';\n  }\n\n  const error = new Error(\n    code + ' at ' + query._path.toString() + ': ' + reason\n  );\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (error as any).code = code.toUpperCase();\n  return error;\n}\n\n/**\n * Used to test for integer-looking strings\n */\nexport const INTEGER_REGEXP_ = new RegExp('^-?(0*)\\\\d{1,10}$');\n\n/**\n * For use in keys, the minimum possible 32-bit integer.\n */\nexport const INTEGER_32_MIN = -2147483648;\n\n/**\n * For use in kyes, the maximum possible 32-bit integer.\n */\nexport const INTEGER_32_MAX = 2147483647;\n\n/**\n * If the string contains a 32-bit integer, return it.  Else return null.\n */\nexport const tryParseInt = function (str: string): number | null {\n  if (INTEGER_REGEXP_.test(str)) {\n    const intVal = Number(str);\n    if (intVal >= INTEGER_32_MIN && intVal <= INTEGER_32_MAX) {\n      return intVal;\n    }\n  }\n  return null;\n};\n\n/**\n * Helper to run some code but catch any exceptions and re-throw them later.\n * Useful for preventing user callbacks from breaking internal code.\n *\n * Re-throwing the exception from a setTimeout is a little evil, but it's very\n * convenient (we don't have to try to figure out when is a safe point to\n * re-throw it), and the behavior seems reasonable:\n *\n * * If you aren't pausing on exceptions, you get an error in the console with\n *   the correct stack trace.\n * * If you're pausing on all exceptions, the debugger will pause on your\n *   exception and then again when we rethrow it.\n * * If you're only pausing on uncaught exceptions, the debugger will only pause\n *   on us re-throwing it.\n *\n * @param fn - The code to guard.\n */\nexport const exceptionGuard = function (fn: () => void) {\n  try {\n    fn();\n  } catch (e) {\n    // Re-throw exception when it's safe.\n    setTimeout(() => {\n      // It used to be that \"throw e\" would result in a good console error with\n      // relevant context, but as of Chrome 39, you just get the firebase.js\n      // file/line number where we re-throw it, which is useless. So we log\n      // e.stack explicitly.\n      const stack = e.stack || '';\n      warn('Exception was thrown by user callback.', stack);\n      throw e;\n    }, Math.floor(0));\n  }\n};\n\n/**\n * Helper function to safely call opt_callback with the specified arguments.  It:\n * 1. Turns into a no-op if opt_callback is null or undefined.\n * 2. Wraps the call inside exceptionGuard to prevent exceptions from breaking our state.\n *\n * @param callback - Optional onComplete callback.\n * @param varArgs - Arbitrary args to be passed to opt_onComplete\n */\nexport const callUserCallback = function (\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  callback?: Function | null,\n  ...varArgs: unknown[]\n) {\n  if (typeof callback === 'function') {\n    exceptionGuard(() => {\n      callback(...varArgs);\n    });\n  }\n};\n\n/**\n * @returns {boolean} true if we think we're currently being crawled.\n */\nexport const beingCrawled = function (): boolean {\n  const userAgent =\n    (typeof window === 'object' &&\n      window['navigator'] &&\n      window['navigator']['userAgent']) ||\n    '';\n\n  // For now we whitelist the most popular crawlers.  We should refine this to be the set of crawlers we\n  // believe to support JavaScript/AJAX rendering.\n  // NOTE: Google Webmaster Tools doesn't really belong, but their \"This is how a visitor to your website\n  // would have seen the page\" is flaky if we don't treat it as a crawler.\n  return (\n    userAgent.search(\n      /googlebot|google webmaster tools|bingbot|yahoo! slurp|baiduspider|yandexbot|duckduckbot/i\n    ) >= 0\n  );\n};\n\n/**\n * Export a property of an object using a getter function.\n */\nexport const exportPropGetter = function (\n  object: object,\n  name: string,\n  fnGet: () => unknown\n) {\n  Object.defineProperty(object, name, { get: fnGet });\n};\n\n/**\n * Same as setTimeout() except on Node.JS it will /not/ prevent the process from exiting.\n *\n * It is removed with clearTimeout() as normal.\n *\n * @param fn - Function to run.\n * @param time - Milliseconds to wait before running.\n * @returns The setTimeout() return value.\n */\nexport const setTimeoutNonBlocking = function (\n  fn: () => void,\n  time: number\n): number | object {\n  const timeout: number | object = setTimeout(fn, time);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (typeof timeout === 'object' && (timeout as any)['unref']) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (timeout as any)['unref']();\n  }\n  return timeout;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { stringLength } from '@firebase/util';\n\nimport { nameCompare } from './util';\n\n/** Maximum key depth. */\nconst MAX_PATH_DEPTH = 32;\n\n/** Maximum number of (UTF8) bytes in a Firebase path. */\nconst MAX_PATH_LENGTH_BYTES = 768;\n\n/**\n * An immutable object representing a parsed path.  It's immutable so that you\n * can pass them around to other functions without worrying about them changing\n * it.\n */\n\nexport class Path {\n  pieces_: string[];\n  pieceNum_: number;\n\n  /**\n   * @param pathOrString - Path string to parse, or another path, or the raw\n   * tokens array\n   */\n  constructor(pathOrString: string | string[], pieceNum?: number) {\n    if (pieceNum === void 0) {\n      this.pieces_ = (pathOrString as string).split('/');\n\n      // Remove empty pieces.\n      let copyTo = 0;\n      for (let i = 0; i < this.pieces_.length; i++) {\n        if (this.pieces_[i].length > 0) {\n          this.pieces_[copyTo] = this.pieces_[i];\n          copyTo++;\n        }\n      }\n      this.pieces_.length = copyTo;\n\n      this.pieceNum_ = 0;\n    } else {\n      this.pieces_ = pathOrString as string[];\n      this.pieceNum_ = pieceNum;\n    }\n  }\n\n  toString(): string {\n    let pathString = '';\n    for (let i = this.pieceNum_; i < this.pieces_.length; i++) {\n      if (this.pieces_[i] !== '') {\n        pathString += '/' + this.pieces_[i];\n      }\n    }\n\n    return pathString || '/';\n  }\n}\n\nexport function newEmptyPath(): Path {\n  return new Path('');\n}\n\nexport function pathGetFront(path: Path): string | null {\n  if (path.pieceNum_ >= path.pieces_.length) {\n    return null;\n  }\n\n  return path.pieces_[path.pieceNum_];\n}\n\n/**\n * @returns The number of segments in this path\n */\nexport function pathGetLength(path: Path): number {\n  return path.pieces_.length - path.pieceNum_;\n}\n\nexport function pathPopFront(path: Path): Path {\n  let pieceNum = path.pieceNum_;\n  if (pieceNum < path.pieces_.length) {\n    pieceNum++;\n  }\n  return new Path(path.pieces_, pieceNum);\n}\n\nexport function pathGetBack(path: Path): string | null {\n  if (path.pieceNum_ < path.pieces_.length) {\n    return path.pieces_[path.pieces_.length - 1];\n  }\n\n  return null;\n}\n\nexport function pathToUrlEncodedString(path: Path): string {\n  let pathString = '';\n  for (let i = path.pieceNum_; i < path.pieces_.length; i++) {\n    if (path.pieces_[i] !== '') {\n      pathString += '/' + encodeURIComponent(String(path.pieces_[i]));\n    }\n  }\n\n  return pathString || '/';\n}\n\n/**\n * Shallow copy of the parts of the path.\n *\n */\nexport function pathSlice(path: Path, begin: number = 0): string[] {\n  return path.pieces_.slice(path.pieceNum_ + begin);\n}\n\nexport function pathParent(path: Path): Path | null {\n  if (path.pieceNum_ >= path.pieces_.length) {\n    return null;\n  }\n\n  const pieces = [];\n  for (let i = path.pieceNum_; i < path.pieces_.length - 1; i++) {\n    pieces.push(path.pieces_[i]);\n  }\n\n  return new Path(pieces, 0);\n}\n\nexport function pathChild(path: Path, childPathObj: string | Path): Path {\n  const pieces = [];\n  for (let i = path.pieceNum_; i < path.pieces_.length; i++) {\n    pieces.push(path.pieces_[i]);\n  }\n\n  if (childPathObj instanceof Path) {\n    for (let i = childPathObj.pieceNum_; i < childPathObj.pieces_.length; i++) {\n      pieces.push(childPathObj.pieces_[i]);\n    }\n  } else {\n    const childPieces = childPathObj.split('/');\n    for (let i = 0; i < childPieces.length; i++) {\n      if (childPieces[i].length > 0) {\n        pieces.push(childPieces[i]);\n      }\n    }\n  }\n\n  return new Path(pieces, 0);\n}\n\n/**\n * @returns True if there are no segments in this path\n */\nexport function pathIsEmpty(path: Path): boolean {\n  return path.pieceNum_ >= path.pieces_.length;\n}\n\n/**\n * @returns The path from outerPath to innerPath\n */\nexport function newRelativePath(outerPath: Path, innerPath: Path): Path {\n  const outer = pathGetFront(outerPath),\n    inner = pathGetFront(innerPath);\n  if (outer === null) {\n    return innerPath;\n  } else if (outer === inner) {\n    return newRelativePath(pathPopFront(outerPath), pathPopFront(innerPath));\n  } else {\n    throw new Error(\n      'INTERNAL ERROR: innerPath (' +\n        innerPath +\n        ') is not within ' +\n        'outerPath (' +\n        outerPath +\n        ')'\n    );\n  }\n}\n\n/**\n * @returns -1, 0, 1 if left is less, equal, or greater than the right.\n */\nexport function pathCompare(left: Path, right: Path): number {\n  const leftKeys = pathSlice(left, 0);\n  const rightKeys = pathSlice(right, 0);\n  for (let i = 0; i < leftKeys.length && i < rightKeys.length; i++) {\n    const cmp = nameCompare(leftKeys[i], rightKeys[i]);\n    if (cmp !== 0) {\n      return cmp;\n    }\n  }\n  if (leftKeys.length === rightKeys.length) {\n    return 0;\n  }\n  return leftKeys.length < rightKeys.length ? -1 : 1;\n}\n\n/**\n * @returns true if paths are the same.\n */\nexport function pathEquals(path: Path, other: Path): boolean {\n  if (pathGetLength(path) !== pathGetLength(other)) {\n    return false;\n  }\n\n  for (\n    let i = path.pieceNum_, j = other.pieceNum_;\n    i <= path.pieces_.length;\n    i++, j++\n  ) {\n    if (path.pieces_[i] !== other.pieces_[j]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * @returns True if this path is a parent (or the same as) other\n */\nexport function pathContains(path: Path, other: Path): boolean {\n  let i = path.pieceNum_;\n  let j = other.pieceNum_;\n  if (pathGetLength(path) > pathGetLength(other)) {\n    return false;\n  }\n  while (i < path.pieces_.length) {\n    if (path.pieces_[i] !== other.pieces_[j]) {\n      return false;\n    }\n    ++i;\n    ++j;\n  }\n  return true;\n}\n\n/**\n * Dynamic (mutable) path used to count path lengths.\n *\n * This class is used to efficiently check paths for valid\n * length (in UTF8 bytes) and depth (used in path validation).\n *\n * Throws Error exception if path is ever invalid.\n *\n * The definition of a path always begins with '/'.\n */\nexport class ValidationPath {\n  parts_: string[];\n  /** Initialize to number of '/' chars needed in path. */\n  byteLength_: number;\n\n  /**\n   * @param path - Initial Path.\n   * @param errorPrefix_ - Prefix for any error messages.\n   */\n  constructor(path: Path, public errorPrefix_: string) {\n    this.parts_ = pathSlice(path, 0);\n    /** Initialize to number of '/' chars needed in path. */\n    this.byteLength_ = Math.max(1, this.parts_.length);\n\n    for (let i = 0; i < this.parts_.length; i++) {\n      this.byteLength_ += stringLength(this.parts_[i]);\n    }\n    validationPathCheckValid(this);\n  }\n}\n\nexport function validationPathPush(\n  validationPath: ValidationPath,\n  child: string\n): void {\n  // Count the needed '/'\n  if (validationPath.parts_.length > 0) {\n    validationPath.byteLength_ += 1;\n  }\n  validationPath.parts_.push(child);\n  validationPath.byteLength_ += stringLength(child);\n  validationPathCheckValid(validationPath);\n}\n\nexport function validationPathPop(validationPath: ValidationPath): void {\n  const last = validationPath.parts_.pop();\n  validationPath.byteLength_ -= stringLength(last);\n  // Un-count the previous '/'\n  if (validationPath.parts_.length > 0) {\n    validationPath.byteLength_ -= 1;\n  }\n}\n\nfunction validationPathCheckValid(validationPath: ValidationPath): void {\n  if (validationPath.byteLength_ > MAX_PATH_LENGTH_BYTES) {\n    throw new Error(\n      validationPath.errorPrefix_ +\n        'has a key path longer than ' +\n        MAX_PATH_LENGTH_BYTES +\n        ' bytes (' +\n        validationPath.byteLength_ +\n        ').'\n    );\n  }\n  if (validationPath.parts_.length > MAX_PATH_DEPTH) {\n    throw new Error(\n      validationPath.errorPrefix_ +\n        'path specified exceeds the maximum depth that can be written (' +\n        MAX_PATH_DEPTH +\n        ') or object contains a cycle ' +\n        validationPathToErrorString(validationPath)\n    );\n  }\n}\n\n/**\n * String for use in error messages - uses '.' notation for path.\n */\nexport function validationPathToErrorString(\n  validationPath: ValidationPath\n): string {\n  if (validationPath.parts_.length === 0) {\n    return '';\n  }\n  return \"in property '\" + validationPath.parts_.join('.') + \"'\";\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  contains,\n  errorPrefix as errorPrefixFxn,\n  safeGet,\n  stringLength\n} from '@firebase/util';\n\nimport { RepoInfo } from '../RepoInfo';\n\nimport {\n  Path,\n  pathChild,\n  pathCompare,\n  pathContains,\n  pathGetBack,\n  pathGetFront,\n  pathSlice,\n  ValidationPath,\n  validationPathPop,\n  validationPathPush,\n  validationPathToErrorString\n} from './Path';\nimport { each, isInvalidJSONNumber } from './util';\n\n/**\n * True for invalid Firebase keys\n */\nexport const INVALID_KEY_REGEX_ = /[\\[\\].#$\\/\\u0000-\\u001F\\u007F]/;\n\n/**\n * True for invalid Firebase paths.\n * Allows '/' in paths.\n */\nexport const INVALID_PATH_REGEX_ = /[\\[\\].#$\\u0000-\\u001F\\u007F]/;\n\n/**\n * Maximum number of characters to allow in leaf value\n */\nexport const MAX_LEAF_SIZE_ = 10 * 1024 * 1024;\n\nexport const isValidKey = function (key: unknown): boolean {\n  return (\n    typeof key === 'string' && key.length !== 0 && !INVALID_KEY_REGEX_.test(key)\n  );\n};\n\nexport const isValidPathString = function (pathString: string): boolean {\n  return (\n    typeof pathString === 'string' &&\n    pathString.length !== 0 &&\n    !INVALID_PATH_REGEX_.test(pathString)\n  );\n};\n\nexport const isValidRootPathString = function (pathString: string): boolean {\n  if (pathString) {\n    // Allow '/.info/' at the beginning.\n    pathString = pathString.replace(/^\\/*\\.info(\\/|$)/, '/');\n  }\n\n  return isValidPathString(pathString);\n};\n\nexport const isValidPriority = function (priority: unknown): boolean {\n  return (\n    priority === null ||\n    typeof priority === 'string' ||\n    (typeof priority === 'number' && !isInvalidJSONNumber(priority)) ||\n    (priority &&\n      typeof priority === 'object' &&\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      contains(priority as any, '.sv'))\n  );\n};\n\n/**\n * Pre-validate a datum passed as an argument to Firebase function.\n */\nexport const validateFirebaseDataArg = function (\n  fnName: string,\n  value: unknown,\n  path: Path,\n  optional: boolean\n) {\n  if (optional && value === undefined) {\n    return;\n  }\n\n  validateFirebaseData(errorPrefixFxn(fnName, 'value'), value, path);\n};\n\n/**\n * Validate a data object client-side before sending to server.\n */\nexport const validateFirebaseData = function (\n  errorPrefix: string,\n  data: unknown,\n  path_: Path | ValidationPath\n) {\n  const path =\n    path_ instanceof Path ? new ValidationPath(path_, errorPrefix) : path_;\n\n  if (data === undefined) {\n    throw new Error(\n      errorPrefix + 'contains undefined ' + validationPathToErrorString(path)\n    );\n  }\n  if (typeof data === 'function') {\n    throw new Error(\n      errorPrefix +\n        'contains a function ' +\n        validationPathToErrorString(path) +\n        ' with contents = ' +\n        data.toString()\n    );\n  }\n  if (isInvalidJSONNumber(data)) {\n    throw new Error(\n      errorPrefix +\n        'contains ' +\n        data.toString() +\n        ' ' +\n        validationPathToErrorString(path)\n    );\n  }\n\n  // Check max leaf size, but try to avoid the utf8 conversion if we can.\n  if (\n    typeof data === 'string' &&\n    data.length > MAX_LEAF_SIZE_ / 3 &&\n    stringLength(data) > MAX_LEAF_SIZE_\n  ) {\n    throw new Error(\n      errorPrefix +\n        'contains a string greater than ' +\n        MAX_LEAF_SIZE_ +\n        ' utf8 bytes ' +\n        validationPathToErrorString(path) +\n        \" ('\" +\n        data.substring(0, 50) +\n        \"...')\"\n    );\n  }\n\n  // TODO = Perf = Consider combining the recursive validation of keys into NodeFromJSON\n  // to save extra walking of large objects.\n  if (data && typeof data === 'object') {\n    let hasDotValue = false;\n    let hasActualChild = false;\n    each(data, (key: string, value: unknown) => {\n      if (key === '.value') {\n        hasDotValue = true;\n      } else if (key !== '.priority' && key !== '.sv') {\n        hasActualChild = true;\n        if (!isValidKey(key)) {\n          throw new Error(\n            errorPrefix +\n              ' contains an invalid key (' +\n              key +\n              ') ' +\n              validationPathToErrorString(path) +\n              '.  Keys must be non-empty strings ' +\n              'and can\\'t contain \".\", \"#\", \"$\", \"/\", \"[\", or \"]\"'\n          );\n        }\n      }\n\n      validationPathPush(path, key);\n      validateFirebaseData(errorPrefix, value, path);\n      validationPathPop(path);\n    });\n\n    if (hasDotValue && hasActualChild) {\n      throw new Error(\n        errorPrefix +\n          ' contains \".value\" child ' +\n          validationPathToErrorString(path) +\n          ' in addition to actual children.'\n      );\n    }\n  }\n};\n\n/**\n * Pre-validate paths passed in the firebase function.\n */\nexport const validateFirebaseMergePaths = function (\n  errorPrefix: string,\n  mergePaths: Path[]\n) {\n  let i, curPath: Path;\n  for (i = 0; i < mergePaths.length; i++) {\n    curPath = mergePaths[i];\n    const keys = pathSlice(curPath);\n    for (let j = 0; j < keys.length; j++) {\n      if (keys[j] === '.priority' && j === keys.length - 1) {\n        // .priority is OK\n      } else if (!isValidKey(keys[j])) {\n        throw new Error(\n          errorPrefix +\n            'contains an invalid key (' +\n            keys[j] +\n            ') in path ' +\n            curPath.toString() +\n            '. Keys must be non-empty strings ' +\n            'and can\\'t contain \".\", \"#\", \"$\", \"/\", \"[\", or \"]\"'\n        );\n      }\n    }\n  }\n\n  // Check that update keys are not descendants of each other.\n  // We rely on the property that sorting guarantees that ancestors come\n  // right before descendants.\n  mergePaths.sort(pathCompare);\n  let prevPath: Path | null = null;\n  for (i = 0; i < mergePaths.length; i++) {\n    curPath = mergePaths[i];\n    if (prevPath !== null && pathContains(prevPath, curPath)) {\n      throw new Error(\n        errorPrefix +\n          'contains a path ' +\n          prevPath.toString() +\n          ' that is ancestor of another path ' +\n          curPath.toString()\n      );\n    }\n    prevPath = curPath;\n  }\n};\n\n/**\n * pre-validate an object passed as an argument to firebase function (\n * must be an object - e.g. for firebase.update()).\n */\nexport const validateFirebaseMergeDataArg = function (\n  fnName: string,\n  data: unknown,\n  path: Path,\n  optional: boolean\n) {\n  if (optional && data === undefined) {\n    return;\n  }\n\n  const errorPrefix = errorPrefixFxn(fnName, 'values');\n\n  if (!(data && typeof data === 'object') || Array.isArray(data)) {\n    throw new Error(\n      errorPrefix + ' must be an object containing the children to replace.'\n    );\n  }\n\n  const mergePaths: Path[] = [];\n  each(data, (key: string, value: unknown) => {\n    const curPath = new Path(key);\n    validateFirebaseData(errorPrefix, value, pathChild(path, curPath));\n    if (pathGetBack(curPath) === '.priority') {\n      if (!isValidPriority(value)) {\n        throw new Error(\n          errorPrefix +\n            \"contains an invalid value for '\" +\n            curPath.toString() +\n            \"', which must be a valid \" +\n            'Firebase priority (a string, finite number, server value, or null).'\n        );\n      }\n    }\n    mergePaths.push(curPath);\n  });\n  validateFirebaseMergePaths(errorPrefix, mergePaths);\n};\n\nexport const validatePriority = function (\n  fnName: string,\n  priority: unknown,\n  optional: boolean\n) {\n  if (optional && priority === undefined) {\n    return;\n  }\n  if (isInvalidJSONNumber(priority)) {\n    throw new Error(\n      errorPrefixFxn(fnName, 'priority') +\n        'is ' +\n        priority.toString() +\n        ', but must be a valid Firebase priority (a string, finite number, ' +\n        'server value, or null).'\n    );\n  }\n  // Special case to allow importing data with a .sv.\n  if (!isValidPriority(priority)) {\n    throw new Error(\n      errorPrefixFxn(fnName, 'priority') +\n        'must be a valid Firebase priority ' +\n        '(a string, finite number, server value, or null).'\n    );\n  }\n};\n\nexport const validateEventType = function (\n  fnName: string,\n  eventType: string,\n  optional: boolean\n) {\n  if (optional && eventType === undefined) {\n    return;\n  }\n\n  switch (eventType) {\n    case 'value':\n    case 'child_added':\n    case 'child_removed':\n    case 'child_changed':\n    case 'child_moved':\n      break;\n    default:\n      throw new Error(\n        errorPrefixFxn(fnName, 'eventType') +\n          'must be a valid event type = \"value\", \"child_added\", \"child_removed\", ' +\n          '\"child_changed\", or \"child_moved\".'\n      );\n  }\n};\n\nexport const validateKey = function (\n  fnName: string,\n  argumentName: string,\n  key: string,\n  optional: boolean\n) {\n  if (optional && key === undefined) {\n    return;\n  }\n  if (!isValidKey(key)) {\n    throw new Error(\n      errorPrefixFxn(fnName, argumentName) +\n        'was an invalid key = \"' +\n        key +\n        '\".  Firebase keys must be non-empty strings and ' +\n        'can\\'t contain \".\", \"#\", \"$\", \"/\", \"[\", or \"]\").'\n    );\n  }\n};\n\nexport const validatePathString = function (\n  fnName: string,\n  argumentName: string,\n  pathString: string,\n  optional: boolean\n) {\n  if (optional && pathString === undefined) {\n    return;\n  }\n\n  if (!isValidPathString(pathString)) {\n    throw new Error(\n      errorPrefixFxn(fnName, argumentName) +\n        'was an invalid path = \"' +\n        pathString +\n        '\". Paths must be non-empty strings and ' +\n        'can\\'t contain \".\", \"#\", \"$\", \"[\", or \"]\"'\n    );\n  }\n};\n\nexport const validateRootPathString = function (\n  fnName: string,\n  argumentName: string,\n  pathString: string,\n  optional: boolean\n) {\n  if (pathString) {\n    // Allow '/.info/' at the beginning.\n    pathString = pathString.replace(/^\\/*\\.info(\\/|$)/, '/');\n  }\n\n  validatePathString(fnName, argumentName, pathString, optional);\n};\n\nexport const validateWritablePath = function (fnName: string, path: Path) {\n  if (pathGetFront(path) === '.info') {\n    throw new Error(fnName + \" failed = Can't modify data under /.info/\");\n  }\n};\n\nexport const validateUrl = function (\n  fnName: string,\n  parsedUrl: { repoInfo: RepoInfo; path: Path }\n) {\n  // TODO = Validate server better.\n  const pathString = parsedUrl.path.toString();\n  if (\n    !(typeof parsedUrl.repoInfo.host === 'string') ||\n    parsedUrl.repoInfo.host.length === 0 ||\n    (!isValidKey(parsedUrl.repoInfo.namespace) &&\n      parsedUrl.repoInfo.host.split(':')[0] !== 'localhost') ||\n    (pathString.length !== 0 && !isValidRootPathString(pathString))\n  ) {\n    throw new Error(\n      errorPrefixFxn(fnName, 'url') +\n        'must be a valid firebase URL and ' +\n        'the path can\\'t contain \".\", \"#\", \"$\", \"[\", or \"]\".'\n    );\n  }\n};\n\nexport const validateBoolean = function (\n  fnName: string,\n  argumentName: string,\n  bool: unknown,\n  optional: boolean\n) {\n  if (optional && bool === undefined) {\n    return;\n  }\n  if (typeof bool !== 'boolean') {\n    throw new Error(\n      errorPrefixFxn(fnName, argumentName) + 'must be a boolean.'\n    );\n  }\n};\n\nexport const validateString = function (\n  fnName: string,\n  argumentName: string,\n  string: unknown,\n  optional: boolean\n) {\n  if (optional && string === undefined) {\n    return;\n  }\n  if (!(typeof string === 'string')) {\n    throw new Error(\n      errorPrefixFxn(fnName, argumentName) + 'must be a valid string.'\n    );\n  }\n};\n\nexport const validateObject = function (\n  fnName: string,\n  argumentName: string,\n  obj: unknown,\n  optional: boolean\n) {\n  if (optional && obj === undefined) {\n    return;\n  }\n  if (!(obj && typeof obj === 'object') || obj === null) {\n    throw new Error(\n      errorPrefixFxn(fnName, argumentName) + 'must be a valid object.'\n    );\n  }\n};\n\nexport const validateObjectContainsKey = function (\n  fnName: string,\n  argumentName: string,\n  obj: unknown,\n  key: string,\n  optional: boolean,\n  optType?: string\n) {\n  const objectContainsKey =\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    obj && typeof obj === 'object' && contains(obj as any, key);\n\n  if (!objectContainsKey) {\n    if (optional) {\n      return;\n    } else {\n      throw new Error(\n        errorPrefixFxn(fnName, argumentName) +\n          'must contain the key \"' +\n          key +\n          '\"'\n      );\n    }\n  }\n\n  if (optType) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const val = safeGet(obj as any, key);\n    if (\n      (optType === 'number' && !(typeof val === 'number')) ||\n      (optType === 'string' && !(typeof val === 'string')) ||\n      (optType === 'boolean' && !(typeof val === 'boolean')) ||\n      (optType === 'function' && !(typeof val === 'function')) ||\n      (optType === 'object' && !(typeof val === 'object') && val)\n    ) {\n      if (optional) {\n        throw new Error(\n          errorPrefixFxn(fnName, argumentName) +\n            'contains invalid value for key \"' +\n            key +\n            '\" (must be of type \"' +\n            optType +\n            '\")'\n        );\n      } else {\n        throw new Error(\n          errorPrefixFxn(fnName, argumentName) +\n            'must contain the key \"' +\n            key +\n            '\" with type \"' +\n            optType +\n            '\"'\n        );\n      }\n    }\n  }\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Path } from '../util/Path';\n\nimport { Index } from './indexes/Index';\n\n/**\n * Node is an interface defining the common functionality for nodes in\n * a DataSnapshot.\n *\n * @interface\n */\nexport interface Node {\n  /**\n   * Whether this node is a leaf node.\n   * @returns Whether this is a leaf node.\n   */\n  isLeafNode(): boolean;\n\n  /**\n   * Gets the priority of the node.\n   * @returns The priority of the node.\n   */\n  getPriority(): Node;\n\n  /**\n   * Returns a duplicate node with the new priority.\n   * @param newPriorityNode - New priority to set for the node.\n   * @returns Node with new priority.\n   */\n  updatePriority(newPriorityNode: Node): Node;\n\n  /**\n   * Returns the specified immediate child, or null if it doesn't exist.\n   * @param childName - The name of the child to retrieve.\n   * @returns The retrieved child, or an empty node.\n   */\n  getImmediateChild(childName: string): Node;\n\n  /**\n   * Returns a child by path, or null if it doesn't exist.\n   * @param path - The path of the child to retrieve.\n   * @returns The retrieved child or an empty node.\n   */\n  getChild(path: Path): Node;\n\n  /**\n   * Returns the name of the child immediately prior to the specified childNode, or null.\n   * @param childName - The name of the child to find the predecessor of.\n   * @param childNode - The node to find the predecessor of.\n   * @param index - The index to use to determine the predecessor\n   * @returns The name of the predecessor child, or null if childNode is the first child.\n   */\n  getPredecessorChildName(\n    childName: string,\n    childNode: Node,\n    index: Index\n  ): string | null;\n\n  /**\n   * Returns a duplicate node, with the specified immediate child updated.\n   * Any value in the node will be removed.\n   * @param childName - The name of the child to update.\n   * @param newChildNode - The new child node\n   * @returns The updated node.\n   */\n  updateImmediateChild(childName: string, newChildNode: Node): Node;\n\n  /**\n   * Returns a duplicate node, with the specified child updated.  Any value will\n   * be removed.\n   * @param path - The path of the child to update.\n   * @param newChildNode - The new child node, which may be an empty node\n   * @returns The updated node.\n   */\n  updateChild(path: Path, newChildNode: Node): Node;\n\n  /**\n   * True if the immediate child specified exists\n   */\n  hasChild(childName: string): boolean;\n\n  /**\n   * @returns True if this node has no value or children.\n   */\n  isEmpty(): boolean;\n\n  /**\n   * @returns The number of children of this node.\n   */\n  numChildren(): number;\n\n  /**\n   * Calls action for each child.\n   * @param action - Action to be called for\n   * each child.  It's passed the child name and the child node.\n   * @returns The first truthy value return by action, or the last falsey one\n   */\n  forEachChild(index: Index, action: (a: string, b: Node) => void): unknown;\n\n  /**\n   * @param exportFormat - True for export format (also wire protocol format).\n   * @returns Value of this node as JSON.\n   */\n  val(exportFormat?: boolean): unknown;\n\n  /**\n   * @returns hash representing the node contents.\n   */\n  hash(): string;\n\n  /**\n   * @param other - Another node\n   * @returns -1 for less than, 0 for equal, 1 for greater than other\n   */\n  compareTo(other: Node): number;\n\n  /**\n   * @returns Whether or not this snapshot equals other\n   */\n  equals(other: Node): boolean;\n\n  /**\n   * @returns This node, with the specified index now available\n   */\n  withIndex(indexDefinition: Index): Node;\n\n  isIndexed(indexDefinition: Index): boolean;\n}\n\nexport class NamedNode {\n  constructor(public name: string, public node: Node) {}\n\n  static Wrap(name: string, node: Node) {\n    return new NamedNode(name, node);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Comparator } from '../../util/SortedMap';\nimport { MIN_NAME } from '../../util/util';\nimport { Node, NamedNode } from '../Node';\n\nexport abstract class Index {\n  abstract compare(a: NamedNode, b: NamedNode): number;\n\n  abstract isDefinedOn(node: Node): boolean;\n\n  /**\n   * @returns A standalone comparison function for\n   * this index\n   */\n  getCompare(): Comparator<NamedNode> {\n    return this.compare.bind(this);\n  }\n\n  /**\n   * Given a before and after value for a node, determine if the indexed value has changed. Even if they are different,\n   * it's possible that the changes are isolated to parts of the snapshot that are not indexed.\n   *\n   *\n   * @returns True if the portion of the snapshot being indexed changed between oldNode and newNode\n   */\n  indexedValueChanged(oldNode: Node, newNode: Node): boolean {\n    const oldWrapped = new NamedNode(MIN_NAME, oldNode);\n    const newWrapped = new NamedNode(MIN_NAME, newNode);\n    return this.compare(oldWrapped, newWrapped) !== 0;\n  }\n\n  /**\n   * @returns a node wrapper that will sort equal to or less than\n   * any other node wrapper, using this index\n   */\n  minPost(): NamedNode {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return (NamedNode as any).MIN;\n  }\n\n  /**\n   * @returns a node wrapper that will sort greater than or equal to\n   * any other node wrapper, using this index\n   */\n  abstract maxPost(): NamedNode;\n\n  abstract makePost(indexValue: unknown, name: string): NamedNode;\n\n  /**\n   * @returns String representation for inclusion in a query spec\n   */\n  abstract toString(): string;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert, contains } from '@firebase/util';\n\nimport { Indexable } from '../util/misc';\nimport { doubleToIEEE754String } from '../util/util';\n\nimport { Node } from './Node';\n\nlet MAX_NODE: Node;\n\nexport function setMaxNode(val: Node) {\n  MAX_NODE = val;\n}\n\nexport const priorityHashText = function (priority: string | number): string {\n  if (typeof priority === 'number') {\n    return 'number:' + doubleToIEEE754String(priority);\n  } else {\n    return 'string:' + priority;\n  }\n};\n\n/**\n * Validates that a priority snapshot Node is valid.\n */\nexport const validatePriorityNode = function (priorityNode: Node) {\n  if (priorityNode.isLeafNode()) {\n    const val = priorityNode.val();\n    assert(\n      typeof val === 'string' ||\n        typeof val === 'number' ||\n        (typeof val === 'object' && contains(val as Indexable, '.sv')),\n      'Priority must be a string or number.'\n    );\n  } else {\n    assert(\n      priorityNode === MAX_NODE || priorityNode.isEmpty(),\n      'priority of unexpected type.'\n    );\n  }\n  // Don't call getPriority() on MAX_NODE to avoid hitting assertion.\n  assert(\n    priorityNode === MAX_NODE || priorityNode.getPriority().isEmpty(),\n    \"Priority nodes can't have a priority of their own.\"\n  );\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from '@firebase/util';\n\nimport { Indexable } from '../util/misc';\nimport {\n  Path,\n  pathGetFront,\n  pathGetLength,\n  pathIsEmpty,\n  pathPopFront\n} from '../util/Path';\nimport { doubleToIEEE754String, sha1 } from '../util/util';\n\nimport { ChildrenNodeConstructor } from './ChildrenNode';\nimport { Index } from './indexes/Index';\nimport { Node } from './Node';\nimport { priorityHashText, validatePriorityNode } from './snap';\n\nlet __childrenNodeConstructor: ChildrenNodeConstructor;\n\n/**\n * LeafNode is a class for storing leaf nodes in a DataSnapshot.  It\n * implements Node and stores the value of the node (a string,\n * number, or boolean) accessible via getValue().\n */\nexport class LeafNode implements Node {\n  static set __childrenNodeConstructor(val: ChildrenNodeConstructor) {\n    __childrenNodeConstructor = val;\n  }\n\n  static get __childrenNodeConstructor() {\n    return __childrenNodeConstructor;\n  }\n\n  /**\n   * The sort order for comparing leaf nodes of different types. If two leaf nodes have\n   * the same type, the comparison falls back to their value\n   */\n  static VALUE_TYPE_ORDER = ['object', 'boolean', 'number', 'string'];\n\n  private lazyHash_: string | null = null;\n\n  /**\n   * @param value_ - The value to store in this leaf node. The object type is\n   * possible in the event of a deferred value\n   * @param priorityNode_ - The priority of this node.\n   */\n  constructor(\n    private readonly value_: string | number | boolean | Indexable,\n    private priorityNode_: Node = LeafNode.__childrenNodeConstructor.EMPTY_NODE\n  ) {\n    assert(\n      this.value_ !== undefined && this.value_ !== null,\n      \"LeafNode shouldn't be created with null/undefined value.\"\n    );\n\n    validatePriorityNode(this.priorityNode_);\n  }\n\n  /** @inheritDoc */\n  isLeafNode(): boolean {\n    return true;\n  }\n\n  /** @inheritDoc */\n  getPriority(): Node {\n    return this.priorityNode_;\n  }\n\n  /** @inheritDoc */\n  updatePriority(newPriorityNode: Node): Node {\n    return new LeafNode(this.value_, newPriorityNode);\n  }\n\n  /** @inheritDoc */\n  getImmediateChild(childName: string): Node {\n    // Hack to treat priority as a regular child\n    if (childName === '.priority') {\n      return this.priorityNode_;\n    } else {\n      return LeafNode.__childrenNodeConstructor.EMPTY_NODE;\n    }\n  }\n\n  /** @inheritDoc */\n  getChild(path: Path): Node {\n    if (pathIsEmpty(path)) {\n      return this;\n    } else if (pathGetFront(path) === '.priority') {\n      return this.priorityNode_;\n    } else {\n      return LeafNode.__childrenNodeConstructor.EMPTY_NODE;\n    }\n  }\n  hasChild(): boolean {\n    return false;\n  }\n\n  /** @inheritDoc */\n  getPredecessorChildName(childName: string, childNode: Node): null {\n    return null;\n  }\n\n  /** @inheritDoc */\n  updateImmediateChild(childName: string, newChildNode: Node): Node {\n    if (childName === '.priority') {\n      return this.updatePriority(newChildNode);\n    } else if (newChildNode.isEmpty() && childName !== '.priority') {\n      return this;\n    } else {\n      return LeafNode.__childrenNodeConstructor.EMPTY_NODE.updateImmediateChild(\n        childName,\n        newChildNode\n      ).updatePriority(this.priorityNode_);\n    }\n  }\n\n  /** @inheritDoc */\n  updateChild(path: Path, newChildNode: Node): Node {\n    const front = pathGetFront(path);\n    if (front === null) {\n      return newChildNode;\n    } else if (newChildNode.isEmpty() && front !== '.priority') {\n      return this;\n    } else {\n      assert(\n        front !== '.priority' || pathGetLength(path) === 1,\n        '.priority must be the last token in a path'\n      );\n\n      return this.updateImmediateChild(\n        front,\n        LeafNode.__childrenNodeConstructor.EMPTY_NODE.updateChild(\n          pathPopFront(path),\n          newChildNode\n        )\n      );\n    }\n  }\n\n  /** @inheritDoc */\n  isEmpty(): boolean {\n    return false;\n  }\n\n  /** @inheritDoc */\n  numChildren(): number {\n    return 0;\n  }\n\n  /** @inheritDoc */\n  forEachChild(index: Index, action: (s: string, n: Node) => void): boolean {\n    return false;\n  }\n  val(exportFormat?: boolean): {} {\n    if (exportFormat && !this.getPriority().isEmpty()) {\n      return {\n        '.value': this.getValue(),\n        '.priority': this.getPriority().val()\n      };\n    } else {\n      return this.getValue();\n    }\n  }\n\n  /** @inheritDoc */\n  hash(): string {\n    if (this.lazyHash_ === null) {\n      let toHash = '';\n      if (!this.priorityNode_.isEmpty()) {\n        toHash +=\n          'priority:' +\n          priorityHashText(this.priorityNode_.val() as number | string) +\n          ':';\n      }\n\n      const type = typeof this.value_;\n      toHash += type + ':';\n      if (type === 'number') {\n        toHash += doubleToIEEE754String(this.value_ as number);\n      } else {\n        toHash += this.value_;\n      }\n      this.lazyHash_ = sha1(toHash);\n    }\n    return this.lazyHash_;\n  }\n\n  /**\n   * Returns the value of the leaf node.\n   * @returns The value of the node.\n   */\n  getValue(): Indexable | string | number | boolean {\n    return this.value_;\n  }\n  compareTo(other: Node): number {\n    if (other === LeafNode.__childrenNodeConstructor.EMPTY_NODE) {\n      return 1;\n    } else if (other instanceof LeafNode.__childrenNodeConstructor) {\n      return -1;\n    } else {\n      assert(other.isLeafNode(), 'Unknown node type');\n      return this.compareToLeafNode_(other as LeafNode);\n    }\n  }\n\n  /**\n   * Comparison specifically for two leaf nodes\n   */\n  private compareToLeafNode_(otherLeaf: LeafNode): number {\n    const otherLeafType = typeof otherLeaf.value_;\n    const thisLeafType = typeof this.value_;\n    const otherIndex = LeafNode.VALUE_TYPE_ORDER.indexOf(otherLeafType);\n    const thisIndex = LeafNode.VALUE_TYPE_ORDER.indexOf(thisLeafType);\n    assert(otherIndex >= 0, 'Unknown leaf type: ' + otherLeafType);\n    assert(thisIndex >= 0, 'Unknown leaf type: ' + thisLeafType);\n    if (otherIndex === thisIndex) {\n      // Same type, compare values\n      if (thisLeafType === 'object') {\n        // Deferred value nodes are all equal, but we should also never get to this point...\n        return 0;\n      } else {\n        // Note that this works because true > false, all others are number or string comparisons\n        if (this.value_ < otherLeaf.value_) {\n          return -1;\n        } else if (this.value_ === otherLeaf.value_) {\n          return 0;\n        } else {\n          return 1;\n        }\n      }\n    } else {\n      return thisIndex - otherIndex;\n    }\n  }\n  withIndex(): Node {\n    return this;\n  }\n  isIndexed(): boolean {\n    return true;\n  }\n  equals(other: Node): boolean {\n    if (other === this) {\n      return true;\n    } else if (other.isLeafNode()) {\n      const otherLeaf = other as LeafNode;\n      return (\n        this.value_ === otherLeaf.value_ &&\n        this.priorityNode_.equals(otherLeaf.priorityNode_)\n      );\n    } else {\n      return false;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { nameCompare, MAX_NAME } from '../../util/util';\nimport { LeafNode } from '../LeafNode';\nimport { NamedNode, Node } from '../Node';\n\nimport { Index } from './Index';\n\nlet nodeFromJSON: (a: unknown) => Node;\nlet MAX_NODE: Node;\n\nexport function setNodeFromJSON(val: (a: unknown) => Node) {\n  nodeFromJSON = val;\n}\n\nexport function setMaxNode(val: Node) {\n  MAX_NODE = val;\n}\n\nexport class PriorityIndex extends Index {\n  compare(a: NamedNode, b: NamedNode): number {\n    const aPriority = a.node.getPriority();\n    const bPriority = b.node.getPriority();\n    const indexCmp = aPriority.compareTo(bPriority);\n    if (indexCmp === 0) {\n      return nameCompare(a.name, b.name);\n    } else {\n      return indexCmp;\n    }\n  }\n  isDefinedOn(node: Node): boolean {\n    return !node.getPriority().isEmpty();\n  }\n  indexedValueChanged(oldNode: Node, newNode: Node): boolean {\n    return !oldNode.getPriority().equals(newNode.getPriority());\n  }\n  minPost(): NamedNode {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return (NamedNode as any).MIN;\n  }\n  maxPost(): NamedNode {\n    return new NamedNode(MAX_NAME, new LeafNode('[PRIORITY-POST]', MAX_NODE));\n  }\n\n  makePost(indexValue: unknown, name: string): NamedNode {\n    const priorityNode = nodeFromJSON(indexValue);\n    return new NamedNode(name, new LeafNode('[PRIORITY-POST]', priorityNode));\n  }\n\n  /**\n   * @returns String representation for inclusion in a query spec\n   */\n  toString(): string {\n    return '.priority';\n  }\n}\n\nexport const PRIORITY_INDEX = new PriorityIndex();\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert, stringify } from '@firebase/util';\n\nimport { Index } from '../snap/indexes/Index';\nimport { KEY_INDEX } from '../snap/indexes/KeyIndex';\nimport { PathIndex } from '../snap/indexes/PathIndex';\nimport { PRIORITY_INDEX, PriorityIndex } from '../snap/indexes/PriorityIndex';\nimport { VALUE_INDEX } from '../snap/indexes/ValueIndex';\nimport { predecessor, successor } from '../util/NextPushId';\nimport { MAX_NAME, MIN_NAME } from '../util/util';\n\nimport { IndexedFilter } from './filter/IndexedFilter';\nimport { LimitedFilter } from './filter/LimitedFilter';\nimport { NodeFilter } from './filter/NodeFilter';\nimport { RangedFilter } from './filter/RangedFilter';\n\n/**\n * Wire Protocol Constants\n */\nconst enum WIRE_PROTOCOL_CONSTANTS {\n  INDEX_START_VALUE = 'sp',\n  INDEX_START_NAME = 'sn',\n  INDEX_END_VALUE = 'ep',\n  INDEX_END_NAME = 'en',\n  LIMIT = 'l',\n  VIEW_FROM = 'vf',\n  VIEW_FROM_LEFT = 'l',\n  VIEW_FROM_RIGHT = 'r',\n  INDEX = 'i'\n}\n\n/**\n * REST Query Constants\n */\nconst enum REST_QUERY_CONSTANTS {\n  ORDER_BY = 'orderBy',\n  PRIORITY_INDEX = '$priority',\n  VALUE_INDEX = '$value',\n  KEY_INDEX = '$key',\n  START_AT = 'startAt',\n  END_AT = 'endAt',\n  LIMIT_TO_FIRST = 'limitToFirst',\n  LIMIT_TO_LAST = 'limitToLast'\n}\n\n/**\n * This class is an immutable-from-the-public-api struct containing a set of query parameters defining a\n * range to be returned for a particular location. It is assumed that validation of parameters is done at the\n * user-facing API level, so it is not done here.\n */\nexport class QueryParams {\n  limitSet_ = false;\n  startSet_ = false;\n  startNameSet_ = false;\n  startAfterSet_ = false;\n  endSet_ = false;\n  endNameSet_ = false;\n  endBeforeSet_ = false;\n  limit_ = 0;\n  viewFrom_ = '';\n  indexStartValue_: unknown | null = null;\n  indexStartName_ = '';\n  indexEndValue_: unknown | null = null;\n  indexEndName_ = '';\n  index_: PriorityIndex = PRIORITY_INDEX;\n\n  hasStart(): boolean {\n    return this.startSet_;\n  }\n\n  hasStartAfter(): boolean {\n    return this.startAfterSet_;\n  }\n\n  hasEndBefore(): boolean {\n    return this.endBeforeSet_;\n  }\n\n  /**\n   * @returns True if it would return from left.\n   */\n  isViewFromLeft(): boolean {\n    if (this.viewFrom_ === '') {\n      // limit(), rather than limitToFirst or limitToLast was called.\n      // This means that only one of startSet_ and endSet_ is true. Use them\n      // to calculate which side of the view to anchor to. If neither is set,\n      // anchor to the end.\n      return this.startSet_;\n    } else {\n      return this.viewFrom_ === WIRE_PROTOCOL_CONSTANTS.VIEW_FROM_LEFT;\n    }\n  }\n\n  /**\n   * Only valid to call if hasStart() returns true\n   */\n  getIndexStartValue(): unknown {\n    assert(this.startSet_, 'Only valid if start has been set');\n    return this.indexStartValue_;\n  }\n\n  /**\n   * Only valid to call if hasStart() returns true.\n   * Returns the starting key name for the range defined by these query parameters\n   */\n  getIndexStartName(): string {\n    assert(this.startSet_, 'Only valid if start has been set');\n    if (this.startNameSet_) {\n      return this.indexStartName_;\n    } else {\n      return MIN_NAME;\n    }\n  }\n\n  hasEnd(): boolean {\n    return this.endSet_;\n  }\n\n  /**\n   * Only valid to call if hasEnd() returns true.\n   */\n  getIndexEndValue(): unknown {\n    assert(this.endSet_, 'Only valid if end has been set');\n    return this.indexEndValue_;\n  }\n\n  /**\n   * Only valid to call if hasEnd() returns true.\n   * Returns the end key name for the range defined by these query parameters\n   */\n  getIndexEndName(): string {\n    assert(this.endSet_, 'Only valid if end has been set');\n    if (this.endNameSet_) {\n      return this.indexEndName_;\n    } else {\n      return MAX_NAME;\n    }\n  }\n\n  hasLimit(): boolean {\n    return this.limitSet_;\n  }\n\n  /**\n   * @returns True if a limit has been set and it has been explicitly anchored\n   */\n  hasAnchoredLimit(): boolean {\n    return this.limitSet_ && this.viewFrom_ !== '';\n  }\n\n  /**\n   * Only valid to call if hasLimit() returns true\n   */\n  getLimit(): number {\n    assert(this.limitSet_, 'Only valid if limit has been set');\n    return this.limit_;\n  }\n\n  getIndex(): Index {\n    return this.index_;\n  }\n\n  loadsAllData(): boolean {\n    return !(this.startSet_ || this.endSet_ || this.limitSet_);\n  }\n\n  isDefault(): boolean {\n    return this.loadsAllData() && this.index_ === PRIORITY_INDEX;\n  }\n\n  copy(): QueryParams {\n    const copy = new QueryParams();\n    copy.limitSet_ = this.limitSet_;\n    copy.limit_ = this.limit_;\n    copy.startSet_ = this.startSet_;\n    copy.indexStartValue_ = this.indexStartValue_;\n    copy.startNameSet_ = this.startNameSet_;\n    copy.indexStartName_ = this.indexStartName_;\n    copy.endSet_ = this.endSet_;\n    copy.indexEndValue_ = this.indexEndValue_;\n    copy.endNameSet_ = this.endNameSet_;\n    copy.indexEndName_ = this.indexEndName_;\n    copy.index_ = this.index_;\n    copy.viewFrom_ = this.viewFrom_;\n    return copy;\n  }\n}\n\nexport function queryParamsGetNodeFilter(queryParams: QueryParams): NodeFilter {\n  if (queryParams.loadsAllData()) {\n    return new IndexedFilter(queryParams.getIndex());\n  } else if (queryParams.hasLimit()) {\n    return new LimitedFilter(queryParams);\n  } else {\n    return new RangedFilter(queryParams);\n  }\n}\n\nexport function queryParamsLimit(\n  queryParams: QueryParams,\n  newLimit: number\n): QueryParams {\n  const newParams = queryParams.copy();\n  newParams.limitSet_ = true;\n  newParams.limit_ = newLimit;\n  newParams.viewFrom_ = '';\n  return newParams;\n}\n\nexport function queryParamsLimitToFirst(\n  queryParams: QueryParams,\n  newLimit: number\n): QueryParams {\n  const newParams = queryParams.copy();\n  newParams.limitSet_ = true;\n  newParams.limit_ = newLimit;\n  newParams.viewFrom_ = WIRE_PROTOCOL_CONSTANTS.VIEW_FROM_LEFT;\n  return newParams;\n}\n\nexport function queryParamsLimitToLast(\n  queryParams: QueryParams,\n  newLimit: number\n): QueryParams {\n  const newParams = queryParams.copy();\n  newParams.limitSet_ = true;\n  newParams.limit_ = newLimit;\n  newParams.viewFrom_ = WIRE_PROTOCOL_CONSTANTS.VIEW_FROM_RIGHT;\n  return newParams;\n}\n\nexport function queryParamsStartAt(\n  queryParams: QueryParams,\n  indexValue: unknown,\n  key?: string | null\n): QueryParams {\n  const newParams = queryParams.copy();\n  newParams.startSet_ = true;\n  if (indexValue === undefined) {\n    indexValue = null;\n  }\n  newParams.indexStartValue_ = indexValue;\n  if (key != null) {\n    newParams.startNameSet_ = true;\n    newParams.indexStartName_ = key;\n  } else {\n    newParams.startNameSet_ = false;\n    newParams.indexStartName_ = '';\n  }\n  return newParams;\n}\n\nexport function queryParamsStartAfter(\n  queryParams: QueryParams,\n  indexValue: unknown,\n  key?: string | null\n): QueryParams {\n  let params: QueryParams;\n  if (queryParams.index_ === KEY_INDEX) {\n    if (typeof indexValue === 'string') {\n      indexValue = successor(indexValue as string);\n    }\n    params = queryParamsStartAt(queryParams, indexValue, key);\n  } else {\n    let childKey: string;\n    if (key == null) {\n      childKey = MAX_NAME;\n    } else {\n      childKey = successor(key);\n    }\n    params = queryParamsStartAt(queryParams, indexValue, childKey);\n  }\n  params.startAfterSet_ = true;\n  return params;\n}\n\nexport function queryParamsEndAt(\n  queryParams: QueryParams,\n  indexValue: unknown,\n  key?: string | null\n): QueryParams {\n  const newParams = queryParams.copy();\n  newParams.endSet_ = true;\n  if (indexValue === undefined) {\n    indexValue = null;\n  }\n  newParams.indexEndValue_ = indexValue;\n  if (key !== undefined) {\n    newParams.endNameSet_ = true;\n    newParams.indexEndName_ = key;\n  } else {\n    newParams.endNameSet_ = false;\n    newParams.indexEndName_ = '';\n  }\n  return newParams;\n}\n\nexport function queryParamsEndBefore(\n  queryParams: QueryParams,\n  indexValue: unknown,\n  key?: string | null\n): QueryParams {\n  let childKey: string;\n  let params: QueryParams;\n  if (queryParams.index_ === KEY_INDEX) {\n    if (typeof indexValue === 'string') {\n      indexValue = predecessor(indexValue as string);\n    }\n    params = queryParamsEndAt(queryParams, indexValue, key);\n  } else {\n    if (key == null) {\n      childKey = MIN_NAME;\n    } else {\n      childKey = predecessor(key);\n    }\n    params = queryParamsEndAt(queryParams, indexValue, childKey);\n  }\n  params.endBeforeSet_ = true;\n  return params;\n}\n\nexport function queryParamsOrderBy(\n  queryParams: QueryParams,\n  index: Index\n): QueryParams {\n  const newParams = queryParams.copy();\n  newParams.index_ = index;\n  return newParams;\n}\n\n/**\n * Returns a set of REST query string parameters representing this query.\n *\n * @returns query string parameters\n */\nexport function queryParamsToRestQueryStringParameters(\n  queryParams: QueryParams\n): Record<string, string | number> {\n  const qs: Record<string, string | number> = {};\n\n  if (queryParams.isDefault()) {\n    return qs;\n  }\n\n  let orderBy;\n  if (queryParams.index_ === PRIORITY_INDEX) {\n    orderBy = REST_QUERY_CONSTANTS.PRIORITY_INDEX;\n  } else if (queryParams.index_ === VALUE_INDEX) {\n    orderBy = REST_QUERY_CONSTANTS.VALUE_INDEX;\n  } else if (queryParams.index_ === KEY_INDEX) {\n    orderBy = REST_QUERY_CONSTANTS.KEY_INDEX;\n  } else {\n    assert(queryParams.index_ instanceof PathIndex, 'Unrecognized index type!');\n    orderBy = queryParams.index_.toString();\n  }\n  qs[REST_QUERY_CONSTANTS.ORDER_BY] = stringify(orderBy);\n\n  if (queryParams.startSet_) {\n    qs[REST_QUERY_CONSTANTS.START_AT] = stringify(queryParams.indexStartValue_);\n    if (queryParams.startNameSet_) {\n      qs[REST_QUERY_CONSTANTS.START_AT] +=\n        ',' + stringify(queryParams.indexStartName_);\n    }\n  }\n\n  if (queryParams.endSet_) {\n    qs[REST_QUERY_CONSTANTS.END_AT] = stringify(queryParams.indexEndValue_);\n    if (queryParams.endNameSet_) {\n      qs[REST_QUERY_CONSTANTS.END_AT] +=\n        ',' + stringify(queryParams.indexEndName_);\n    }\n  }\n\n  if (queryParams.limitSet_) {\n    if (queryParams.isViewFromLeft()) {\n      qs[REST_QUERY_CONSTANTS.LIMIT_TO_FIRST] = queryParams.limit_;\n    } else {\n      qs[REST_QUERY_CONSTANTS.LIMIT_TO_LAST] = queryParams.limit_;\n    }\n  }\n\n  return qs;\n}\n\nexport function queryParamsGetQueryObject(\n  queryParams: QueryParams\n): Record<string, unknown> {\n  const obj: Record<string, unknown> = {};\n  if (queryParams.startSet_) {\n    obj[WIRE_PROTOCOL_CONSTANTS.INDEX_START_VALUE] =\n      queryParams.indexStartValue_;\n    if (queryParams.startNameSet_) {\n      obj[WIRE_PROTOCOL_CONSTANTS.INDEX_START_NAME] =\n        queryParams.indexStartName_;\n    }\n  }\n  if (queryParams.endSet_) {\n    obj[WIRE_PROTOCOL_CONSTANTS.INDEX_END_VALUE] = queryParams.indexEndValue_;\n    if (queryParams.endNameSet_) {\n      obj[WIRE_PROTOCOL_CONSTANTS.INDEX_END_NAME] = queryParams.indexEndName_;\n    }\n  }\n  if (queryParams.limitSet_) {\n    obj[WIRE_PROTOCOL_CONSTANTS.LIMIT] = queryParams.limit_;\n    let viewFrom = queryParams.viewFrom_;\n    if (viewFrom === '') {\n      if (queryParams.isViewFromLeft()) {\n        viewFrom = WIRE_PROTOCOL_CONSTANTS.VIEW_FROM_LEFT;\n      } else {\n        viewFrom = WIRE_PROTOCOL_CONSTANTS.VIEW_FROM_RIGHT;\n      }\n    }\n    obj[WIRE_PROTOCOL_CONSTANTS.VIEW_FROM] = viewFrom;\n  }\n  // For now, priority index is the default, so we only specify if it's some other index\n  if (queryParams.index_ !== PRIORITY_INDEX) {\n    obj[WIRE_PROTOCOL_CONSTANTS.INDEX] = queryParams.index_.toString();\n  }\n  return obj;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { validateArgCount, validateCallback, Compat } from '@firebase/util';\n\nimport { Indexable } from '../core/util/misc';\nimport { warn } from '../core/util/util';\n\n// TODO: revert to import { OnDisconnect as ExpOnDisconnect } from '../../exp/index'; once the modular SDK goes GA\n/**\n * This is a workaround for an issue in the no-modular '@firebase/database' where its typings\n * reference types from `@firebase/app-exp`.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype ExpOnDisconnect = any;\n\nexport class OnDisconnect implements Compat<ExpOnDisconnect> {\n  constructor(readonly _delegate: ExpOnDisconnect) {}\n\n  cancel(onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('OnDisconnect.cancel', 0, 1, arguments.length);\n    validateCallback('OnDisconnect.cancel', 'onComplete', onComplete, true);\n    const result = this._delegate.cancel();\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  remove(onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('OnDisconnect.remove', 0, 1, arguments.length);\n    validateCallback('OnDisconnect.remove', 'onComplete', onComplete, true);\n    const result = this._delegate.remove();\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  set(value: unknown, onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('OnDisconnect.set', 1, 2, arguments.length);\n    validateCallback('OnDisconnect.set', 'onComplete', onComplete, true);\n    const result = this._delegate.set(value);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  setWithPriority(\n    value: unknown,\n    priority: number | string | null,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('OnDisconnect.setWithPriority', 2, 3, arguments.length);\n    validateCallback(\n      'OnDisconnect.setWithPriority',\n      'onComplete',\n      onComplete,\n      true\n    );\n    const result = this._delegate.setWithPriority(value, priority);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  update(\n    objectToMerge: Indexable,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('OnDisconnect.update', 1, 2, arguments.length);\n    if (Array.isArray(objectToMerge)) {\n      const newObjectToMerge: { [k: string]: unknown } = {};\n      for (let i = 0; i < objectToMerge.length; ++i) {\n        newObjectToMerge['' + i] = objectToMerge[i];\n      }\n      objectToMerge = newObjectToMerge;\n      warn(\n        'Passing an Array to firebase.database.onDisconnect().update() is deprecated. Use set() if you want to overwrite the ' +\n          'existing data, or an Object with integer keys if you really do want to only update some of the children.'\n      );\n    }\n    validateCallback('OnDisconnect.update', 'onComplete', onComplete, true);\n    const result = this._delegate.update(objectToMerge);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { validateArgCount } from '@firebase/util';\n\nimport { DataSnapshot } from './Reference';\n\nexport class TransactionResult {\n  /**\n   * A type for the resolve value of Firebase.transaction.\n   */\n  constructor(public committed: boolean, public snapshot: DataSnapshot) {}\n\n  // Do not create public documentation. This is intended to make JSON serialization work but is otherwise unnecessary\n  // for end-users\n  toJSON(): object {\n    validateArgCount('TransactionResult.toJSON', 0, 1, arguments.length);\n    return { committed: this.committed, snapshot: this.snapshot.toJSON() };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Compat,\n  Deferred,\n  errorPrefix,\n  validateArgCount,\n  validateCallback,\n  validateContextObject\n} from '@firebase/util';\n\nimport {\n  OnDisconnect as ExpOnDisconnect,\n  off,\n  onChildAdded,\n  onChildChanged,\n  onChildMoved,\n  onChildRemoved,\n  onValue,\n  EventType,\n  limitToFirst,\n  query,\n  limitToLast,\n  orderByChild,\n  orderByKey,\n  orderByValue,\n  orderByPriority,\n  startAt,\n  startAfter,\n  endAt,\n  endBefore,\n  equalTo,\n  get,\n  set,\n  update,\n  setWithPriority,\n  remove,\n  setPriority,\n  push,\n  runTransaction,\n  _QueryImpl,\n  _ReferenceImpl,\n  child\n} from '../../exp/index'; // import from the exp public API\nimport { warn } from '../core/util/util';\nimport {\n  validateBoolean,\n  validateEventType,\n  validatePathString,\n  validateWritablePath\n} from '../core/util/validation';\nimport { UserCallback } from '../core/view/EventRegistration';\nimport { QueryParams } from '../core/view/QueryParams';\nimport { ThenableReferenceImpl } from '../exp/Reference_impl';\n\nimport { Database } from './Database';\nimport { OnDisconnect } from './onDisconnect';\nimport { TransactionResult } from './TransactionResult';\n\n// TODO: revert to import {  DataSnapshot as ExpDataSnapshot, Query as ExpQuery,\n// Reference as ExpReference,} from '../../exp/index'; once the modular SDK goes GA\n/**\n * This is part of a workaround for an issue in the no-modular '@firebase/database' where its typings\n * reference types from `@firebase/app-exp`.\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\ntype ExpDataSnapshot = any;\ntype ExpQuery = any;\ntype ExpReference = any;\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/**\n * Class representing a firebase data snapshot.  It wraps a SnapshotNode and\n * surfaces the public methods (val, forEach, etc.) we want to expose.\n */\nexport class DataSnapshot implements Compat<ExpDataSnapshot> {\n  constructor(\n    readonly _database: Database,\n    readonly _delegate: ExpDataSnapshot\n  ) {}\n\n  /**\n   * Retrieves the snapshot contents as JSON.  Returns null if the snapshot is\n   * empty.\n   *\n   * @returns JSON representation of the DataSnapshot contents, or null if empty.\n   */\n  val(): unknown {\n    validateArgCount('DataSnapshot.val', 0, 0, arguments.length);\n    return this._delegate.val();\n  }\n\n  /**\n   * Returns the snapshot contents as JSON, including priorities of node.  Suitable for exporting\n   * the entire node contents.\n   * @returns JSON representation of the DataSnapshot contents, or null if empty.\n   */\n  exportVal(): unknown {\n    validateArgCount('DataSnapshot.exportVal', 0, 0, arguments.length);\n    return this._delegate.exportVal();\n  }\n\n  // Do not create public documentation. This is intended to make JSON serialization work but is otherwise unnecessary\n  // for end-users\n  toJSON(): unknown {\n    // Optional spacer argument is unnecessary because we're depending on recursion rather than stringifying the content\n    validateArgCount('DataSnapshot.toJSON', 0, 1, arguments.length);\n    return this._delegate.toJSON();\n  }\n\n  /**\n   * Returns whether the snapshot contains a non-null value.\n   *\n   * @returns Whether the snapshot contains a non-null value, or is empty.\n   */\n  exists(): boolean {\n    validateArgCount('DataSnapshot.exists', 0, 0, arguments.length);\n    return this._delegate.exists();\n  }\n\n  /**\n   * Returns a DataSnapshot of the specified child node's contents.\n   *\n   * @param path - Path to a child.\n   * @returns DataSnapshot for child node.\n   */\n  child(path: string): DataSnapshot {\n    validateArgCount('DataSnapshot.child', 0, 1, arguments.length);\n    // Ensure the childPath is a string (can be a number)\n    path = String(path);\n    validatePathString('DataSnapshot.child', 'path', path, false);\n    return new DataSnapshot(this._database, this._delegate.child(path));\n  }\n\n  /**\n   * Returns whether the snapshot contains a child at the specified path.\n   *\n   * @param path - Path to a child.\n   * @returns Whether the child exists.\n   */\n  hasChild(path: string): boolean {\n    validateArgCount('DataSnapshot.hasChild', 1, 1, arguments.length);\n    validatePathString('DataSnapshot.hasChild', 'path', path, false);\n    return this._delegate.hasChild(path);\n  }\n\n  /**\n   * Returns the priority of the object, or null if no priority was set.\n   *\n   * @returns The priority.\n   */\n  getPriority(): string | number | null {\n    validateArgCount('DataSnapshot.getPriority', 0, 0, arguments.length);\n    return this._delegate.priority;\n  }\n\n  /**\n   * Iterates through child nodes and calls the specified action for each one.\n   *\n   * @param action - Callback function to be called\n   * for each child.\n   * @returns True if forEach was canceled by action returning true for\n   * one of the child nodes.\n   */\n  forEach(action: (snapshot: DataSnapshot) => boolean | void): boolean {\n    validateArgCount('DataSnapshot.forEach', 1, 1, arguments.length);\n    validateCallback('DataSnapshot.forEach', 'action', action, false);\n    return this._delegate.forEach(expDataSnapshot =>\n      action(new DataSnapshot(this._database, expDataSnapshot))\n    );\n  }\n\n  /**\n   * Returns whether this DataSnapshot has children.\n   * @returns True if the DataSnapshot contains 1 or more child nodes.\n   */\n  hasChildren(): boolean {\n    validateArgCount('DataSnapshot.hasChildren', 0, 0, arguments.length);\n    return this._delegate.hasChildren();\n  }\n\n  get key() {\n    return this._delegate.key;\n  }\n\n  /**\n   * Returns the number of children for this DataSnapshot.\n   * @returns The number of children that this DataSnapshot contains.\n   */\n  numChildren(): number {\n    validateArgCount('DataSnapshot.numChildren', 0, 0, arguments.length);\n    return this._delegate.size;\n  }\n\n  /**\n   * @returns The Firebase reference for the location this snapshot's data came\n   * from.\n   */\n  getRef(): Reference {\n    validateArgCount('DataSnapshot.ref', 0, 0, arguments.length);\n    return new Reference(this._database, this._delegate.ref);\n  }\n\n  get ref(): Reference {\n    return this.getRef();\n  }\n}\n\nexport interface SnapshotCallback {\n  (dataSnapshot: DataSnapshot, previousChildName?: string | null): unknown;\n}\n\n/**\n * A Query represents a filter to be applied to a firebase location.  This object purely represents the\n * query expression (and exposes our public API to build the query).  The actual query logic is in ViewBase.js.\n *\n * Since every Firebase reference is a query, Firebase inherits from this object.\n */\nexport class Query implements Compat<ExpQuery> {\n  constructor(readonly database: Database, readonly _delegate: ExpQuery) {}\n\n  on(\n    eventType: string,\n    callback: SnapshotCallback,\n    cancelCallbackOrContext?: ((a: Error) => unknown) | object | null,\n    context?: object | null\n  ): SnapshotCallback {\n    validateArgCount('Query.on', 2, 4, arguments.length);\n    validateCallback('Query.on', 'callback', callback, false);\n\n    const ret = Query.getCancelAndContextArgs_(\n      'Query.on',\n      cancelCallbackOrContext,\n      context\n    );\n    const valueCallback: UserCallback = (expSnapshot, previousChildName?) => {\n      callback.call(\n        ret.context,\n        new DataSnapshot(this.database, expSnapshot),\n        previousChildName\n      );\n    };\n    valueCallback.userCallback = callback;\n    valueCallback.context = ret.context;\n    const cancelCallback = ret.cancel?.bind(ret.context);\n\n    switch (eventType) {\n      case 'value':\n        onValue(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_added':\n        onChildAdded(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_removed':\n        onChildRemoved(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_changed':\n        onChildChanged(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_moved':\n        onChildMoved(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      default:\n        throw new Error(\n          errorPrefix('Query.on', 'eventType') +\n            'must be a valid event type = \"value\", \"child_added\", \"child_removed\", ' +\n            '\"child_changed\", or \"child_moved\".'\n        );\n    }\n  }\n\n  off(\n    eventType?: string,\n    callback?: SnapshotCallback,\n    context?: object | null\n  ): void {\n    validateArgCount('Query.off', 0, 3, arguments.length);\n    validateEventType('Query.off', eventType, true);\n    validateCallback('Query.off', 'callback', callback, true);\n    validateContextObject('Query.off', 'context', context, true);\n    if (callback) {\n      const valueCallback: UserCallback = () => {};\n      valueCallback.userCallback = callback;\n      valueCallback.context = context;\n      off(this._delegate, eventType as EventType, valueCallback);\n    } else {\n      off(this._delegate, eventType as EventType | undefined);\n    }\n  }\n\n  /**\n   * Get the server-value for this query, or return a cached value if not connected.\n   */\n  get(): Promise<DataSnapshot> {\n    return get(this._delegate).then(expSnapshot => {\n      return new DataSnapshot(this.database, expSnapshot);\n    });\n  }\n\n  /**\n   * Attaches a listener, waits for the first event, and then removes the listener\n   */\n  once(\n    eventType: string,\n    callback?: SnapshotCallback,\n    failureCallbackOrContext?: ((a: Error) => void) | object | null,\n    context?: object | null\n  ): Promise<DataSnapshot> {\n    validateArgCount('Query.once', 1, 4, arguments.length);\n    validateCallback('Query.once', 'callback', callback, true);\n\n    const ret = Query.getCancelAndContextArgs_(\n      'Query.once',\n      failureCallbackOrContext,\n      context\n    );\n    const deferred = new Deferred<DataSnapshot>();\n    const valueCallback: UserCallback = (expSnapshot, previousChildName?) => {\n      const result = new DataSnapshot(this.database, expSnapshot);\n      if (callback) {\n        callback.call(ret.context, result, previousChildName);\n      }\n      deferred.resolve(result);\n    };\n    valueCallback.userCallback = callback;\n    valueCallback.context = ret.context;\n    const cancelCallback = (error: Error) => {\n      if (ret.cancel) {\n        ret.cancel.call(ret.context, error);\n      }\n      deferred.reject(error);\n    };\n\n    switch (eventType) {\n      case 'value':\n        onValue(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_added':\n        onChildAdded(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_removed':\n        onChildRemoved(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_changed':\n        onChildChanged(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_moved':\n        onChildMoved(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      default:\n        throw new Error(\n          errorPrefix('Query.once', 'eventType') +\n            'must be a valid event type = \"value\", \"child_added\", \"child_removed\", ' +\n            '\"child_changed\", or \"child_moved\".'\n        );\n    }\n\n    return deferred.promise;\n  }\n\n  /**\n   * Set a limit and anchor it to the start of the window.\n   */\n  limitToFirst(limit: number): Query {\n    validateArgCount('Query.limitToFirst', 1, 1, arguments.length);\n    return new Query(this.database, query(this._delegate, limitToFirst(limit)));\n  }\n\n  /**\n   * Set a limit and anchor it to the end of the window.\n   */\n  limitToLast(limit: number): Query {\n    validateArgCount('Query.limitToLast', 1, 1, arguments.length);\n    return new Query(this.database, query(this._delegate, limitToLast(limit)));\n  }\n\n  /**\n   * Given a child path, return a new query ordered by the specified grandchild path.\n   */\n  orderByChild(path: string): Query {\n    validateArgCount('Query.orderByChild', 1, 1, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByChild(path)));\n  }\n\n  /**\n   * Return a new query ordered by the KeyIndex\n   */\n  orderByKey(): Query {\n    validateArgCount('Query.orderByKey', 0, 0, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByKey()));\n  }\n\n  /**\n   * Return a new query ordered by the PriorityIndex\n   */\n  orderByPriority(): Query {\n    validateArgCount('Query.orderByPriority', 0, 0, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByPriority()));\n  }\n\n  /**\n   * Return a new query ordered by the ValueIndex\n   */\n  orderByValue(): Query {\n    validateArgCount('Query.orderByValue', 0, 0, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByValue()));\n  }\n\n  startAt(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.startAt', 0, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, startAt(value, name))\n    );\n  }\n\n  startAfter(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.startAfter', 0, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, startAfter(value, name))\n    );\n  }\n\n  endAt(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.endAt', 0, 2, arguments.length);\n    return new Query(this.database, query(this._delegate, endAt(value, name)));\n  }\n\n  endBefore(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.endBefore', 0, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, endBefore(value, name))\n    );\n  }\n\n  /**\n   * Load the selection of children with exactly the specified value, and, optionally,\n   * the specified name.\n   */\n  equalTo(value: number | string | boolean | null, name?: string) {\n    validateArgCount('Query.equalTo', 1, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, equalTo(value, name))\n    );\n  }\n\n  /**\n   * @returns URL for this location.\n   */\n  toString(): string {\n    validateArgCount('Query.toString', 0, 0, arguments.length);\n    return this._delegate.toString();\n  }\n\n  // Do not create public documentation. This is intended to make JSON serialization work but is otherwise unnecessary\n  // for end-users.\n  toJSON() {\n    // An optional spacer argument is unnecessary for a string.\n    validateArgCount('Query.toJSON', 0, 1, arguments.length);\n    return this._delegate.toJSON();\n  }\n\n  /**\n   * Return true if this query and the provided query are equivalent; otherwise, return false.\n   */\n  isEqual(other: Query): boolean {\n    validateArgCount('Query.isEqual', 1, 1, arguments.length);\n    if (!(other instanceof Query)) {\n      const error =\n        'Query.isEqual failed: First argument must be an instance of firebase.database.Query.';\n      throw new Error(error);\n    }\n    return this._delegate.isEqual(other._delegate);\n  }\n\n  /**\n   * Helper used by .on and .once to extract the context and or cancel arguments.\n   * @param fnName - The function name (on or once)\n   *\n   */\n  private static getCancelAndContextArgs_(\n    fnName: string,\n    cancelOrContext?: ((a: Error) => void) | object | null,\n    context?: object | null\n  ): { cancel: ((a: Error) => void) | undefined; context: object | undefined } {\n    const ret: {\n      cancel: ((a: Error) => void) | null;\n      context: object | null;\n    } = { cancel: undefined, context: undefined };\n    if (cancelOrContext && context) {\n      ret.cancel = cancelOrContext as (a: Error) => void;\n      validateCallback(fnName, 'cancel', ret.cancel, true);\n\n      ret.context = context;\n      validateContextObject(fnName, 'context', ret.context, true);\n    } else if (cancelOrContext) {\n      // we have either a cancel callback or a context.\n      if (typeof cancelOrContext === 'object' && cancelOrContext !== null) {\n        // it's a context!\n        ret.context = cancelOrContext;\n      } else if (typeof cancelOrContext === 'function') {\n        ret.cancel = cancelOrContext as (a: Error) => void;\n      } else {\n        throw new Error(\n          errorPrefix(fnName, 'cancelOrContext') +\n            ' must either be a cancel callback or a context object.'\n        );\n      }\n    }\n    return ret;\n  }\n\n  get ref(): Reference {\n    return new Reference(\n      this.database,\n      new _ReferenceImpl(this._delegate._repo, this._delegate._path)\n    );\n  }\n}\n\nexport class Reference extends Query implements Compat<ExpReference> {\n  then: Promise<Reference>['then'];\n  catch: Promise<Reference>['catch'];\n\n  /**\n   * Call options:\n   *   new Reference(Repo, Path) or\n   *   new Reference(url: string, string|RepoManager)\n   *\n   * Externally - this is the firebase.database.Reference type.\n   */\n  constructor(readonly database: Database, readonly _delegate: ExpReference) {\n    super(\n      database,\n      new _QueryImpl(_delegate._repo, _delegate._path, new QueryParams(), false)\n    );\n  }\n\n  /** @returns {?string} */\n  getKey(): string | null {\n    validateArgCount('Reference.key', 0, 0, arguments.length);\n    return this._delegate.key;\n  }\n\n  child(pathString: string): Reference {\n    validateArgCount('Reference.child', 1, 1, arguments.length);\n    if (typeof pathString === 'number') {\n      pathString = String(pathString);\n    }\n    return new Reference(this.database, child(this._delegate, pathString));\n  }\n\n  /** @returns {?Reference} */\n  getParent(): Reference | null {\n    validateArgCount('Reference.parent', 0, 0, arguments.length);\n    const parent = this._delegate.parent;\n    return parent ? new Reference(this.database, parent) : null;\n  }\n\n  /** @returns {!Reference} */\n  getRoot(): Reference {\n    validateArgCount('Reference.root', 0, 0, arguments.length);\n    return new Reference(this.database, this._delegate.root);\n  }\n\n  set(\n    newVal: unknown,\n    onComplete?: (error: Error | null) => void\n  ): Promise<unknown> {\n    validateArgCount('Reference.set', 1, 2, arguments.length);\n    validateCallback('Reference.set', 'onComplete', onComplete, true);\n    const result = set(this._delegate, newVal);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  update(\n    values: object,\n    onComplete?: (a: Error | null) => void\n  ): Promise<unknown> {\n    validateArgCount('Reference.update', 1, 2, arguments.length);\n\n    if (Array.isArray(values)) {\n      const newObjectToMerge: { [k: string]: unknown } = {};\n      for (let i = 0; i < values.length; ++i) {\n        newObjectToMerge['' + i] = values[i];\n      }\n      values = newObjectToMerge;\n      warn(\n        'Passing an Array to Firebase.update() is deprecated. ' +\n          'Use set() if you want to overwrite the existing data, or ' +\n          'an Object with integer keys if you really do want to ' +\n          'only update some of the children.'\n      );\n    }\n    validateWritablePath('Reference.update', this._delegate._path);\n    validateCallback('Reference.update', 'onComplete', onComplete, true);\n\n    const result = update(this._delegate, values);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  setWithPriority(\n    newVal: unknown,\n    newPriority: string | number | null,\n    onComplete?: (a: Error | null) => void\n  ): Promise<unknown> {\n    validateArgCount('Reference.setWithPriority', 2, 3, arguments.length);\n    validateCallback(\n      'Reference.setWithPriority',\n      'onComplete',\n      onComplete,\n      true\n    );\n\n    const result = setWithPriority(this._delegate, newVal, newPriority);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  remove(onComplete?: (a: Error | null) => void): Promise<unknown> {\n    validateArgCount('Reference.remove', 0, 1, arguments.length);\n    validateCallback('Reference.remove', 'onComplete', onComplete, true);\n\n    const result = remove(this._delegate);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  transaction(\n    transactionUpdate: (currentData: unknown) => unknown,\n    onComplete?: (\n      error: Error | null,\n      committed: boolean,\n      dataSnapshot: DataSnapshot | null\n    ) => void,\n    applyLocally?: boolean\n  ): Promise<TransactionResult> {\n    validateArgCount('Reference.transaction', 1, 3, arguments.length);\n    validateCallback(\n      'Reference.transaction',\n      'transactionUpdate',\n      transactionUpdate,\n      false\n    );\n    validateCallback('Reference.transaction', 'onComplete', onComplete, true);\n    validateBoolean(\n      'Reference.transaction',\n      'applyLocally',\n      applyLocally,\n      true\n    );\n\n    const result = runTransaction(this._delegate, transactionUpdate, {\n      applyLocally\n    }).then(\n      transactionResult =>\n        new TransactionResult(\n          transactionResult.committed,\n          new DataSnapshot(this.database, transactionResult.snapshot)\n        )\n    );\n    if (onComplete) {\n      result.then(\n        transactionResult =>\n          onComplete(\n            null,\n            transactionResult.committed,\n            transactionResult.snapshot\n          ),\n        error => onComplete(error, false, null)\n      );\n    }\n    return result;\n  }\n\n  setPriority(\n    priority: string | number | null,\n    onComplete?: (a: Error | null) => void\n  ): Promise<unknown> {\n    validateArgCount('Reference.setPriority', 1, 2, arguments.length);\n    validateCallback('Reference.setPriority', 'onComplete', onComplete, true);\n\n    const result = setPriority(this._delegate, priority);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  push(value?: unknown, onComplete?: (a: Error | null) => void): Reference {\n    validateArgCount('Reference.push', 0, 2, arguments.length);\n    validateCallback('Reference.push', 'onComplete', onComplete, true);\n\n    const expPromise = push(this._delegate, value) as ThenableReferenceImpl;\n    const promise = expPromise.then(\n      expRef => new Reference(this.database, expRef)\n    );\n\n    if (onComplete) {\n      promise.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n\n    const result = new Reference(this.database, expPromise);\n    result.then = promise.then.bind(promise);\n    result.catch = promise.catch.bind(promise, undefined);\n    return result;\n  }\n\n  onDisconnect(): OnDisconnect {\n    validateWritablePath('Reference.onDisconnect', this._delegate._path);\n    return new OnDisconnect(\n      new ExpOnDisconnect(this._delegate._repo, this._delegate._path)\n    );\n  }\n\n  get key(): string | null {\n    return this.getKey();\n  }\n\n  get parent(): Reference | null {\n    return this.getParent();\n  }\n\n  get root(): Reference {\n    return this.getRoot();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\n\nimport { FirebaseApp } from '@firebase/app-types';\nimport { FirebaseService } from '@firebase/app-types/private';\nimport {\n  validateArgCount,\n  Compat,\n  EmulatorMockTokenOptions\n} from '@firebase/util';\n\nimport {\n  goOnline,\n  connectDatabaseEmulator,\n  goOffline,\n  ref,\n  refFromURL,\n  increment,\n  serverTimestamp\n} from '../../exp/index'; // import from the exp public API\n\nimport { Reference } from './Reference';\n\n// TODO: revert to import {FirebaseDatabase as ExpDatabase} from '@firebase/database' once modular SDK goes GA\n/**\n * This is a workaround for an issue in the no-modular '@firebase/database' where its typings\n * reference types from `@firebase/app-exp`.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype ExpDatabase = any;\n\n/**\n * Class representing a firebase database.\n */\nexport class Database implements FirebaseService, Compat<ExpDatabase> {\n  static readonly ServerValue = {\n    TIMESTAMP: serverTimestamp(),\n    increment: (delta: number) => increment(delta)\n  };\n\n  /**\n   * The constructor should not be called by users of our public API.\n   */\n  constructor(readonly _delegate: ExpDatabase, readonly app: FirebaseApp) {}\n\n  INTERNAL = {\n    delete: () => this._delegate._delete()\n  };\n\n  /**\n   * Modify this instance to communicate with the Realtime Database emulator.\n   *\n   * <p>Note: This method must be called before performing any other operation.\n   *\n   * @param host - the emulator host (ex: localhost)\n   * @param port - the emulator port (ex: 8080)\n   * @param options.mockUserToken - the mock auth token to use for unit testing Security Rules\n   */\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions;\n    } = {}\n  ): void {\n    connectDatabaseEmulator(this._delegate, host, port, options);\n  }\n\n  /**\n   * Returns a reference to the root or to the path specified in the provided\n   * argument.\n   *\n   * @param path - The relative string path or an existing Reference to a database\n   * location.\n   * @throws If a Reference is provided, throws if it does not belong to the\n   * same project.\n   * @returns Firebase reference.\n   */\n  ref(path?: string): Reference;\n  ref(path?: Reference): Reference;\n  ref(path?: string | Reference): Reference {\n    validateArgCount('database.ref', 0, 1, arguments.length);\n    if (path instanceof Reference) {\n      const childRef = refFromURL(this._delegate, path.toString());\n      return new Reference(this, childRef);\n    } else {\n      const childRef = ref(this._delegate, path);\n      return new Reference(this, childRef);\n    }\n  }\n\n  /**\n   * Returns a reference to the root or the path specified in url.\n   * We throw a exception if the url is not in the same domain as the\n   * current repo.\n   * @returns Firebase reference.\n   */\n  refFromURL(url: string): Reference {\n    const apiName = 'database.refFromURL';\n    validateArgCount(apiName, 1, 1, arguments.length);\n    const childRef = refFromURL(this._delegate, url);\n    return new Reference(this, childRef);\n  }\n\n  // Make individual repo go offline.\n  goOffline(): void {\n    validateArgCount('database.goOffline', 0, 0, arguments.length);\n    return goOffline(this._delegate);\n  }\n\n  goOnline(): void {\n    validateArgCount('database.goOnline', 0, 0, arguments.length);\n    return goOnline(this._delegate);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const PROTOCOL_VERSION = '5';\n\nexport const VERSION_PARAM = 'v';\n\nexport const TRANSPORT_SESSION_PARAM = 's';\n\nexport const REFERER_PARAM = 'r';\n\nexport const FORGE_REF = 'f';\n\n// Matches console.firebase.google.com, firebase-console-*.corp.google.com and\n// firebase.corp.google.com\nexport const FORGE_DOMAIN_RE = /(console\\.firebase|firebase-console-\\w+\\.corp|firebase\\.corp)\\.google\\.com/;\n\nexport const LAST_SESSION_PARAM = 'ls';\n\nexport const APPLICATION_ID_PARAM = 'p';\n\nexport const APP_CHECK_TOKEN_PARAM = 'ac';\n\nexport const WEBSOCKET = 'websocket';\n\nexport const LONG_POLLING = 'long_polling';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from '@firebase/util';\n\nimport { LONG_POLLING, WEBSOCKET } from '../realtime/Constants';\n\nimport { PersistentStorage } from './storage/storage';\nimport { each } from './util/util';\n\n/**\n * A class that holds metadata about a Repo object\n */\nexport class RepoInfo {\n  private _host: string;\n  private _domain: string;\n  internalHost: string;\n\n  /**\n   * @param host - Hostname portion of the url for the repo\n   * @param secure - Whether or not this repo is accessed over ssl\n   * @param namespace - The namespace represented by the repo\n   * @param webSocketOnly - Whether to prefer websockets over all other transports (used by Nest).\n   * @param nodeAdmin - Whether this instance uses Admin SDK credentials\n   * @param persistenceKey - Override the default session persistence storage key\n   */\n  constructor(\n    host: string,\n    public readonly secure: boolean,\n    public readonly namespace: string,\n    public readonly webSocketOnly: boolean,\n    public readonly nodeAdmin: boolean = false,\n    public readonly persistenceKey: string = '',\n    public readonly includeNamespaceInQueryParams: boolean = false\n  ) {\n    this._host = host.toLowerCase();\n    this._domain = this._host.substr(this._host.indexOf('.') + 1);\n    this.internalHost =\n      (PersistentStorage.get('host:' + host) as string) || this._host;\n  }\n\n  isCacheableHost(): boolean {\n    return this.internalHost.substr(0, 2) === 's-';\n  }\n\n  isCustomHost() {\n    return (\n      this._domain !== 'firebaseio.com' &&\n      this._domain !== 'firebaseio-demo.com'\n    );\n  }\n\n  get host() {\n    return this._host;\n  }\n\n  set host(newHost: string) {\n    if (newHost !== this.internalHost) {\n      this.internalHost = newHost;\n      if (this.isCacheableHost()) {\n        PersistentStorage.set('host:' + this._host, this.internalHost);\n      }\n    }\n  }\n\n  toString(): string {\n    let str = this.toURLString();\n    if (this.persistenceKey) {\n      str += '<' + this.persistenceKey + '>';\n    }\n    return str;\n  }\n\n  toURLString(): string {\n    const protocol = this.secure ? 'https://' : 'http://';\n    const query = this.includeNamespaceInQueryParams\n      ? `?ns=${this.namespace}`\n      : '';\n    return `${protocol}${this.host}/${query}`;\n  }\n}\n\nfunction repoInfoNeedsQueryParam(repoInfo: RepoInfo): boolean {\n  return (\n    repoInfo.host !== repoInfo.internalHost ||\n    repoInfo.isCustomHost() ||\n    repoInfo.includeNamespaceInQueryParams\n  );\n}\n\n/**\n * Returns the websocket URL for this repo\n * @param repoInfo - RepoInfo object\n * @param type - of connection\n * @param params - list\n * @returns The URL for this repo\n */\nexport function repoInfoConnectionURL(\n  repoInfo: RepoInfo,\n  type: string,\n  params: { [k: string]: string }\n): string {\n  assert(typeof type === 'string', 'typeof type must == string');\n  assert(typeof params === 'object', 'typeof params must == object');\n\n  let connURL: string;\n  if (type === WEBSOCKET) {\n    connURL =\n      (repoInfo.secure ? 'wss://' : 'ws://') + repoInfo.internalHost + '/.ws?';\n  } else if (type === LONG_POLLING) {\n    connURL =\n      (repoInfo.secure ? 'https://' : 'http://') +\n      repoInfo.internalHost +\n      '/.lp?';\n  } else {\n    throw new Error('Unknown connection type: ' + type);\n  }\n  if (repoInfoNeedsQueryParam(repoInfo)) {\n    params['ns'] = repoInfo.namespace;\n  }\n\n  const pairs: string[] = [];\n\n  each(params, (key: string, value: string) => {\n    pairs.push(key + '=' + value);\n  });\n\n  return connURL + pairs.join('&');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deepCopy, contains } from '@firebase/util';\n\n/**\n * Tracks a collection of stats.\n */\nexport class StatsCollection {\n  private counters_: { [k: string]: number } = {};\n\n  incrementCounter(name: string, amount: number = 1) {\n    if (!contains(this.counters_, name)) {\n      this.counters_[name] = 0;\n    }\n\n    this.counters_[name] += amount;\n  }\n\n  get() {\n    return deepCopy(this.counters_);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RepoInfo } from '../RepoInfo';\n\nimport { StatsCollection } from './StatsCollection';\n\nconst collections: { [k: string]: StatsCollection } = {};\nconst reporters: { [k: string]: unknown } = {};\n\nexport function statsManagerGetCollection(repoInfo: RepoInfo): StatsCollection {\n  const hashString = repoInfo.toString();\n\n  if (!collections[hashString]) {\n    collections[hashString] = new StatsCollection();\n  }\n\n  return collections[hashString];\n}\n\nexport function statsManagerGetOrCreateReporter<T>(\n  repoInfo: RepoInfo,\n  creatorFunction: () => T\n): T {\n  const hashString = repoInfo.toString();\n\n  if (!reporters[hashString]) {\n    reporters[hashString] = creatorFunction();\n  }\n\n  return reporters[hashString] as T;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { exceptionGuard } from '../../core/util/util';\n\n/**\n * This class ensures the packets from the server arrive in order\n * This class takes data from the server and ensures it gets passed into the callbacks in order.\n */\nexport class PacketReceiver {\n  pendingResponses: unknown[] = [];\n  currentResponseNum = 0;\n  closeAfterResponse = -1;\n  onClose: (() => void) | null = null;\n\n  /**\n   * @param onMessage_\n   */\n  constructor(private onMessage_: (a: {}) => void) {}\n\n  closeAfter(responseNum: number, callback: () => void) {\n    this.closeAfterResponse = responseNum;\n    this.onClose = callback;\n    if (this.closeAfterResponse < this.currentResponseNum) {\n      this.onClose();\n      this.onClose = null;\n    }\n  }\n\n  /**\n   * Each message from the server comes with a response number, and an array of data. The responseNumber\n   * allows us to ensure that we process them in the right order, since we can't be guaranteed that all\n   * browsers will respond in the same order as the requests we sent\n   */\n  handleResponse(requestNum: number, data: unknown[]) {\n    this.pendingResponses[requestNum] = data;\n    while (this.pendingResponses[this.currentResponseNum]) {\n      const toProcess = this.pendingResponses[\n        this.currentResponseNum\n      ] as unknown[];\n      delete this.pendingResponses[this.currentResponseNum];\n      for (let i = 0; i < toProcess.length; ++i) {\n        if (toProcess[i]) {\n          exceptionGuard(() => {\n            this.onMessage_(toProcess[i]);\n          });\n        }\n      }\n      if (this.currentResponseNum === this.closeAfterResponse) {\n        if (this.onClose) {\n          this.onClose();\n          this.onClose = null;\n        }\n        break;\n      }\n      this.currentResponseNum++;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Encode, isNodeSdk, stringify } from '@firebase/util';\n\nimport { RepoInfo, repoInfoConnectionURL } from '../core/RepoInfo';\nimport { StatsCollection } from '../core/stats/StatsCollection';\nimport { statsManagerGetCollection } from '../core/stats/StatsManager';\nimport {\n  executeWhenDOMReady,\n  isChromeExtensionContentScript,\n  isWindowsStoreApp,\n  log,\n  logWrapper,\n  LUIDGenerator,\n  splitStringBySize\n} from '../core/util/util';\n\nimport {\n  APP_CHECK_TOKEN_PARAM,\n  APPLICATION_ID_PARAM,\n  FORGE_DOMAIN_RE,\n  FORGE_REF,\n  LAST_SESSION_PARAM,\n  LONG_POLLING,\n  PROTOCOL_VERSION,\n  REFERER_PARAM,\n  TRANSPORT_SESSION_PARAM,\n  VERSION_PARAM\n} from './Constants';\nimport { PacketReceiver } from './polling/PacketReceiver';\nimport { Transport } from './Transport';\n\n// URL query parameters associated with longpolling\nexport const FIREBASE_LONGPOLL_START_PARAM = 'start';\nexport const FIREBASE_LONGPOLL_CLOSE_COMMAND = 'close';\nexport const FIREBASE_LONGPOLL_COMMAND_CB_NAME = 'pLPCommand';\nexport const FIREBASE_LONGPOLL_DATA_CB_NAME = 'pRTLPCB';\nexport const FIREBASE_LONGPOLL_ID_PARAM = 'id';\nexport const FIREBASE_LONGPOLL_PW_PARAM = 'pw';\nexport const FIREBASE_LONGPOLL_SERIAL_PARAM = 'ser';\nexport const FIREBASE_LONGPOLL_CALLBACK_ID_PARAM = 'cb';\nexport const FIREBASE_LONGPOLL_SEGMENT_NUM_PARAM = 'seg';\nexport const FIREBASE_LONGPOLL_SEGMENTS_IN_PACKET = 'ts';\nexport const FIREBASE_LONGPOLL_DATA_PARAM = 'd';\nexport const FIREBASE_LONGPOLL_DISCONN_FRAME_PARAM = 'disconn';\nexport const FIREBASE_LONGPOLL_DISCONN_FRAME_REQUEST_PARAM = 'dframe';\n\n//Data size constants.\n//TODO: Perf: the maximum length actually differs from browser to browser.\n// We should check what browser we're on and set accordingly.\nconst MAX_URL_DATA_SIZE = 1870;\nconst SEG_HEADER_SIZE = 30; //ie: &seg=8299234&ts=982389123&d=\nconst MAX_PAYLOAD_SIZE = MAX_URL_DATA_SIZE - SEG_HEADER_SIZE;\n\n/**\n * Keepalive period\n * send a fresh request at minimum every 25 seconds. Opera has a maximum request\n * length of 30 seconds that we can't exceed.\n */\nconst KEEPALIVE_REQUEST_INTERVAL = 25000;\n\n/**\n * How long to wait before aborting a long-polling connection attempt.\n */\nconst LP_CONNECT_TIMEOUT = 30000;\n\n/**\n * This class manages a single long-polling connection.\n */\nexport class BrowserPollConnection implements Transport {\n  bytesSent = 0;\n  bytesReceived = 0;\n  urlFn: (params: object) => string;\n  scriptTagHolder: FirebaseIFrameScriptHolder;\n  myDisconnFrame: HTMLIFrameElement;\n  curSegmentNum: number;\n  myPacketOrderer: PacketReceiver;\n  id: string;\n  password: string;\n  private log_: (...a: unknown[]) => void;\n  private stats_: StatsCollection;\n  private everConnected_ = false;\n  private isClosed_: boolean;\n  private connectTimeoutTimer_: number | null;\n  private onDisconnect_: ((a?: boolean) => void) | null;\n\n  /**\n   * @param connId An identifier for this connection, used for logging\n   * @param repoInfo The info for the endpoint to send data to.\n   * @param applicationId The Firebase App ID for this project.\n   * @param appCheckToken The AppCheck token for this client.\n   * @param authToken The AuthToken to use for this connection.\n   * @param transportSessionId Optional transportSessionid if we are\n   * reconnecting for an existing transport session\n   * @param lastSessionId Optional lastSessionId if the PersistentConnection has\n   * already created a connection previously\n   */\n  constructor(\n    public connId: string,\n    public repoInfo: RepoInfo,\n    private applicationId?: string,\n    private appCheckToken?: string,\n    private authToken?: string,\n    public transportSessionId?: string,\n    public lastSessionId?: string\n  ) {\n    this.log_ = logWrapper(connId);\n    this.stats_ = statsManagerGetCollection(repoInfo);\n    this.urlFn = (params: { [k: string]: string }) => {\n      // Always add the token if we have one.\n      if (this.appCheckToken) {\n        params[APP_CHECK_TOKEN_PARAM] = this.appCheckToken;\n      }\n      return repoInfoConnectionURL(repoInfo, LONG_POLLING, params);\n    };\n  }\n\n  /**\n   * @param onMessage - Callback when messages arrive\n   * @param onDisconnect - Callback with connection lost.\n   */\n  open(onMessage: (msg: {}) => void, onDisconnect: (a?: boolean) => void) {\n    this.curSegmentNum = 0;\n    this.onDisconnect_ = onDisconnect;\n    this.myPacketOrderer = new PacketReceiver(onMessage);\n    this.isClosed_ = false;\n\n    this.connectTimeoutTimer_ = setTimeout(() => {\n      this.log_('Timed out trying to connect.');\n      // Make sure we clear the host cache\n      this.onClosed_();\n      this.connectTimeoutTimer_ = null;\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    }, Math.floor(LP_CONNECT_TIMEOUT)) as any;\n\n    // Ensure we delay the creation of the iframe until the DOM is loaded.\n    executeWhenDOMReady(() => {\n      if (this.isClosed_) {\n        return;\n      }\n\n      //Set up a callback that gets triggered once a connection is set up.\n      this.scriptTagHolder = new FirebaseIFrameScriptHolder(\n        (...args) => {\n          const [command, arg1, arg2, arg3, arg4] = args;\n          this.incrementIncomingBytes_(args);\n          if (!this.scriptTagHolder) {\n            return; // we closed the connection.\n          }\n\n          if (this.connectTimeoutTimer_) {\n            clearTimeout(this.connectTimeoutTimer_);\n            this.connectTimeoutTimer_ = null;\n          }\n          this.everConnected_ = true;\n          if (command === FIREBASE_LONGPOLL_START_PARAM) {\n            this.id = arg1 as string;\n            this.password = arg2 as string;\n          } else if (command === FIREBASE_LONGPOLL_CLOSE_COMMAND) {\n            // Don't clear the host cache. We got a response from the server, so we know it's reachable\n            if (arg1) {\n              // We aren't expecting any more data (other than what the server's already in the process of sending us\n              // through our already open polls), so don't send any more.\n              this.scriptTagHolder.sendNewPolls = false;\n\n              // arg1 in this case is the last response number sent by the server. We should try to receive\n              // all of the responses up to this one before closing\n              this.myPacketOrderer.closeAfter(arg1 as number, () => {\n                this.onClosed_();\n              });\n            } else {\n              this.onClosed_();\n            }\n          } else {\n            throw new Error('Unrecognized command received: ' + command);\n          }\n        },\n        (...args) => {\n          const [pN, data] = args;\n          this.incrementIncomingBytes_(args);\n          this.myPacketOrderer.handleResponse(pN as number, data as unknown[]);\n        },\n        () => {\n          this.onClosed_();\n        },\n        this.urlFn\n      );\n\n      //Send the initial request to connect. The serial number is simply to keep the browser from pulling previous results\n      //from cache.\n      const urlParams: { [k: string]: string | number } = {};\n      urlParams[FIREBASE_LONGPOLL_START_PARAM] = 't';\n      urlParams[FIREBASE_LONGPOLL_SERIAL_PARAM] = Math.floor(\n        Math.random() * 100000000\n      );\n      if (this.scriptTagHolder.uniqueCallbackIdentifier) {\n        urlParams[\n          FIREBASE_LONGPOLL_CALLBACK_ID_PARAM\n        ] = this.scriptTagHolder.uniqueCallbackIdentifier;\n      }\n      urlParams[VERSION_PARAM] = PROTOCOL_VERSION;\n      if (this.transportSessionId) {\n        urlParams[TRANSPORT_SESSION_PARAM] = this.transportSessionId;\n      }\n      if (this.lastSessionId) {\n        urlParams[LAST_SESSION_PARAM] = this.lastSessionId;\n      }\n      if (this.applicationId) {\n        urlParams[APPLICATION_ID_PARAM] = this.applicationId;\n      }\n      if (this.appCheckToken) {\n        urlParams[APP_CHECK_TOKEN_PARAM] = this.appCheckToken;\n      }\n      if (\n        typeof location !== 'undefined' &&\n        location.hostname &&\n        FORGE_DOMAIN_RE.test(location.hostname)\n      ) {\n        urlParams[REFERER_PARAM] = FORGE_REF;\n      }\n      const connectURL = this.urlFn(urlParams);\n      this.log_('Connecting via long-poll to ' + connectURL);\n      this.scriptTagHolder.addTag(connectURL, () => {\n        /* do nothing */\n      });\n    });\n  }\n\n  /**\n   * Call this when a handshake has completed successfully and we want to consider the connection established\n   */\n  start() {\n    this.scriptTagHolder.startLongPoll(this.id, this.password);\n    this.addDisconnectPingFrame(this.id, this.password);\n  }\n\n  private static forceAllow_: boolean;\n\n  /**\n   * Forces long polling to be considered as a potential transport\n   */\n  static forceAllow() {\n    BrowserPollConnection.forceAllow_ = true;\n  }\n\n  private static forceDisallow_: boolean;\n\n  /**\n   * Forces longpolling to not be considered as a potential transport\n   */\n  static forceDisallow() {\n    BrowserPollConnection.forceDisallow_ = true;\n  }\n\n  // Static method, use string literal so it can be accessed in a generic way\n  static isAvailable() {\n    if (isNodeSdk()) {\n      return false;\n    } else if (BrowserPollConnection.forceAllow_) {\n      return true;\n    } else {\n      // NOTE: In React-Native there's normally no 'document', but if you debug a React-Native app in\n      // the Chrome debugger, 'document' is defined, but document.createElement is null (2015/06/08).\n      return (\n        !BrowserPollConnection.forceDisallow_ &&\n        typeof document !== 'undefined' &&\n        document.createElement != null &&\n        !isChromeExtensionContentScript() &&\n        !isWindowsStoreApp()\n      );\n    }\n  }\n\n  /**\n   * No-op for polling\n   */\n  markConnectionHealthy() {}\n\n  /**\n   * Stops polling and cleans up the iframe\n   */\n  private shutdown_() {\n    this.isClosed_ = true;\n\n    if (this.scriptTagHolder) {\n      this.scriptTagHolder.close();\n      this.scriptTagHolder = null;\n    }\n\n    //remove the disconnect frame, which will trigger an XHR call to the server to tell it we're leaving.\n    if (this.myDisconnFrame) {\n      document.body.removeChild(this.myDisconnFrame);\n      this.myDisconnFrame = null;\n    }\n\n    if (this.connectTimeoutTimer_) {\n      clearTimeout(this.connectTimeoutTimer_);\n      this.connectTimeoutTimer_ = null;\n    }\n  }\n\n  /**\n   * Triggered when this transport is closed\n   */\n  private onClosed_() {\n    if (!this.isClosed_) {\n      this.log_('Longpoll is closing itself');\n      this.shutdown_();\n\n      if (this.onDisconnect_) {\n        this.onDisconnect_(this.everConnected_);\n        this.onDisconnect_ = null;\n      }\n    }\n  }\n\n  /**\n   * External-facing close handler. RealTime has requested we shut down. Kill our connection and tell the server\n   * that we've left.\n   */\n  close() {\n    if (!this.isClosed_) {\n      this.log_('Longpoll is being closed.');\n      this.shutdown_();\n    }\n  }\n\n  /**\n   * Send the JSON object down to the server. It will need to be stringified, base64 encoded, and then\n   * broken into chunks (since URLs have a small maximum length).\n   * @param data - The JSON data to transmit.\n   */\n  send(data: {}) {\n    const dataStr = stringify(data);\n    this.bytesSent += dataStr.length;\n    this.stats_.incrementCounter('bytes_sent', dataStr.length);\n\n    //first, lets get the base64-encoded data\n    const base64data = base64Encode(dataStr);\n\n    //We can only fit a certain amount in each URL, so we need to split this request\n    //up into multiple pieces if it doesn't fit in one request.\n    const dataSegs = splitStringBySize(base64data, MAX_PAYLOAD_SIZE);\n\n    //Enqueue each segment for transmission. We assign each chunk a sequential ID and a total number\n    //of segments so that we can reassemble the packet on the server.\n    for (let i = 0; i < dataSegs.length; i++) {\n      this.scriptTagHolder.enqueueSegment(\n        this.curSegmentNum,\n        dataSegs.length,\n        dataSegs[i]\n      );\n      this.curSegmentNum++;\n    }\n  }\n\n  /**\n   * This is how we notify the server that we're leaving.\n   * We aren't able to send requests with DHTML on a window close event, but we can\n   * trigger XHR requests in some browsers (everything but Opera basically).\n   */\n  addDisconnectPingFrame(id: string, pw: string) {\n    if (isNodeSdk()) {\n      return;\n    }\n    this.myDisconnFrame = document.createElement('iframe');\n    const urlParams: { [k: string]: string } = {};\n    urlParams[FIREBASE_LONGPOLL_DISCONN_FRAME_REQUEST_PARAM] = 't';\n    urlParams[FIREBASE_LONGPOLL_ID_PARAM] = id;\n    urlParams[FIREBASE_LONGPOLL_PW_PARAM] = pw;\n    this.myDisconnFrame.src = this.urlFn(urlParams);\n    this.myDisconnFrame.style.display = 'none';\n\n    document.body.appendChild(this.myDisconnFrame);\n  }\n\n  /**\n   * Used to track the bytes received by this client\n   */\n  private incrementIncomingBytes_(args: unknown) {\n    // TODO: This is an annoying perf hit just to track the number of incoming bytes.  Maybe it should be opt-in.\n    const bytesReceived = stringify(args).length;\n    this.bytesReceived += bytesReceived;\n    this.stats_.incrementCounter('bytes_received', bytesReceived);\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport interface IFrameElement extends HTMLIFrameElement {\n  doc: Document;\n}\n\n/*********************************************************************************************\n * A wrapper around an iframe that is used as a long-polling script holder.\n *********************************************************************************************/\nexport class FirebaseIFrameScriptHolder {\n  //We maintain a count of all of the outstanding requests, because if we have too many active at once it can cause\n  //problems in some browsers.\n  outstandingRequests = new Set<number>();\n\n  //A queue of the pending segments waiting for transmission to the server.\n  pendingSegs: Array<{ seg: number; ts: number; d: unknown }> = [];\n\n  //A serial number. We use this for two things:\n  // 1) A way to ensure the browser doesn't cache responses to polls\n  // 2) A way to make the server aware when long-polls arrive in a different order than we started them. The\n  //    server needs to release both polls in this case or it will cause problems in Opera since Opera can only execute\n  //    JSONP code in the order it was added to the iframe.\n  currentSerial = Math.floor(Math.random() * 100000000);\n\n  // This gets set to false when we're \"closing down\" the connection (e.g. we're switching transports but there's still\n  // incoming data from the server that we're waiting for).\n  sendNewPolls = true;\n\n  uniqueCallbackIdentifier: number;\n  myIFrame: IFrameElement;\n  alive: boolean;\n  myID: string;\n  myPW: string;\n  commandCB: (command: string, ...args: unknown[]) => void;\n  onMessageCB: (...args: unknown[]) => void;\n\n  /**\n   * @param commandCB - The callback to be called when control commands are recevied from the server.\n   * @param onMessageCB - The callback to be triggered when responses arrive from the server.\n   * @param onDisconnect - The callback to be triggered when this tag holder is closed\n   * @param urlFn - A function that provides the URL of the endpoint to send data to.\n   */\n  constructor(\n    commandCB: (command: string, ...args: unknown[]) => void,\n    onMessageCB: (...args: unknown[]) => void,\n    public onDisconnect: () => void,\n    public urlFn: (a: object) => string\n  ) {\n    if (!isNodeSdk()) {\n      //Each script holder registers a couple of uniquely named callbacks with the window. These are called from the\n      //iframes where we put the long-polling script tags. We have two callbacks:\n      //   1) Command Callback - Triggered for control issues, like starting a connection.\n      //   2) Message Callback - Triggered when new data arrives.\n      this.uniqueCallbackIdentifier = LUIDGenerator();\n      window[\n        FIREBASE_LONGPOLL_COMMAND_CB_NAME + this.uniqueCallbackIdentifier\n      ] = commandCB;\n      window[\n        FIREBASE_LONGPOLL_DATA_CB_NAME + this.uniqueCallbackIdentifier\n      ] = onMessageCB;\n\n      //Create an iframe for us to add script tags to.\n      this.myIFrame = FirebaseIFrameScriptHolder.createIFrame_();\n\n      // Set the iframe's contents.\n      let script = '';\n      // if we set a javascript url, it's IE and we need to set the document domain. The javascript url is sufficient\n      // for ie9, but ie8 needs to do it again in the document itself.\n      if (\n        this.myIFrame.src &&\n        this.myIFrame.src.substr(0, 'javascript:'.length) === 'javascript:'\n      ) {\n        const currentDomain = document.domain;\n        script = '<script>document.domain=\"' + currentDomain + '\";</script>';\n      }\n      const iframeContents = '<html><body>' + script + '</body></html>';\n      try {\n        this.myIFrame.doc.open();\n        this.myIFrame.doc.write(iframeContents);\n        this.myIFrame.doc.close();\n      } catch (e) {\n        log('frame writing exception');\n        if (e.stack) {\n          log(e.stack);\n        }\n        log(e);\n      }\n    } else {\n      this.commandCB = commandCB;\n      this.onMessageCB = onMessageCB;\n    }\n  }\n\n  /**\n   * Each browser has its own funny way to handle iframes. Here we mush them all together into one object that I can\n   * actually use.\n   */\n  private static createIFrame_(): IFrameElement {\n    const iframe = document.createElement('iframe') as IFrameElement;\n    iframe.style.display = 'none';\n\n    // This is necessary in order to initialize the document inside the iframe\n    if (document.body) {\n      document.body.appendChild(iframe);\n      try {\n        // If document.domain has been modified in IE, this will throw an error, and we need to set the\n        // domain of the iframe's document manually. We can do this via a javascript: url as the src attribute\n        // Also note that we must do this *after* the iframe has been appended to the page. Otherwise it doesn't work.\n        const a = iframe.contentWindow.document;\n        if (!a) {\n          // Apologies for the log-spam, I need to do something to keep closure from optimizing out the assignment above.\n          log('No IE domain setting required');\n        }\n      } catch (e) {\n        const domain = document.domain;\n        iframe.src =\n          \"javascript:void((function(){document.open();document.domain='\" +\n          domain +\n          \"';document.close();})())\";\n      }\n    } else {\n      // LongPollConnection attempts to delay initialization until the document is ready, so hopefully this\n      // never gets hit.\n      throw 'Document body has not initialized. Wait to initialize Firebase until after the document is ready.';\n    }\n\n    // Get the document of the iframe in a browser-specific way.\n    if (iframe.contentDocument) {\n      iframe.doc = iframe.contentDocument; // Firefox, Opera, Safari\n    } else if (iframe.contentWindow) {\n      iframe.doc = iframe.contentWindow.document; // Internet Explorer\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    } else if ((iframe as any).document) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      iframe.doc = (iframe as any).document; //others?\n    }\n\n    return iframe;\n  }\n\n  /**\n   * Cancel all outstanding queries and remove the frame.\n   */\n  close() {\n    //Mark this iframe as dead, so no new requests are sent.\n    this.alive = false;\n\n    if (this.myIFrame) {\n      //We have to actually remove all of the html inside this iframe before removing it from the\n      //window, or IE will continue loading and executing the script tags we've already added, which\n      //can lead to some errors being thrown. Setting innerHTML seems to be the easiest way to do this.\n      this.myIFrame.doc.body.innerHTML = '';\n      setTimeout(() => {\n        if (this.myIFrame !== null) {\n          document.body.removeChild(this.myIFrame);\n          this.myIFrame = null;\n        }\n      }, Math.floor(0));\n    }\n\n    // Protect from being called recursively.\n    const onDisconnect = this.onDisconnect;\n    if (onDisconnect) {\n      this.onDisconnect = null;\n      onDisconnect();\n    }\n  }\n\n  /**\n   * Actually start the long-polling session by adding the first script tag(s) to the iframe.\n   * @param id - The ID of this connection\n   * @param pw - The password for this connection\n   */\n  startLongPoll(id: string, pw: string) {\n    this.myID = id;\n    this.myPW = pw;\n    this.alive = true;\n\n    //send the initial request. If there are requests queued, make sure that we transmit as many as we are currently able to.\n    while (this.newRequest_()) {}\n  }\n\n  /**\n   * This is called any time someone might want a script tag to be added. It adds a script tag when there aren't\n   * too many outstanding requests and we are still alive.\n   *\n   * If there are outstanding packet segments to send, it sends one. If there aren't, it sends a long-poll anyways if\n   * needed.\n   */\n  private newRequest_() {\n    // We keep one outstanding request open all the time to receive data, but if we need to send data\n    // (pendingSegs.length > 0) then we create a new request to send the data.  The server will automatically\n    // close the old request.\n    if (\n      this.alive &&\n      this.sendNewPolls &&\n      this.outstandingRequests.size < (this.pendingSegs.length > 0 ? 2 : 1)\n    ) {\n      //construct our url\n      this.currentSerial++;\n      const urlParams: { [k: string]: string | number } = {};\n      urlParams[FIREBASE_LONGPOLL_ID_PARAM] = this.myID;\n      urlParams[FIREBASE_LONGPOLL_PW_PARAM] = this.myPW;\n      urlParams[FIREBASE_LONGPOLL_SERIAL_PARAM] = this.currentSerial;\n      let theURL = this.urlFn(urlParams);\n      //Now add as much data as we can.\n      let curDataString = '';\n      let i = 0;\n\n      while (this.pendingSegs.length > 0) {\n        //first, lets see if the next segment will fit.\n        const nextSeg = this.pendingSegs[0];\n        if (\n          (nextSeg.d as unknown[]).length +\n            SEG_HEADER_SIZE +\n            curDataString.length <=\n          MAX_URL_DATA_SIZE\n        ) {\n          //great, the segment will fit. Lets append it.\n          const theSeg = this.pendingSegs.shift();\n          curDataString =\n            curDataString +\n            '&' +\n            FIREBASE_LONGPOLL_SEGMENT_NUM_PARAM +\n            i +\n            '=' +\n            theSeg.seg +\n            '&' +\n            FIREBASE_LONGPOLL_SEGMENTS_IN_PACKET +\n            i +\n            '=' +\n            theSeg.ts +\n            '&' +\n            FIREBASE_LONGPOLL_DATA_PARAM +\n            i +\n            '=' +\n            theSeg.d;\n          i++;\n        } else {\n          break;\n        }\n      }\n\n      theURL = theURL + curDataString;\n      this.addLongPollTag_(theURL, this.currentSerial);\n\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /**\n   * Queue a packet for transmission to the server.\n   * @param segnum - A sequential id for this packet segment used for reassembly\n   * @param totalsegs - The total number of segments in this packet\n   * @param data - The data for this segment.\n   */\n  enqueueSegment(segnum: number, totalsegs: number, data: unknown) {\n    //add this to the queue of segments to send.\n    this.pendingSegs.push({ seg: segnum, ts: totalsegs, d: data });\n\n    //send the data immediately if there isn't already data being transmitted, unless\n    //startLongPoll hasn't been called yet.\n    if (this.alive) {\n      this.newRequest_();\n    }\n  }\n\n  /**\n   * Add a script tag for a regular long-poll request.\n   * @param url - The URL of the script tag.\n   * @param serial - The serial number of the request.\n   */\n  private addLongPollTag_(url: string, serial: number) {\n    //remember that we sent this request.\n    this.outstandingRequests.add(serial);\n\n    const doNewRequest = () => {\n      this.outstandingRequests.delete(serial);\n      this.newRequest_();\n    };\n\n    // If this request doesn't return on its own accord (by the server sending us some data), we'll\n    // create a new one after the KEEPALIVE interval to make sure we always keep a fresh request open.\n    const keepaliveTimeout = setTimeout(\n      doNewRequest,\n      Math.floor(KEEPALIVE_REQUEST_INTERVAL)\n    );\n\n    const readyStateCB = () => {\n      // Request completed.  Cancel the keepalive.\n      clearTimeout(keepaliveTimeout);\n\n      // Trigger a new request so we can continue receiving data.\n      doNewRequest();\n    };\n\n    this.addTag(url, readyStateCB);\n  }\n\n  /**\n   * Add an arbitrary script tag to the iframe.\n   * @param url - The URL for the script tag source.\n   * @param loadCB - A callback to be triggered once the script has loaded.\n   */\n  addTag(url: string, loadCB: () => void) {\n    if (isNodeSdk()) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (this as any).doNodeLongPoll(url, loadCB);\n    } else {\n      setTimeout(() => {\n        try {\n          // if we're already closed, don't add this poll\n          if (!this.sendNewPolls) {\n            return;\n          }\n          const newScript = this.myIFrame.doc.createElement('script');\n          newScript.type = 'text/javascript';\n          newScript.async = true;\n          newScript.src = url;\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          newScript.onload = (newScript as any).onreadystatechange = function () {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const rstate = (newScript as any).readyState;\n            if (!rstate || rstate === 'loaded' || rstate === 'complete') {\n              // eslint-disable-next-line @typescript-eslint/no-explicit-any\n              newScript.onload = (newScript as any).onreadystatechange = null;\n              if (newScript.parentNode) {\n                newScript.parentNode.removeChild(newScript);\n              }\n              loadCB();\n            }\n          };\n          newScript.onerror = () => {\n            log('Long-poll script failed to load: ' + url);\n            this.sendNewPolls = false;\n            this.close();\n          };\n          this.myIFrame.doc.body.appendChild(newScript);\n        } catch (e) {\n          // TODO: we should make this error visible somehow\n        }\n      }, Math.floor(1));\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** The semver (www.semver.org) version of the SDK. */\nexport let SDK_VERSION = '';\n\n// SDK_VERSION should be set before any database instance is created\nexport function setSDKVersion(version: string): void {\n  SDK_VERSION = version;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert, isNodeSdk, jsonEval, stringify } from '@firebase/util';\n\nimport { RepoInfo, repoInfoConnectionURL } from '../core/RepoInfo';\nimport { StatsCollection } from '../core/stats/StatsCollection';\nimport { statsManagerGetCollection } from '../core/stats/StatsManager';\nimport { PersistentStorage } from '../core/storage/storage';\nimport { logWrapper, splitStringBySize } from '../core/util/util';\nimport { SDK_VERSION } from '../core/version';\n\nimport {\n  APP_CHECK_TOKEN_PARAM,\n  FORGE_DOMAIN_RE,\n  FORGE_REF,\n  LAST_SESSION_PARAM,\n  PROTOCOL_VERSION,\n  REFERER_PARAM,\n  TRANSPORT_SESSION_PARAM,\n  VERSION_PARAM,\n  WEBSOCKET\n} from './Constants';\nimport { Transport } from './Transport';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ndeclare const MozWebSocket: any;\n\nconst WEBSOCKET_MAX_FRAME_SIZE = 16384;\nconst WEBSOCKET_KEEPALIVE_INTERVAL = 45000;\n\nlet WebSocketImpl = null;\nif (typeof MozWebSocket !== 'undefined') {\n  WebSocketImpl = MozWebSocket;\n} else if (typeof WebSocket !== 'undefined') {\n  WebSocketImpl = WebSocket;\n}\n\nexport function setWebSocketImpl(impl) {\n  WebSocketImpl = impl;\n}\n\n/**\n * Create a new websocket connection with the given callbacks.\n */\nexport class WebSocketConnection implements Transport {\n  keepaliveTimer: number | null = null;\n  frames: string[] | null = null;\n  totalFrames = 0;\n  bytesSent = 0;\n  bytesReceived = 0;\n  connURL: string;\n  onDisconnect: (a?: boolean) => void;\n  onMessage: (msg: {}) => void;\n  mySock: WebSocket | null;\n  private log_: (...a: unknown[]) => void;\n  private stats_: StatsCollection;\n  private everConnected_: boolean;\n  private isClosed_: boolean;\n  private nodeAdmin: boolean;\n\n  /**\n   * @param connId identifier for this transport\n   * @param repoInfo The info for the websocket endpoint.\n   * @param applicationId The Firebase App ID for this project.\n   * @param appCheckToken The App Check Token for this client.\n   * @param authToken The Auth Token for this client.\n   * @param transportSessionId Optional transportSessionId if this is connecting\n   * to an existing transport session\n   * @param lastSessionId Optional lastSessionId if there was a previous\n   * connection\n   */\n  constructor(\n    public connId: string,\n    repoInfo: RepoInfo,\n    private applicationId?: string,\n    private appCheckToken?: string,\n    private authToken?: string,\n    transportSessionId?: string,\n    lastSessionId?: string\n  ) {\n    this.log_ = logWrapper(this.connId);\n    this.stats_ = statsManagerGetCollection(repoInfo);\n    this.connURL = WebSocketConnection.connectionURL_(\n      repoInfo,\n      transportSessionId,\n      lastSessionId,\n      appCheckToken\n    );\n    this.nodeAdmin = repoInfo.nodeAdmin;\n  }\n\n  /**\n   * @param repoInfo - The info for the websocket endpoint.\n   * @param transportSessionId - Optional transportSessionId if this is connecting to an existing transport\n   *                                         session\n   * @param lastSessionId - Optional lastSessionId if there was a previous connection\n   * @returns connection url\n   */\n  private static connectionURL_(\n    repoInfo: RepoInfo,\n    transportSessionId?: string,\n    lastSessionId?: string,\n    appCheckToken?: string\n  ): string {\n    const urlParams: { [k: string]: string } = {};\n    urlParams[VERSION_PARAM] = PROTOCOL_VERSION;\n\n    if (\n      !isNodeSdk() &&\n      typeof location !== 'undefined' &&\n      location.hostname &&\n      FORGE_DOMAIN_RE.test(location.hostname)\n    ) {\n      urlParams[REFERER_PARAM] = FORGE_REF;\n    }\n    if (transportSessionId) {\n      urlParams[TRANSPORT_SESSION_PARAM] = transportSessionId;\n    }\n    if (lastSessionId) {\n      urlParams[LAST_SESSION_PARAM] = lastSessionId;\n    }\n    if (appCheckToken) {\n      urlParams[APP_CHECK_TOKEN_PARAM] = appCheckToken;\n    }\n\n    return repoInfoConnectionURL(repoInfo, WEBSOCKET, urlParams);\n  }\n\n  /**\n   * @param onMessage - Callback when messages arrive\n   * @param onDisconnect - Callback with connection lost.\n   */\n  open(onMessage: (msg: {}) => void, onDisconnect: (a?: boolean) => void) {\n    this.onDisconnect = onDisconnect;\n    this.onMessage = onMessage;\n\n    this.log_('Websocket connecting to ' + this.connURL);\n\n    this.everConnected_ = false;\n    // Assume failure until proven otherwise.\n    PersistentStorage.set('previous_websocket_failure', true);\n\n    try {\n      if (isNodeSdk()) {\n        const device = this.nodeAdmin ? 'AdminNode' : 'Node';\n        // UA Format: Firebase/<wire_protocol>/<sdk_version>/<platform>/<device>\n        const options: { [k: string]: object } = {\n          headers: {\n            'User-Agent': `Firebase/${PROTOCOL_VERSION}/${SDK_VERSION}/${process.platform}/${device}`,\n            'X-Firebase-GMPID': this.applicationId || ''\n          }\n        };\n\n        // If using Node with admin creds, AppCheck-related checks are unnecessary.\n        // Note that we send the credentials here even if they aren't admin credentials, which is\n        // not a problem.\n        // Note that this header is just used to bypass appcheck, and the token should still be sent\n        // through the websocket connection once it is established.\n        if (this.authToken) {\n          options.headers['Authorization'] = `Bearer ${this.authToken}`;\n        }\n        if (this.appCheckToken) {\n          options.headers['X-Firebase-AppCheck'] = this.appCheckToken;\n        }\n\n        // Plumb appropriate http_proxy environment variable into faye-websocket if it exists.\n        const env = process['env'];\n        const proxy =\n          this.connURL.indexOf('wss://') === 0\n            ? env['HTTPS_PROXY'] || env['https_proxy']\n            : env['HTTP_PROXY'] || env['http_proxy'];\n\n        if (proxy) {\n          options['proxy'] = { origin: proxy };\n        }\n\n        this.mySock = new WebSocketImpl(this.connURL, [], options);\n      } else {\n        const options: { [k: string]: object } = {\n          headers: {\n            'X-Firebase-GMPID': this.applicationId || '',\n            'X-Firebase-AppCheck': this.appCheckToken || ''\n          }\n        };\n        this.mySock = new WebSocketImpl(this.connURL, [], options);\n      }\n    } catch (e) {\n      this.log_('Error instantiating WebSocket.');\n      const error = e.message || e.data;\n      if (error) {\n        this.log_(error);\n      }\n      this.onClosed_();\n      return;\n    }\n\n    this.mySock.onopen = () => {\n      this.log_('Websocket connected.');\n      this.everConnected_ = true;\n    };\n\n    this.mySock.onclose = () => {\n      this.log_('Websocket connection was disconnected.');\n      this.mySock = null;\n      this.onClosed_();\n    };\n\n    this.mySock.onmessage = m => {\n      this.handleIncomingFrame(m as {});\n    };\n\n    this.mySock.onerror = e => {\n      this.log_('WebSocket error.  Closing connection.');\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const error = (e as any).message || (e as any).data;\n      if (error) {\n        this.log_(error);\n      }\n      this.onClosed_();\n    };\n  }\n\n  /**\n   * No-op for websockets, we don't need to do anything once the connection is confirmed as open\n   */\n  start() {}\n\n  static forceDisallow_: boolean;\n\n  static forceDisallow() {\n    WebSocketConnection.forceDisallow_ = true;\n  }\n\n  static isAvailable(): boolean {\n    let isOldAndroid = false;\n    if (typeof navigator !== 'undefined' && navigator.userAgent) {\n      const oldAndroidRegex = /Android ([0-9]{0,}\\.[0-9]{0,})/;\n      const oldAndroidMatch = navigator.userAgent.match(oldAndroidRegex);\n      if (oldAndroidMatch && oldAndroidMatch.length > 1) {\n        if (parseFloat(oldAndroidMatch[1]) < 4.4) {\n          isOldAndroid = true;\n        }\n      }\n    }\n\n    return (\n      !isOldAndroid &&\n      WebSocketImpl !== null &&\n      !WebSocketConnection.forceDisallow_\n    );\n  }\n\n  /**\n   * Number of response before we consider the connection \"healthy.\"\n   */\n  static responsesRequiredToBeHealthy = 2;\n\n  /**\n   * Time to wait for the connection te become healthy before giving up.\n   */\n  static healthyTimeout = 30000;\n\n  /**\n   * Returns true if we previously failed to connect with this transport.\n   */\n  static previouslyFailed(): boolean {\n    // If our persistent storage is actually only in-memory storage,\n    // we default to assuming that it previously failed to be safe.\n    return (\n      PersistentStorage.isInMemoryStorage ||\n      PersistentStorage.get('previous_websocket_failure') === true\n    );\n  }\n\n  markConnectionHealthy() {\n    PersistentStorage.remove('previous_websocket_failure');\n  }\n\n  private appendFrame_(data: string) {\n    this.frames.push(data);\n    if (this.frames.length === this.totalFrames) {\n      const fullMess = this.frames.join('');\n      this.frames = null;\n      const jsonMess = jsonEval(fullMess) as object;\n\n      //handle the message\n      this.onMessage(jsonMess);\n    }\n  }\n\n  /**\n   * @param frameCount - The number of frames we are expecting from the server\n   */\n  private handleNewFrameCount_(frameCount: number) {\n    this.totalFrames = frameCount;\n    this.frames = [];\n  }\n\n  /**\n   * Attempts to parse a frame count out of some text. If it can't, assumes a value of 1\n   * @returns Any remaining data to be process, or null if there is none\n   */\n  private extractFrameCount_(data: string): string | null {\n    assert(this.frames === null, 'We already have a frame buffer');\n    // TODO: The server is only supposed to send up to 9999 frames (i.e. length <= 4), but that isn't being enforced\n    // currently.  So allowing larger frame counts (length <= 6).  See https://app.asana.com/0/search/8688598998380/8237608042508\n    if (data.length <= 6) {\n      const frameCount = Number(data);\n      if (!isNaN(frameCount)) {\n        this.handleNewFrameCount_(frameCount);\n        return null;\n      }\n    }\n    this.handleNewFrameCount_(1);\n    return data;\n  }\n\n  /**\n   * Process a websocket frame that has arrived from the server.\n   * @param mess - The frame data\n   */\n  handleIncomingFrame(mess: { [k: string]: unknown }) {\n    if (this.mySock === null) {\n      return; // Chrome apparently delivers incoming packets even after we .close() the connection sometimes.\n    }\n    const data = mess['data'] as string;\n    this.bytesReceived += data.length;\n    this.stats_.incrementCounter('bytes_received', data.length);\n\n    this.resetKeepAlive();\n\n    if (this.frames !== null) {\n      // we're buffering\n      this.appendFrame_(data);\n    } else {\n      // try to parse out a frame count, otherwise, assume 1 and process it\n      const remainingData = this.extractFrameCount_(data);\n      if (remainingData !== null) {\n        this.appendFrame_(remainingData);\n      }\n    }\n  }\n\n  /**\n   * Send a message to the server\n   * @param data - The JSON object to transmit\n   */\n  send(data: {}) {\n    this.resetKeepAlive();\n\n    const dataStr = stringify(data);\n    this.bytesSent += dataStr.length;\n    this.stats_.incrementCounter('bytes_sent', dataStr.length);\n\n    //We can only fit a certain amount in each websocket frame, so we need to split this request\n    //up into multiple pieces if it doesn't fit in one request.\n\n    const dataSegs = splitStringBySize(dataStr, WEBSOCKET_MAX_FRAME_SIZE);\n\n    //Send the length header\n    if (dataSegs.length > 1) {\n      this.sendString_(String(dataSegs.length));\n    }\n\n    //Send the actual data in segments.\n    for (let i = 0; i < dataSegs.length; i++) {\n      this.sendString_(dataSegs[i]);\n    }\n  }\n\n  private shutdown_() {\n    this.isClosed_ = true;\n    if (this.keepaliveTimer) {\n      clearInterval(this.keepaliveTimer);\n      this.keepaliveTimer = null;\n    }\n\n    if (this.mySock) {\n      this.mySock.close();\n      this.mySock = null;\n    }\n  }\n\n  private onClosed_() {\n    if (!this.isClosed_) {\n      this.log_('WebSocket is closing itself');\n      this.shutdown_();\n\n      // since this is an internal close, trigger the close listener\n      if (this.onDisconnect) {\n        this.onDisconnect(this.everConnected_);\n        this.onDisconnect = null;\n      }\n    }\n  }\n\n  /**\n   * External-facing close handler.\n   * Close the websocket and kill the connection.\n   */\n  close() {\n    if (!this.isClosed_) {\n      this.log_('WebSocket is being closed');\n      this.shutdown_();\n    }\n  }\n\n  /**\n   * Kill the current keepalive timer and start a new one, to ensure that it always fires N seconds after\n   * the last activity.\n   */\n  resetKeepAlive() {\n    clearInterval(this.keepaliveTimer);\n    this.keepaliveTimer = setInterval(() => {\n      //If there has been no websocket activity for a while, send a no-op\n      if (this.mySock) {\n        this.sendString_('0');\n      }\n      this.resetKeepAlive();\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    }, Math.floor(WEBSOCKET_KEEPALIVE_INTERVAL)) as any;\n  }\n\n  /**\n   * Send a string over the websocket.\n   *\n   * @param str - String to send.\n   */\n  private sendString_(str: string) {\n    // Firefox seems to sometimes throw exceptions (NS_ERROR_UNEXPECTED) from websocket .send()\n    // calls for some unknown reason.  We treat these as an error and disconnect.\n    // See https://app.asana.com/0/58926111402292/68021340250410\n    try {\n      this.mySock.send(str);\n    } catch (e) {\n      this.log_(\n        'Exception thrown from WebSocket.send():',\n        e.message || e.data,\n        'Closing connection.'\n      );\n      setTimeout(this.onClosed_.bind(this), 0);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RepoInfo } from '../core/RepoInfo';\nimport { warn } from '../core/util/util';\n\nimport { BrowserPollConnection } from './BrowserPollConnection';\nimport { TransportConstructor } from './Transport';\nimport { WebSocketConnection } from './WebSocketConnection';\n\n/**\n * Currently simplistic, this class manages what transport a Connection should use at various stages of its\n * lifecycle.\n *\n * It starts with longpolling in a browser, and httppolling on node. It then upgrades to websockets if\n * they are available.\n */\nexport class TransportManager {\n  private transports_: TransportConstructor[];\n\n  static get ALL_TRANSPORTS() {\n    return [BrowserPollConnection, WebSocketConnection];\n  }\n\n  /**\n   * @param repoInfo - Metadata around the namespace we're connecting to\n   */\n  constructor(repoInfo: RepoInfo) {\n    this.initTransports_(repoInfo);\n  }\n\n  private initTransports_(repoInfo: RepoInfo) {\n    const isWebSocketsAvailable: boolean =\n      WebSocketConnection && WebSocketConnection['isAvailable']();\n    let isSkipPollConnection =\n      isWebSocketsAvailable && !WebSocketConnection.previouslyFailed();\n\n    if (repoInfo.webSocketOnly) {\n      if (!isWebSocketsAvailable) {\n        warn(\n          \"wss:// URL used, but browser isn't known to support websockets.  Trying anyway.\"\n        );\n      }\n\n      isSkipPollConnection = true;\n    }\n\n    if (isSkipPollConnection) {\n      this.transports_ = [WebSocketConnection];\n    } else {\n      const transports = (this.transports_ = [] as TransportConstructor[]);\n      for (const transport of TransportManager.ALL_TRANSPORTS) {\n        if (transport && transport['isAvailable']()) {\n          transports.push(transport);\n        }\n      }\n    }\n  }\n\n  /**\n   * @returns The constructor for the initial transport to use\n   */\n  initialTransport(): TransportConstructor {\n    if (this.transports_.length > 0) {\n      return this.transports_[0];\n    } else {\n      throw new Error('No transports available');\n    }\n  }\n\n  /**\n   * @returns The constructor for the next transport, or null\n   */\n  upgradeTransport(): TransportConstructor | null {\n    if (this.transports_.length > 1) {\n      return this.transports_[1];\n    } else {\n      return null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RepoInfo } from '../core/RepoInfo';\nimport { PersistentStorage } from '../core/storage/storage';\nimport { Indexable } from '../core/util/misc';\nimport {\n  error,\n  logWrapper,\n  requireKey,\n  setTimeoutNonBlocking,\n  warn\n} from '../core/util/util';\n\nimport { PROTOCOL_VERSION } from './Constants';\nimport { Transport, TransportConstructor } from './Transport';\nimport { TransportManager } from './TransportManager';\n\n// Abort upgrade attempt if it takes longer than 60s.\nconst UPGRADE_TIMEOUT = 60000;\n\n// For some transports (WebSockets), we need to \"validate\" the transport by exchanging a few requests and responses.\n// If we haven't sent enough requests within 5s, we'll start sending noop ping requests.\nconst DELAY_BEFORE_SENDING_EXTRA_REQUESTS = 5000;\n\n// If the initial data sent triggers a lot of bandwidth (i.e. it's a large put or a listen for a large amount of data)\n// then we may not be able to exchange our ping/pong requests within the healthy timeout.  So if we reach the timeout\n// but we've sent/received enough bytes, we don't cancel the connection.\nconst BYTES_SENT_HEALTHY_OVERRIDE = 10 * 1024;\nconst BYTES_RECEIVED_HEALTHY_OVERRIDE = 100 * 1024;\n\nconst enum RealtimeState {\n  CONNECTING,\n  CONNECTED,\n  DISCONNECTED\n}\n\nconst MESSAGE_TYPE = 't';\nconst MESSAGE_DATA = 'd';\nconst CONTROL_SHUTDOWN = 's';\nconst CONTROL_RESET = 'r';\nconst CONTROL_ERROR = 'e';\nconst CONTROL_PONG = 'o';\nconst SWITCH_ACK = 'a';\nconst END_TRANSMISSION = 'n';\nconst PING = 'p';\n\nconst SERVER_HELLO = 'h';\n\n/**\n * Creates a new real-time connection to the server using whichever method works\n * best in the current browser.\n */\nexport class Connection {\n  connectionCount = 0;\n  pendingDataMessages: unknown[] = [];\n  sessionId: string;\n\n  private conn_: Transport;\n  private healthyTimeout_: number;\n  private isHealthy_: boolean;\n  private log_: (...args: unknown[]) => void;\n  private primaryResponsesRequired_: number;\n  private rx_: Transport;\n  private secondaryConn_: Transport;\n  private secondaryResponsesRequired_: number;\n  private state_ = RealtimeState.CONNECTING;\n  private transportManager_: TransportManager;\n  private tx_: Transport;\n\n  /**\n   * @param id - an id for this connection\n   * @param repoInfo_ - the info for the endpoint to connect to\n   * @param applicationId_ - the Firebase App ID for this project\n   * @param appCheckToken_ - The App Check Token for this device.\n   * @param authToken_ - The auth token for this session.\n   * @param onMessage_ - the callback to be triggered when a server-push message arrives\n   * @param onReady_ - the callback to be triggered when this connection is ready to send messages.\n   * @param onDisconnect_ - the callback to be triggered when a connection was lost\n   * @param onKill_ - the callback to be triggered when this connection has permanently shut down.\n   * @param lastSessionId - last session id in persistent connection. is used to clean up old session in real-time server\n   */\n  constructor(\n    public id: string,\n    private repoInfo_: RepoInfo,\n    private applicationId_: string | undefined,\n    private appCheckToken_: string | undefined,\n    private authToken_: string | undefined,\n    private onMessage_: (a: {}) => void,\n    private onReady_: (a: number, b: string) => void,\n    private onDisconnect_: () => void,\n    private onKill_: (a: string) => void,\n    public lastSessionId?: string\n  ) {\n    this.log_ = logWrapper('c:' + this.id + ':');\n    this.transportManager_ = new TransportManager(repoInfo_);\n    this.log_('Connection created');\n    this.start_();\n  }\n\n  /**\n   * Starts a connection attempt\n   */\n  private start_(): void {\n    const conn = this.transportManager_.initialTransport();\n    this.conn_ = new conn(\n      this.nextTransportId_(),\n      this.repoInfo_,\n      this.applicationId_,\n      this.appCheckToken_,\n      this.authToken_,\n      null,\n      this.lastSessionId\n    );\n\n    // For certain transports (WebSockets), we need to send and receive several messages back and forth before we\n    // can consider the transport healthy.\n    this.primaryResponsesRequired_ = conn['responsesRequiredToBeHealthy'] || 0;\n\n    const onMessageReceived = this.connReceiver_(this.conn_);\n    const onConnectionLost = this.disconnReceiver_(this.conn_);\n    this.tx_ = this.conn_;\n    this.rx_ = this.conn_;\n    this.secondaryConn_ = null;\n    this.isHealthy_ = false;\n\n    /*\n     * Firefox doesn't like when code from one iframe tries to create another iframe by way of the parent frame.\n     * This can occur in the case of a redirect, i.e. we guessed wrong on what server to connect to and received a reset.\n     * Somehow, setTimeout seems to make this ok. That doesn't make sense from a security perspective, since you should\n     * still have the context of your originating frame.\n     */\n    setTimeout(() => {\n      // this.conn_ gets set to null in some of the tests. Check to make sure it still exists before using it\n      this.conn_ && this.conn_.open(onMessageReceived, onConnectionLost);\n    }, Math.floor(0));\n\n    const healthyTimeoutMS = conn['healthyTimeout'] || 0;\n    if (healthyTimeoutMS > 0) {\n      this.healthyTimeout_ = setTimeoutNonBlocking(() => {\n        this.healthyTimeout_ = null;\n        if (!this.isHealthy_) {\n          if (\n            this.conn_ &&\n            this.conn_.bytesReceived > BYTES_RECEIVED_HEALTHY_OVERRIDE\n          ) {\n            this.log_(\n              'Connection exceeded healthy timeout but has received ' +\n                this.conn_.bytesReceived +\n                ' bytes.  Marking connection healthy.'\n            );\n            this.isHealthy_ = true;\n            this.conn_.markConnectionHealthy();\n          } else if (\n            this.conn_ &&\n            this.conn_.bytesSent > BYTES_SENT_HEALTHY_OVERRIDE\n          ) {\n            this.log_(\n              'Connection exceeded healthy timeout but has sent ' +\n                this.conn_.bytesSent +\n                ' bytes.  Leaving connection alive.'\n            );\n            // NOTE: We don't want to mark it healthy, since we have no guarantee that the bytes have made it to\n            // the server.\n          } else {\n            this.log_('Closing unhealthy connection after timeout.');\n            this.close();\n          }\n        }\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      }, Math.floor(healthyTimeoutMS)) as any;\n    }\n  }\n\n  private nextTransportId_(): string {\n    return 'c:' + this.id + ':' + this.connectionCount++;\n  }\n\n  private disconnReceiver_(conn) {\n    return everConnected => {\n      if (conn === this.conn_) {\n        this.onConnectionLost_(everConnected);\n      } else if (conn === this.secondaryConn_) {\n        this.log_('Secondary connection lost.');\n        this.onSecondaryConnectionLost_();\n      } else {\n        this.log_('closing an old connection');\n      }\n    };\n  }\n\n  private connReceiver_(conn: Transport) {\n    return (message: Indexable) => {\n      if (this.state_ !== RealtimeState.DISCONNECTED) {\n        if (conn === this.rx_) {\n          this.onPrimaryMessageReceived_(message);\n        } else if (conn === this.secondaryConn_) {\n          this.onSecondaryMessageReceived_(message);\n        } else {\n          this.log_('message on old connection');\n        }\n      }\n    };\n  }\n\n  /**\n   * @param dataMsg - An arbitrary data message to be sent to the server\n   */\n  sendRequest(dataMsg: object) {\n    // wrap in a data message envelope and send it on\n    const msg = { t: 'd', d: dataMsg };\n    this.sendData_(msg);\n  }\n\n  tryCleanupConnection() {\n    if (this.tx_ === this.secondaryConn_ && this.rx_ === this.secondaryConn_) {\n      this.log_(\n        'cleaning up and promoting a connection: ' + this.secondaryConn_.connId\n      );\n      this.conn_ = this.secondaryConn_;\n      this.secondaryConn_ = null;\n      // the server will shutdown the old connection\n    }\n  }\n\n  private onSecondaryControl_(controlData: { [k: string]: unknown }) {\n    if (MESSAGE_TYPE in controlData) {\n      const cmd = controlData[MESSAGE_TYPE] as string;\n      if (cmd === SWITCH_ACK) {\n        this.upgradeIfSecondaryHealthy_();\n      } else if (cmd === CONTROL_RESET) {\n        // Most likely the session wasn't valid. Abandon the switch attempt\n        this.log_('Got a reset on secondary, closing it');\n        this.secondaryConn_.close();\n        // If we were already using this connection for something, than we need to fully close\n        if (\n          this.tx_ === this.secondaryConn_ ||\n          this.rx_ === this.secondaryConn_\n        ) {\n          this.close();\n        }\n      } else if (cmd === CONTROL_PONG) {\n        this.log_('got pong on secondary.');\n        this.secondaryResponsesRequired_--;\n        this.upgradeIfSecondaryHealthy_();\n      }\n    }\n  }\n\n  private onSecondaryMessageReceived_(parsedData: Indexable) {\n    const layer: string = requireKey('t', parsedData) as string;\n    const data: unknown = requireKey('d', parsedData);\n    if (layer === 'c') {\n      this.onSecondaryControl_(data as Indexable);\n    } else if (layer === 'd') {\n      // got a data message, but we're still second connection. Need to buffer it up\n      this.pendingDataMessages.push(data);\n    } else {\n      throw new Error('Unknown protocol layer: ' + layer);\n    }\n  }\n\n  private upgradeIfSecondaryHealthy_() {\n    if (this.secondaryResponsesRequired_ <= 0) {\n      this.log_('Secondary connection is healthy.');\n      this.isHealthy_ = true;\n      this.secondaryConn_.markConnectionHealthy();\n      this.proceedWithUpgrade_();\n    } else {\n      // Send a ping to make sure the connection is healthy.\n      this.log_('sending ping on secondary.');\n      this.secondaryConn_.send({ t: 'c', d: { t: PING, d: {} } });\n    }\n  }\n\n  private proceedWithUpgrade_() {\n    // tell this connection to consider itself open\n    this.secondaryConn_.start();\n    // send ack\n    this.log_('sending client ack on secondary');\n    this.secondaryConn_.send({ t: 'c', d: { t: SWITCH_ACK, d: {} } });\n\n    // send end packet on primary transport, switch to sending on this one\n    // can receive on this one, buffer responses until end received on primary transport\n    this.log_('Ending transmission on primary');\n    this.conn_.send({ t: 'c', d: { t: END_TRANSMISSION, d: {} } });\n    this.tx_ = this.secondaryConn_;\n\n    this.tryCleanupConnection();\n  }\n\n  private onPrimaryMessageReceived_(parsedData: { [k: string]: unknown }) {\n    // Must refer to parsedData properties in quotes, so closure doesn't touch them.\n    const layer: string = requireKey('t', parsedData) as string;\n    const data: unknown = requireKey('d', parsedData);\n    if (layer === 'c') {\n      this.onControl_(data as { [k: string]: unknown });\n    } else if (layer === 'd') {\n      this.onDataMessage_(data);\n    }\n  }\n\n  private onDataMessage_(message: unknown) {\n    this.onPrimaryResponse_();\n\n    // We don't do anything with data messages, just kick them up a level\n    this.onMessage_(message);\n  }\n\n  private onPrimaryResponse_() {\n    if (!this.isHealthy_) {\n      this.primaryResponsesRequired_--;\n      if (this.primaryResponsesRequired_ <= 0) {\n        this.log_('Primary connection is healthy.');\n        this.isHealthy_ = true;\n        this.conn_.markConnectionHealthy();\n      }\n    }\n  }\n\n  private onControl_(controlData: { [k: string]: unknown }) {\n    const cmd: string = requireKey(MESSAGE_TYPE, controlData) as string;\n    if (MESSAGE_DATA in controlData) {\n      const payload = controlData[MESSAGE_DATA];\n      if (cmd === SERVER_HELLO) {\n        this.onHandshake_(\n          payload as {\n            ts: number;\n            v: string;\n            h: string;\n            s: string;\n          }\n        );\n      } else if (cmd === END_TRANSMISSION) {\n        this.log_('recvd end transmission on primary');\n        this.rx_ = this.secondaryConn_;\n        for (let i = 0; i < this.pendingDataMessages.length; ++i) {\n          this.onDataMessage_(this.pendingDataMessages[i]);\n        }\n        this.pendingDataMessages = [];\n        this.tryCleanupConnection();\n      } else if (cmd === CONTROL_SHUTDOWN) {\n        // This was previously the 'onKill' callback passed to the lower-level connection\n        // payload in this case is the reason for the shutdown. Generally a human-readable error\n        this.onConnectionShutdown_(payload as string);\n      } else if (cmd === CONTROL_RESET) {\n        // payload in this case is the host we should contact\n        this.onReset_(payload as string);\n      } else if (cmd === CONTROL_ERROR) {\n        error('Server Error: ' + payload);\n      } else if (cmd === CONTROL_PONG) {\n        this.log_('got pong on primary.');\n        this.onPrimaryResponse_();\n        this.sendPingOnPrimaryIfNecessary_();\n      } else {\n        error('Unknown control packet command: ' + cmd);\n      }\n    }\n  }\n\n  /**\n   * @param handshake - The handshake data returned from the server\n   */\n  private onHandshake_(handshake: {\n    ts: number;\n    v: string;\n    h: string;\n    s: string;\n  }): void {\n    const timestamp = handshake.ts;\n    const version = handshake.v;\n    const host = handshake.h;\n    this.sessionId = handshake.s;\n    this.repoInfo_.host = host;\n    // if we've already closed the connection, then don't bother trying to progress further\n    if (this.state_ === RealtimeState.CONNECTING) {\n      this.conn_.start();\n      this.onConnectionEstablished_(this.conn_, timestamp);\n      if (PROTOCOL_VERSION !== version) {\n        warn('Protocol version mismatch detected');\n      }\n      // TODO: do we want to upgrade? when? maybe a delay?\n      this.tryStartUpgrade_();\n    }\n  }\n\n  private tryStartUpgrade_() {\n    const conn = this.transportManager_.upgradeTransport();\n    if (conn) {\n      this.startUpgrade_(conn);\n    }\n  }\n\n  private startUpgrade_(conn: TransportConstructor) {\n    this.secondaryConn_ = new conn(\n      this.nextTransportId_(),\n      this.repoInfo_,\n      this.applicationId_,\n      this.appCheckToken_,\n      this.authToken_,\n      this.sessionId\n    );\n    // For certain transports (WebSockets), we need to send and receive several messages back and forth before we\n    // can consider the transport healthy.\n    this.secondaryResponsesRequired_ =\n      conn['responsesRequiredToBeHealthy'] || 0;\n\n    const onMessage = this.connReceiver_(this.secondaryConn_);\n    const onDisconnect = this.disconnReceiver_(this.secondaryConn_);\n    this.secondaryConn_.open(onMessage, onDisconnect);\n\n    // If we haven't successfully upgraded after UPGRADE_TIMEOUT, give up and kill the secondary.\n    setTimeoutNonBlocking(() => {\n      if (this.secondaryConn_) {\n        this.log_('Timed out trying to upgrade.');\n        this.secondaryConn_.close();\n      }\n    }, Math.floor(UPGRADE_TIMEOUT));\n  }\n\n  private onReset_(host: string) {\n    this.log_('Reset packet received.  New host: ' + host);\n    this.repoInfo_.host = host;\n    // TODO: if we're already \"connected\", we need to trigger a disconnect at the next layer up.\n    // We don't currently support resets after the connection has already been established\n    if (this.state_ === RealtimeState.CONNECTED) {\n      this.close();\n    } else {\n      // Close whatever connections we have open and start again.\n      this.closeConnections_();\n      this.start_();\n    }\n  }\n\n  private onConnectionEstablished_(conn: Transport, timestamp: number) {\n    this.log_('Realtime connection established.');\n    this.conn_ = conn;\n    this.state_ = RealtimeState.CONNECTED;\n\n    if (this.onReady_) {\n      this.onReady_(timestamp, this.sessionId);\n      this.onReady_ = null;\n    }\n\n    // If after 5 seconds we haven't sent enough requests to the server to get the connection healthy,\n    // send some pings.\n    if (this.primaryResponsesRequired_ === 0) {\n      this.log_('Primary connection is healthy.');\n      this.isHealthy_ = true;\n    } else {\n      setTimeoutNonBlocking(() => {\n        this.sendPingOnPrimaryIfNecessary_();\n      }, Math.floor(DELAY_BEFORE_SENDING_EXTRA_REQUESTS));\n    }\n  }\n\n  private sendPingOnPrimaryIfNecessary_() {\n    // If the connection isn't considered healthy yet, we'll send a noop ping packet request.\n    if (!this.isHealthy_ && this.state_ === RealtimeState.CONNECTED) {\n      this.log_('sending ping on primary.');\n      this.sendData_({ t: 'c', d: { t: PING, d: {} } });\n    }\n  }\n\n  private onSecondaryConnectionLost_() {\n    const conn = this.secondaryConn_;\n    this.secondaryConn_ = null;\n    if (this.tx_ === conn || this.rx_ === conn) {\n      // we are relying on this connection already in some capacity. Therefore, a failure is real\n      this.close();\n    }\n  }\n\n  /**\n   * @param everConnected - Whether or not the connection ever reached a server. Used to determine if\n   * we should flush the host cache\n   */\n  private onConnectionLost_(everConnected: boolean) {\n    this.conn_ = null;\n\n    // NOTE: IF you're seeing a Firefox error for this line, I think it might be because it's getting\n    // called on window close and RealtimeState.CONNECTING is no longer defined.  Just a guess.\n    if (!everConnected && this.state_ === RealtimeState.CONNECTING) {\n      this.log_('Realtime connection failed.');\n      // Since we failed to connect at all, clear any cached entry for this namespace in case the machine went away\n      if (this.repoInfo_.isCacheableHost()) {\n        PersistentStorage.remove('host:' + this.repoInfo_.host);\n        // reset the internal host to what we would show the user, i.e. <ns>.firebaseio.com\n        this.repoInfo_.internalHost = this.repoInfo_.host;\n      }\n    } else if (this.state_ === RealtimeState.CONNECTED) {\n      this.log_('Realtime connection lost.');\n    }\n\n    this.close();\n  }\n\n  private onConnectionShutdown_(reason: string) {\n    this.log_('Connection shutdown command received. Shutting down...');\n\n    if (this.onKill_) {\n      this.onKill_(reason);\n      this.onKill_ = null;\n    }\n\n    // We intentionally don't want to fire onDisconnect (kill is a different case),\n    // so clear the callback.\n    this.onDisconnect_ = null;\n\n    this.close();\n  }\n\n  private sendData_(data: object) {\n    if (this.state_ !== RealtimeState.CONNECTED) {\n      throw 'Connection is not connected';\n    } else {\n      this.tx_.send(data);\n    }\n  }\n\n  /**\n   * Cleans up this connection, calling the appropriate callbacks\n   */\n  close() {\n    if (this.state_ !== RealtimeState.DISCONNECTED) {\n      this.log_('Closing realtime connection.');\n      this.state_ = RealtimeState.DISCONNECTED;\n\n      this.closeConnections_();\n\n      if (this.onDisconnect_) {\n        this.onDisconnect_();\n        this.onDisconnect_ = null;\n      }\n    }\n  }\n\n  private closeConnections_() {\n    this.log_('Shutting down all connections');\n    if (this.conn_) {\n      this.conn_.close();\n      this.conn_ = null;\n    }\n\n    if (this.secondaryConn_) {\n      this.secondaryConn_.close();\n      this.secondaryConn_ = null;\n    }\n\n    if (this.healthyTimeout_) {\n      clearTimeout(this.healthyTimeout_);\n      this.healthyTimeout_ = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { QueryContext } from './view/EventRegistration';\n\n/**\n * Interface defining the set of actions that can be performed against the Firebase server\n * (basically corresponds to our wire protocol).\n *\n * @interface\n */\nexport abstract class ServerActions {\n  abstract listen(\n    query: QueryContext,\n    currentHashFn: () => string,\n    tag: number | null,\n    onComplete: (a: string, b: unknown) => void\n  ): void;\n\n  /**\n   * Remove a listen.\n   */\n  abstract unlisten(query: QueryContext, tag: number | null): void;\n\n  /**\n   * Get the server value satisfying this query.\n   */\n  abstract get(query: QueryContext): Promise<string>;\n\n  put(\n    pathString: string,\n    data: unknown,\n    onComplete?: (a: string, b: string) => void,\n    hash?: string\n  ) {}\n\n  merge(\n    pathString: string,\n    data: unknown,\n    onComplete: (a: string, b: string | null) => void,\n    hash?: string\n  ) {}\n\n  /**\n   * Refreshes the auth token for the current connection.\n   * @param token - The authentication token\n   */\n  refreshAuthToken(token: string) {}\n\n  /**\n   * Refreshes the app check token for the current connection.\n   * @param token The app check token\n   */\n  refreshAppCheckToken(token: string) {}\n\n  onDisconnectPut(\n    pathString: string,\n    data: unknown,\n    onComplete?: (a: string, b: string) => void\n  ) {}\n\n  onDisconnectMerge(\n    pathString: string,\n    data: unknown,\n    onComplete?: (a: string, b: string) => void\n  ) {}\n\n  onDisconnectCancel(\n    pathString: string,\n    onComplete?: (a: string, b: string) => void\n  ) {}\n\n  reportStats(stats: { [k: string]: unknown }) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from '@firebase/util';\n\n/**\n * Base class to be used if you want to emit events. Call the constructor with\n * the set of allowed event names.\n */\nexport abstract class EventEmitter {\n  private listeners_: {\n    [eventType: string]: Array<{\n      callback(...args: unknown[]): void;\n      context: unknown;\n    }>;\n  } = {};\n\n  constructor(private allowedEvents_: string[]) {\n    assert(\n      Array.isArray(allowedEvents_) && allowedEvents_.length > 0,\n      'Requires a non-empty array'\n    );\n  }\n\n  /**\n   * To be overridden by derived classes in order to fire an initial event when\n   * somebody subscribes for data.\n   *\n   * @returns {Array.<*>} Array of parameters to trigger initial event with.\n   */\n  abstract getInitialEvent(eventType: string): unknown[];\n\n  /**\n   * To be called by derived classes to trigger events.\n   */\n  protected trigger(eventType: string, ...varArgs: unknown[]) {\n    if (Array.isArray(this.listeners_[eventType])) {\n      // Clone the list, since callbacks could add/remove listeners.\n      const listeners = [...this.listeners_[eventType]];\n\n      for (let i = 0; i < listeners.length; i++) {\n        listeners[i].callback.apply(listeners[i].context, varArgs);\n      }\n    }\n  }\n\n  on(eventType: string, callback: (a: unknown) => void, context: unknown) {\n    this.validateEventType_(eventType);\n    this.listeners_[eventType] = this.listeners_[eventType] || [];\n    this.listeners_[eventType].push({ callback, context });\n\n    const eventData = this.getInitialEvent(eventType);\n    if (eventData) {\n      callback.apply(context, eventData);\n    }\n  }\n\n  off(eventType: string, callback: (a: unknown) => void, context: unknown) {\n    this.validateEventType_(eventType);\n    const listeners = this.listeners_[eventType] || [];\n    for (let i = 0; i < listeners.length; i++) {\n      if (\n        listeners[i].callback === callback &&\n        (!context || context === listeners[i].context)\n      ) {\n        listeners.splice(i, 1);\n        return;\n      }\n    }\n  }\n\n  private validateEventType_(eventType: string) {\n    assert(\n      this.allowedEvents_.find(et => {\n        return et === eventType;\n      }),\n      'Unknown event: ' + eventType\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert, isMobileCordova } from '@firebase/util';\n\nimport { EventEmitter } from './EventEmitter';\n\n/**\n * Monitors online state (as reported by window.online/offline events).\n *\n * The expectation is that this could have many false positives (thinks we are online\n * when we're not), but no false negatives.  So we can safely use it to determine when\n * we definitely cannot reach the internet.\n */\nexport class OnlineMonitor extends EventEmitter {\n  private online_ = true;\n\n  static getInstance() {\n    return new OnlineMonitor();\n  }\n\n  constructor() {\n    super(['online']);\n\n    // We've had repeated complaints that Cordova apps can get stuck \"offline\", e.g.\n    // https://forum.ionicframework.com/t/firebase-connection-is-lost-and-never-come-back/43810\n    // It would seem that the 'online' event does not always fire consistently. So we disable it\n    // for Cordova.\n    if (\n      typeof window !== 'undefined' &&\n      typeof window.addEventListener !== 'undefined' &&\n      !isMobileCordova()\n    ) {\n      window.addEventListener(\n        'online',\n        () => {\n          if (!this.online_) {\n            this.online_ = true;\n            this.trigger('online', true);\n          }\n        },\n        false\n      );\n\n      window.addEventListener(\n        'offline',\n        () => {\n          if (this.online_) {\n            this.online_ = false;\n            this.trigger('online', false);\n          }\n        },\n        false\n      );\n    }\n  }\n\n  getInitialEvent(eventType: string): boolean[] {\n    assert(eventType === 'online', 'Unknown event type: ' + eventType);\n    return [this.online_];\n  }\n\n  currentlyOnline(): boolean {\n    return this.online_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from '@firebase/util';\n\nimport { EventEmitter } from './EventEmitter';\n\ndeclare const document: Document;\n\nexport class VisibilityMonitor extends EventEmitter {\n  private visible_: boolean;\n\n  static getInstance() {\n    return new VisibilityMonitor();\n  }\n\n  constructor() {\n    super(['visible']);\n    let hidden: string;\n    let visibilityChange: string;\n    if (\n      typeof document !== 'undefined' &&\n      typeof document.addEventListener !== 'undefined'\n    ) {\n      if (typeof document['hidden'] !== 'undefined') {\n        // Opera 12.10 and Firefox 18 and later support\n        visibilityChange = 'visibilitychange';\n        hidden = 'hidden';\n      } else if (typeof document['mozHidden'] !== 'undefined') {\n        visibilityChange = 'mozvisibilitychange';\n        hidden = 'mozHidden';\n      } else if (typeof document['msHidden'] !== 'undefined') {\n        visibilityChange = 'msvisibilitychange';\n        hidden = 'msHidden';\n      } else if (typeof document['webkitHidden'] !== 'undefined') {\n        visibilityChange = 'webkitvisibilitychange';\n        hidden = 'webkitHidden';\n      }\n    }\n\n    // Initially, we always assume we are visible. This ensures that in browsers\n    // without page visibility support or in cases where we are never visible\n    // (e.g. chrome extension), we act as if we are visible, i.e. don't delay\n    // reconnects\n    this.visible_ = true;\n\n    if (visibilityChange) {\n      document.addEventListener(\n        visibilityChange,\n        () => {\n          const visible = !document[hidden];\n          if (visible !== this.visible_) {\n            this.visible_ = visible;\n            this.trigger('visible', visible);\n          }\n        },\n        false\n      );\n    }\n  }\n\n  getInitialEvent(eventType: string): boolean[] {\n    assert(eventType === 'visible', 'Unknown event type: ' + eventType);\n    return [this.visible_];\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  assert,\n  contains,\n  Deferred,\n  isEmpty,\n  isMobileCordova,\n  isNodeSdk,\n  isReactNative,\n  isValidFormat,\n  safeGet,\n  stringify,\n  isAdmin\n} from '@firebase/util';\n\nimport { Connection } from '../realtime/Connection';\n\nimport { AppCheckTokenProvider } from './AppCheckTokenProvider';\nimport { AuthTokenProvider } from './AuthTokenProvider';\nimport { RepoInfo } from './RepoInfo';\nimport { ServerActions } from './ServerActions';\nimport { OnlineMonitor } from './util/OnlineMonitor';\nimport { Path } from './util/Path';\nimport { error, log, logWrapper, warn, ObjectToUniqueKey } from './util/util';\nimport { VisibilityMonitor } from './util/VisibilityMonitor';\nimport { SDK_VERSION } from './version';\nimport { QueryContext } from './view/EventRegistration';\n\nconst RECONNECT_MIN_DELAY = 1000;\nconst RECONNECT_MAX_DELAY_DEFAULT = 60 * 5 * 1000; // 5 minutes in milliseconds (Case: 1858)\nconst GET_CONNECT_TIMEOUT = 3 * 1000;\nconst RECONNECT_MAX_DELAY_FOR_ADMINS = 30 * 1000; // 30 seconds for admin clients (likely to be a backend server)\nconst RECONNECT_DELAY_MULTIPLIER = 1.3;\nconst RECONNECT_DELAY_RESET_TIMEOUT = 30000; // Reset delay back to MIN_DELAY after being connected for 30sec.\nconst SERVER_KILL_INTERRUPT_REASON = 'server_kill';\n\n// If auth fails repeatedly, we'll assume something is wrong and log a warning / back off.\nconst INVALID_TOKEN_THRESHOLD = 3;\n\ninterface ListenSpec {\n  onComplete(s: string, p?: unknown): void;\n\n  hashFn(): string;\n\n  query: QueryContext;\n  tag: number | null;\n}\n\ninterface OnDisconnectRequest {\n  pathString: string;\n  action: string;\n  data: unknown;\n  onComplete?: (a: string, b: string) => void;\n}\n\ninterface OutstandingPut {\n  action: string;\n  request: object;\n  queued?: boolean;\n  onComplete: (a: string, b?: string) => void;\n}\n\ninterface OutstandingGet {\n  request: object;\n  onComplete: (response: { [k: string]: unknown }) => void;\n}\n\n/**\n * Firebase connection.  Abstracts wire protocol and handles reconnecting.\n *\n * NOTE: All JSON objects sent to the realtime connection must have property names enclosed\n * in quotes to make sure the closure compiler does not minify them.\n */\nexport class PersistentConnection extends ServerActions {\n  // Used for diagnostic logging.\n  id = PersistentConnection.nextPersistentConnectionId_++;\n  private log_ = logWrapper('p:' + this.id + ':');\n\n  private interruptReasons_: { [reason: string]: boolean } = {};\n  private readonly listens: Map<\n    /* path */ string,\n    Map</* queryId */ string, ListenSpec>\n  > = new Map();\n  private outstandingPuts_: OutstandingPut[] = [];\n  private outstandingGets_: OutstandingGet[] = [];\n  private outstandingPutCount_ = 0;\n  private outstandingGetCount_ = 0;\n  private onDisconnectRequestQueue_: OnDisconnectRequest[] = [];\n  private connected_ = false;\n  private reconnectDelay_ = RECONNECT_MIN_DELAY;\n  private maxReconnectDelay_ = RECONNECT_MAX_DELAY_DEFAULT;\n  private securityDebugCallback_: ((a: object) => void) | null = null;\n  lastSessionId: string | null = null;\n\n  private establishConnectionTimer_: number | null = null;\n\n  private visible_: boolean = false;\n\n  // Before we get connected, we keep a queue of pending messages to send.\n  private requestCBHash_: { [k: number]: (a: unknown) => void } = {};\n  private requestNumber_ = 0;\n\n  private realtime_: {\n    sendRequest(a: object): void;\n    close(): void;\n  } | null = null;\n\n  private authToken_: string | null = null;\n  private appCheckToken_: string | null = null;\n  private forceTokenRefresh_ = false;\n  private invalidAuthTokenCount_ = 0;\n  private invalidAppCheckTokenCount_ = 0;\n\n  private firstConnection_ = true;\n  private lastConnectionAttemptTime_: number | null = null;\n  private lastConnectionEstablishedTime_: number | null = null;\n\n  private static nextPersistentConnectionId_ = 0;\n\n  /**\n   * Counter for number of connections created. Mainly used for tagging in the logs\n   */\n  private static nextConnectionId_ = 0;\n\n  /**\n   * @param repoInfo_ - Data about the namespace we are connecting to\n   * @param applicationId_ - The Firebase App ID for this project\n   * @param onDataUpdate_ - A callback for new data from the server\n   */\n  constructor(\n    private repoInfo_: RepoInfo,\n    private applicationId_: string,\n    private onDataUpdate_: (\n      a: string,\n      b: unknown,\n      c: boolean,\n      d: number | null\n    ) => void,\n    private onConnectStatus_: (a: boolean) => void,\n    private onServerInfoUpdate_: (a: unknown) => void,\n    private authTokenProvider_: AuthTokenProvider,\n    private appCheckTokenProvider_: AppCheckTokenProvider,\n    private authOverride_?: object | null\n  ) {\n    super();\n\n    if (authOverride_ && !isNodeSdk()) {\n      throw new Error(\n        'Auth override specified in options, but not supported on non Node.js platforms'\n      );\n    }\n\n    VisibilityMonitor.getInstance().on('visible', this.onVisible_, this);\n\n    if (repoInfo_.host.indexOf('fblocal') === -1) {\n      OnlineMonitor.getInstance().on('online', this.onOnline_, this);\n    }\n  }\n\n  protected sendRequest(\n    action: string,\n    body: unknown,\n    onResponse?: (a: unknown) => void\n  ) {\n    const curReqNum = ++this.requestNumber_;\n\n    const msg = { r: curReqNum, a: action, b: body };\n    this.log_(stringify(msg));\n    assert(\n      this.connected_,\n      \"sendRequest call when we're not connected not allowed.\"\n    );\n    this.realtime_.sendRequest(msg);\n    if (onResponse) {\n      this.requestCBHash_[curReqNum] = onResponse;\n    }\n  }\n\n  get(query: QueryContext): Promise<string> {\n    this.initConnection_();\n\n    const deferred = new Deferred<string>();\n    const request = {\n      p: query._path.toString(),\n      q: query._queryObject\n    };\n    const outstandingGet = {\n      action: 'g',\n      request,\n      onComplete: (message: { [k: string]: unknown }) => {\n        const payload = message['d'] as string;\n        if (message['s'] === 'ok') {\n          this.onDataUpdate_(\n            request['p'],\n            payload,\n            /*isMerge*/ false,\n            /*tag*/ null\n          );\n          deferred.resolve(payload);\n        } else {\n          deferred.reject(payload);\n        }\n      }\n    };\n    this.outstandingGets_.push(outstandingGet);\n    this.outstandingGetCount_++;\n    const index = this.outstandingGets_.length - 1;\n\n    if (!this.connected_) {\n      setTimeout(() => {\n        const get = this.outstandingGets_[index];\n        if (get === undefined || outstandingGet !== get) {\n          return;\n        }\n        delete this.outstandingGets_[index];\n        this.outstandingGetCount_--;\n        if (this.outstandingGetCount_ === 0) {\n          this.outstandingGets_ = [];\n        }\n        this.log_('get ' + index + ' timed out on connection');\n        deferred.reject(new Error('Client is offline.'));\n      }, GET_CONNECT_TIMEOUT);\n    }\n\n    if (this.connected_) {\n      this.sendGet_(index);\n    }\n\n    return deferred.promise;\n  }\n\n  listen(\n    query: QueryContext,\n    currentHashFn: () => string,\n    tag: number | null,\n    onComplete: (a: string, b: unknown) => void\n  ) {\n    this.initConnection_();\n\n    const queryId = query._queryIdentifier;\n    const pathString = query._path.toString();\n    this.log_('Listen called for ' + pathString + ' ' + queryId);\n    if (!this.listens.has(pathString)) {\n      this.listens.set(pathString, new Map());\n    }\n    assert(\n      query._queryParams.isDefault() || !query._queryParams.loadsAllData(),\n      'listen() called for non-default but complete query'\n    );\n    assert(\n      !this.listens.get(pathString)!.has(queryId),\n      'listen() called twice for same path/queryId.'\n    );\n    const listenSpec: ListenSpec = {\n      onComplete,\n      hashFn: currentHashFn,\n      query,\n      tag\n    };\n    this.listens.get(pathString)!.set(queryId, listenSpec);\n\n    if (this.connected_) {\n      this.sendListen_(listenSpec);\n    }\n  }\n\n  private sendGet_(index: number) {\n    const get = this.outstandingGets_[index];\n    this.sendRequest('g', get.request, (message: { [k: string]: unknown }) => {\n      delete this.outstandingGets_[index];\n      this.outstandingGetCount_--;\n      if (this.outstandingGetCount_ === 0) {\n        this.outstandingGets_ = [];\n      }\n      if (get.onComplete) {\n        get.onComplete(message);\n      }\n    });\n  }\n\n  private sendListen_(listenSpec: ListenSpec) {\n    const query = listenSpec.query;\n    const pathString = query._path.toString();\n    const queryId = query._queryIdentifier;\n    this.log_('Listen on ' + pathString + ' for ' + queryId);\n    const req: { [k: string]: unknown } = { /*path*/ p: pathString };\n\n    const action = 'q';\n\n    // Only bother to send query if it's non-default.\n    if (listenSpec.tag) {\n      req['q'] = query._queryObject;\n      req['t'] = listenSpec.tag;\n    }\n\n    req[/*hash*/ 'h'] = listenSpec.hashFn();\n\n    this.sendRequest(action, req, (message: { [k: string]: unknown }) => {\n      const payload: unknown = message[/*data*/ 'd'];\n      const status = message[/*status*/ 's'] as string;\n\n      // print warnings in any case...\n      PersistentConnection.warnOnListenWarnings_(payload, query);\n\n      const currentListenSpec =\n        this.listens.get(pathString) &&\n        this.listens.get(pathString)!.get(queryId);\n      // only trigger actions if the listen hasn't been removed and readded\n      if (currentListenSpec === listenSpec) {\n        this.log_('listen response', message);\n\n        if (status !== 'ok') {\n          this.removeListen_(pathString, queryId);\n        }\n\n        if (listenSpec.onComplete) {\n          listenSpec.onComplete(status, payload);\n        }\n      }\n    });\n  }\n\n  private static warnOnListenWarnings_(payload: unknown, query: QueryContext) {\n    if (payload && typeof payload === 'object' && contains(payload, 'w')) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const warnings = safeGet(payload as any, 'w');\n      if (Array.isArray(warnings) && ~warnings.indexOf('no_index')) {\n        const indexSpec =\n          '\".indexOn\": \"' + query._queryParams.getIndex().toString() + '\"';\n        const indexPath = query._path.toString();\n        warn(\n          `Using an unspecified index. Your data will be downloaded and ` +\n            `filtered on the client. Consider adding ${indexSpec} at ` +\n            `${indexPath} to your security rules for better performance.`\n        );\n      }\n    }\n  }\n\n  refreshAuthToken(token: string) {\n    this.authToken_ = token;\n    this.log_('Auth token refreshed');\n    if (this.authToken_) {\n      this.tryAuth();\n    } else {\n      //If we're connected we want to let the server know to unauthenticate us. If we're not connected, simply delete\n      //the credential so we dont become authenticated next time we connect.\n      if (this.connected_) {\n        this.sendRequest('unauth', {}, () => {});\n      }\n    }\n\n    this.reduceReconnectDelayIfAdminCredential_(token);\n  }\n\n  private reduceReconnectDelayIfAdminCredential_(credential: string) {\n    // NOTE: This isn't intended to be bulletproof (a malicious developer can always just modify the client).\n    // Additionally, we don't bother resetting the max delay back to the default if auth fails / expires.\n    const isFirebaseSecret = credential && credential.length === 40;\n    if (isFirebaseSecret || isAdmin(credential)) {\n      this.log_(\n        'Admin auth credential detected.  Reducing max reconnect time.'\n      );\n      this.maxReconnectDelay_ = RECONNECT_MAX_DELAY_FOR_ADMINS;\n    }\n  }\n\n  refreshAppCheckToken(token: string | null) {\n    this.appCheckToken_ = token;\n    this.log_('App check token refreshed');\n    if (this.appCheckToken_) {\n      this.tryAppCheck();\n    } else {\n      //If we're connected we want to let the server know to unauthenticate us.\n      //If we're not connected, simply delete the credential so we dont become\n      // authenticated next time we connect.\n      if (this.connected_) {\n        this.sendRequest('unappeck', {}, () => {});\n      }\n    }\n  }\n\n  /**\n   * Attempts to authenticate with the given credentials. If the authentication attempt fails, it's triggered like\n   * a auth revoked (the connection is closed).\n   */\n  tryAuth() {\n    if (this.connected_ && this.authToken_) {\n      const token = this.authToken_;\n      const authMethod = isValidFormat(token) ? 'auth' : 'gauth';\n      const requestData: { [k: string]: unknown } = { cred: token };\n      if (this.authOverride_ === null) {\n        requestData['noauth'] = true;\n      } else if (typeof this.authOverride_ === 'object') {\n        requestData['authvar'] = this.authOverride_;\n      }\n      this.sendRequest(\n        authMethod,\n        requestData,\n        (res: { [k: string]: unknown }) => {\n          const status = res[/*status*/ 's'] as string;\n          const data = (res[/*data*/ 'd'] as string) || 'error';\n\n          if (this.authToken_ === token) {\n            if (status === 'ok') {\n              this.invalidAuthTokenCount_ = 0;\n            } else {\n              // Triggers reconnect and force refresh for auth token\n              this.onAuthRevoked_(status, data);\n            }\n          }\n        }\n      );\n    }\n  }\n\n  /**\n   * Attempts to authenticate with the given token. If the authentication\n   * attempt fails, it's triggered like the token was revoked (the connection is\n   * closed).\n   */\n  tryAppCheck() {\n    if (this.connected_ && this.appCheckToken_) {\n      this.sendRequest(\n        'appcheck',\n        { 'token': this.appCheckToken_ },\n        (res: { [k: string]: unknown }) => {\n          const status = res[/*status*/ 's'] as string;\n          const data = (res[/*data*/ 'd'] as string) || 'error';\n          if (status === 'ok') {\n            this.invalidAppCheckTokenCount_ = 0;\n          } else {\n            this.onAppCheckRevoked_(status, data);\n          }\n        }\n      );\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  unlisten(query: QueryContext, tag: number | null) {\n    const pathString = query._path.toString();\n    const queryId = query._queryIdentifier;\n\n    this.log_('Unlisten called for ' + pathString + ' ' + queryId);\n\n    assert(\n      query._queryParams.isDefault() || !query._queryParams.loadsAllData(),\n      'unlisten() called for non-default but complete query'\n    );\n    const listen = this.removeListen_(pathString, queryId);\n    if (listen && this.connected_) {\n      this.sendUnlisten_(pathString, queryId, query._queryObject, tag);\n    }\n  }\n\n  private sendUnlisten_(\n    pathString: string,\n    queryId: string,\n    queryObj: object,\n    tag: number | null\n  ) {\n    this.log_('Unlisten on ' + pathString + ' for ' + queryId);\n\n    const req: { [k: string]: unknown } = { /*path*/ p: pathString };\n    const action = 'n';\n    // Only bother sending queryId if it's non-default.\n    if (tag) {\n      req['q'] = queryObj;\n      req['t'] = tag;\n    }\n\n    this.sendRequest(action, req);\n  }\n\n  onDisconnectPut(\n    pathString: string,\n    data: unknown,\n    onComplete?: (a: string, b: string) => void\n  ) {\n    this.initConnection_();\n\n    if (this.connected_) {\n      this.sendOnDisconnect_('o', pathString, data, onComplete);\n    } else {\n      this.onDisconnectRequestQueue_.push({\n        pathString,\n        action: 'o',\n        data,\n        onComplete\n      });\n    }\n  }\n\n  onDisconnectMerge(\n    pathString: string,\n    data: unknown,\n    onComplete?: (a: string, b: string) => void\n  ) {\n    this.initConnection_();\n\n    if (this.connected_) {\n      this.sendOnDisconnect_('om', pathString, data, onComplete);\n    } else {\n      this.onDisconnectRequestQueue_.push({\n        pathString,\n        action: 'om',\n        data,\n        onComplete\n      });\n    }\n  }\n\n  onDisconnectCancel(\n    pathString: string,\n    onComplete?: (a: string, b: string) => void\n  ) {\n    this.initConnection_();\n\n    if (this.connected_) {\n      this.sendOnDisconnect_('oc', pathString, null, onComplete);\n    } else {\n      this.onDisconnectRequestQueue_.push({\n        pathString,\n        action: 'oc',\n        data: null,\n        onComplete\n      });\n    }\n  }\n\n  private sendOnDisconnect_(\n    action: string,\n    pathString: string,\n    data: unknown,\n    onComplete: (a: string, b: string) => void\n  ) {\n    const request = { /*path*/ p: pathString, /*data*/ d: data };\n    this.log_('onDisconnect ' + action, request);\n    this.sendRequest(action, request, (response: { [k: string]: unknown }) => {\n      if (onComplete) {\n        setTimeout(() => {\n          onComplete(\n            response[/*status*/ 's'] as string,\n            response[/* data */ 'd'] as string\n          );\n        }, Math.floor(0));\n      }\n    });\n  }\n\n  put(\n    pathString: string,\n    data: unknown,\n    onComplete?: (a: string, b: string) => void,\n    hash?: string\n  ) {\n    this.putInternal('p', pathString, data, onComplete, hash);\n  }\n\n  merge(\n    pathString: string,\n    data: unknown,\n    onComplete: (a: string, b: string | null) => void,\n    hash?: string\n  ) {\n    this.putInternal('m', pathString, data, onComplete, hash);\n  }\n\n  putInternal(\n    action: string,\n    pathString: string,\n    data: unknown,\n    onComplete: (a: string, b: string | null) => void,\n    hash?: string\n  ) {\n    this.initConnection_();\n\n    const request: { [k: string]: unknown } = {\n      /*path*/ p: pathString,\n      /*data*/ d: data\n    };\n\n    if (hash !== undefined) {\n      request[/*hash*/ 'h'] = hash;\n    }\n\n    // TODO: Only keep track of the most recent put for a given path?\n    this.outstandingPuts_.push({\n      action,\n      request,\n      onComplete\n    });\n\n    this.outstandingPutCount_++;\n    const index = this.outstandingPuts_.length - 1;\n\n    if (this.connected_) {\n      this.sendPut_(index);\n    } else {\n      this.log_('Buffering put: ' + pathString);\n    }\n  }\n\n  private sendPut_(index: number) {\n    const action = this.outstandingPuts_[index].action;\n    const request = this.outstandingPuts_[index].request;\n    const onComplete = this.outstandingPuts_[index].onComplete;\n    this.outstandingPuts_[index].queued = this.connected_;\n\n    this.sendRequest(action, request, (message: { [k: string]: unknown }) => {\n      this.log_(action + ' response', message);\n\n      delete this.outstandingPuts_[index];\n      this.outstandingPutCount_--;\n\n      // Clean up array occasionally.\n      if (this.outstandingPutCount_ === 0) {\n        this.outstandingPuts_ = [];\n      }\n\n      if (onComplete) {\n        onComplete(\n          message[/*status*/ 's'] as string,\n          message[/* data */ 'd'] as string\n        );\n      }\n    });\n  }\n\n  reportStats(stats: { [k: string]: unknown }) {\n    // If we're not connected, we just drop the stats.\n    if (this.connected_) {\n      const request = { /*counters*/ c: stats };\n      this.log_('reportStats', request);\n\n      this.sendRequest(/*stats*/ 's', request, result => {\n        const status = result[/*status*/ 's'];\n        if (status !== 'ok') {\n          const errorReason = result[/* data */ 'd'];\n          this.log_('reportStats', 'Error sending stats: ' + errorReason);\n        }\n      });\n    }\n  }\n\n  private onDataMessage_(message: { [k: string]: unknown }) {\n    if ('r' in message) {\n      // this is a response\n      this.log_('from server: ' + stringify(message));\n      const reqNum = message['r'] as string;\n      const onResponse = this.requestCBHash_[reqNum];\n      if (onResponse) {\n        delete this.requestCBHash_[reqNum];\n        onResponse(message[/*body*/ 'b']);\n      }\n    } else if ('error' in message) {\n      throw 'A server-side error has occurred: ' + message['error'];\n    } else if ('a' in message) {\n      // a and b are action and body, respectively\n      this.onDataPush_(message['a'] as string, message['b'] as {});\n    }\n  }\n\n  private onDataPush_(action: string, body: { [k: string]: unknown }) {\n    this.log_('handleServerMessage', action, body);\n    if (action === 'd') {\n      this.onDataUpdate_(\n        body[/*path*/ 'p'] as string,\n        body[/*data*/ 'd'],\n        /*isMerge*/ false,\n        body['t'] as number\n      );\n    } else if (action === 'm') {\n      this.onDataUpdate_(\n        body[/*path*/ 'p'] as string,\n        body[/*data*/ 'd'],\n        /*isMerge=*/ true,\n        body['t'] as number\n      );\n    } else if (action === 'c') {\n      this.onListenRevoked_(\n        body[/*path*/ 'p'] as string,\n        body[/*query*/ 'q'] as unknown[]\n      );\n    } else if (action === 'ac') {\n      this.onAuthRevoked_(\n        body[/*status code*/ 's'] as string,\n        body[/* explanation */ 'd'] as string\n      );\n    } else if (action === 'apc') {\n      this.onAppCheckRevoked_(\n        body[/*status code*/ 's'] as string,\n        body[/* explanation */ 'd'] as string\n      );\n    } else if (action === 'sd') {\n      this.onSecurityDebugPacket_(body);\n    } else {\n      error(\n        'Unrecognized action received from server: ' +\n          stringify(action) +\n          '\\nAre you using the latest client?'\n      );\n    }\n  }\n\n  private onReady_(timestamp: number, sessionId: string) {\n    this.log_('connection ready');\n    this.connected_ = true;\n    this.lastConnectionEstablishedTime_ = new Date().getTime();\n    this.handleTimestamp_(timestamp);\n    this.lastSessionId = sessionId;\n    if (this.firstConnection_) {\n      this.sendConnectStats_();\n    }\n    this.restoreState_();\n    this.firstConnection_ = false;\n    this.onConnectStatus_(true);\n  }\n\n  private scheduleConnect_(timeout: number) {\n    assert(\n      !this.realtime_,\n      \"Scheduling a connect when we're already connected/ing?\"\n    );\n\n    if (this.establishConnectionTimer_) {\n      clearTimeout(this.establishConnectionTimer_);\n    }\n\n    // NOTE: Even when timeout is 0, it's important to do a setTimeout to work around an infuriating \"Security Error\" in\n    // Firefox when trying to write to our long-polling iframe in some scenarios (e.g. Forge or our unit tests).\n\n    this.establishConnectionTimer_ = setTimeout(() => {\n      this.establishConnectionTimer_ = null;\n      this.establishConnection_();\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    }, Math.floor(timeout)) as any;\n  }\n\n  private initConnection_() {\n    if (!this.realtime_ && this.firstConnection_) {\n      this.scheduleConnect_(0);\n    }\n  }\n\n  private onVisible_(visible: boolean) {\n    // NOTE: Tabbing away and back to a window will defeat our reconnect backoff, but I think that's fine.\n    if (\n      visible &&\n      !this.visible_ &&\n      this.reconnectDelay_ === this.maxReconnectDelay_\n    ) {\n      this.log_('Window became visible.  Reducing delay.');\n      this.reconnectDelay_ = RECONNECT_MIN_DELAY;\n\n      if (!this.realtime_) {\n        this.scheduleConnect_(0);\n      }\n    }\n    this.visible_ = visible;\n  }\n\n  private onOnline_(online: boolean) {\n    if (online) {\n      this.log_('Browser went online.');\n      this.reconnectDelay_ = RECONNECT_MIN_DELAY;\n      if (!this.realtime_) {\n        this.scheduleConnect_(0);\n      }\n    } else {\n      this.log_('Browser went offline.  Killing connection.');\n      if (this.realtime_) {\n        this.realtime_.close();\n      }\n    }\n  }\n\n  private onRealtimeDisconnect_() {\n    this.log_('data client disconnected');\n    this.connected_ = false;\n    this.realtime_ = null;\n\n    // Since we don't know if our sent transactions succeeded or not, we need to cancel them.\n    this.cancelSentTransactions_();\n\n    // Clear out the pending requests.\n    this.requestCBHash_ = {};\n\n    if (this.shouldReconnect_()) {\n      if (!this.visible_) {\n        this.log_(\"Window isn't visible.  Delaying reconnect.\");\n        this.reconnectDelay_ = this.maxReconnectDelay_;\n        this.lastConnectionAttemptTime_ = new Date().getTime();\n      } else if (this.lastConnectionEstablishedTime_) {\n        // If we've been connected long enough, reset reconnect delay to minimum.\n        const timeSinceLastConnectSucceeded =\n          new Date().getTime() - this.lastConnectionEstablishedTime_;\n        if (timeSinceLastConnectSucceeded > RECONNECT_DELAY_RESET_TIMEOUT) {\n          this.reconnectDelay_ = RECONNECT_MIN_DELAY;\n        }\n        this.lastConnectionEstablishedTime_ = null;\n      }\n\n      const timeSinceLastConnectAttempt =\n        new Date().getTime() - this.lastConnectionAttemptTime_;\n      let reconnectDelay = Math.max(\n        0,\n        this.reconnectDelay_ - timeSinceLastConnectAttempt\n      );\n      reconnectDelay = Math.random() * reconnectDelay;\n\n      this.log_('Trying to reconnect in ' + reconnectDelay + 'ms');\n      this.scheduleConnect_(reconnectDelay);\n\n      // Adjust reconnect delay for next time.\n      this.reconnectDelay_ = Math.min(\n        this.maxReconnectDelay_,\n        this.reconnectDelay_ * RECONNECT_DELAY_MULTIPLIER\n      );\n    }\n    this.onConnectStatus_(false);\n  }\n\n  private async establishConnection_() {\n    if (this.shouldReconnect_()) {\n      this.log_('Making a connection attempt');\n      this.lastConnectionAttemptTime_ = new Date().getTime();\n      this.lastConnectionEstablishedTime_ = null;\n      const onDataMessage = this.onDataMessage_.bind(this);\n      const onReady = this.onReady_.bind(this);\n      const onDisconnect = this.onRealtimeDisconnect_.bind(this);\n      const connId = this.id + ':' + PersistentConnection.nextConnectionId_++;\n      const lastSessionId = this.lastSessionId;\n      let canceled = false;\n      let connection: Connection | null = null;\n      const closeFn = function () {\n        if (connection) {\n          connection.close();\n        } else {\n          canceled = true;\n          onDisconnect();\n        }\n      };\n      const sendRequestFn = function (msg: object) {\n        assert(\n          connection,\n          \"sendRequest call when we're not connected not allowed.\"\n        );\n        connection.sendRequest(msg);\n      };\n\n      this.realtime_ = {\n        close: closeFn,\n        sendRequest: sendRequestFn\n      };\n\n      const forceRefresh = this.forceTokenRefresh_;\n      this.forceTokenRefresh_ = false;\n\n      try {\n        // First fetch auth and app check token, and establish connection after\n        // fetching the token was successful\n        const [authToken, appCheckToken] = await Promise.all([\n          this.authTokenProvider_.getToken(forceRefresh),\n          this.appCheckTokenProvider_.getToken(forceRefresh)\n        ]);\n\n        if (!canceled) {\n          log('getToken() completed. Creating connection.');\n          this.authToken_ = authToken && authToken.accessToken;\n          this.appCheckToken_ = appCheckToken && appCheckToken.token;\n          connection = new Connection(\n            connId,\n            this.repoInfo_,\n            this.applicationId_,\n            this.appCheckToken_,\n            this.authToken_,\n            onDataMessage,\n            onReady,\n            onDisconnect,\n            /* onKill= */ reason => {\n              warn(reason + ' (' + this.repoInfo_.toString() + ')');\n              this.interrupt(SERVER_KILL_INTERRUPT_REASON);\n            },\n            lastSessionId\n          );\n        } else {\n          log('getToken() completed but was canceled');\n        }\n      } catch (error) {\n        this.log_('Failed to get token: ' + error);\n        if (!canceled) {\n          if (this.repoInfo_.nodeAdmin) {\n            // This may be a critical error for the Admin Node.js SDK, so log a warning.\n            // But getToken() may also just have temporarily failed, so we still want to\n            // continue retrying.\n            warn(error);\n          }\n          closeFn();\n        }\n      }\n    }\n  }\n\n  interrupt(reason: string) {\n    log('Interrupting connection for reason: ' + reason);\n    this.interruptReasons_[reason] = true;\n    if (this.realtime_) {\n      this.realtime_.close();\n    } else {\n      if (this.establishConnectionTimer_) {\n        clearTimeout(this.establishConnectionTimer_);\n        this.establishConnectionTimer_ = null;\n      }\n      if (this.connected_) {\n        this.onRealtimeDisconnect_();\n      }\n    }\n  }\n\n  resume(reason: string) {\n    log('Resuming connection for reason: ' + reason);\n    delete this.interruptReasons_[reason];\n    if (isEmpty(this.interruptReasons_)) {\n      this.reconnectDelay_ = RECONNECT_MIN_DELAY;\n      if (!this.realtime_) {\n        this.scheduleConnect_(0);\n      }\n    }\n  }\n\n  private handleTimestamp_(timestamp: number) {\n    const delta = timestamp - new Date().getTime();\n    this.onServerInfoUpdate_({ serverTimeOffset: delta });\n  }\n\n  private cancelSentTransactions_() {\n    for (let i = 0; i < this.outstandingPuts_.length; i++) {\n      const put = this.outstandingPuts_[i];\n      if (put && /*hash*/ 'h' in put.request && put.queued) {\n        if (put.onComplete) {\n          put.onComplete('disconnect');\n        }\n\n        delete this.outstandingPuts_[i];\n        this.outstandingPutCount_--;\n      }\n    }\n\n    // Clean up array occasionally.\n    if (this.outstandingPutCount_ === 0) {\n      this.outstandingPuts_ = [];\n    }\n  }\n\n  private onListenRevoked_(pathString: string, query?: unknown[]) {\n    // Remove the listen and manufacture a \"permission_denied\" error for the failed listen.\n    let queryId;\n    if (!query) {\n      queryId = 'default';\n    } else {\n      queryId = query.map(q => ObjectToUniqueKey(q)).join('$');\n    }\n    const listen = this.removeListen_(pathString, queryId);\n    if (listen && listen.onComplete) {\n      listen.onComplete('permission_denied');\n    }\n  }\n\n  private removeListen_(pathString: string, queryId: string): ListenSpec {\n    const normalizedPathString = new Path(pathString).toString(); // normalize path.\n    let listen;\n    if (this.listens.has(normalizedPathString)) {\n      const map = this.listens.get(normalizedPathString)!;\n      listen = map.get(queryId);\n      map.delete(queryId);\n      if (map.size === 0) {\n        this.listens.delete(normalizedPathString);\n      }\n    } else {\n      // all listens for this path has already been removed\n      listen = undefined;\n    }\n    return listen;\n  }\n\n  private onAuthRevoked_(statusCode: string, explanation: string) {\n    log('Auth token revoked: ' + statusCode + '/' + explanation);\n    this.authToken_ = null;\n    this.forceTokenRefresh_ = true;\n    this.realtime_.close();\n    if (statusCode === 'invalid_token' || statusCode === 'permission_denied') {\n      // We'll wait a couple times before logging the warning / increasing the\n      // retry period since oauth tokens will report as \"invalid\" if they're\n      // just expired. Plus there may be transient issues that resolve themselves.\n      this.invalidAuthTokenCount_++;\n      if (this.invalidAuthTokenCount_ >= INVALID_TOKEN_THRESHOLD) {\n        // Set a long reconnect delay because recovery is unlikely\n        this.reconnectDelay_ = RECONNECT_MAX_DELAY_FOR_ADMINS;\n\n        // Notify the auth token provider that the token is invalid, which will log\n        // a warning\n        this.authTokenProvider_.notifyForInvalidToken();\n      }\n    }\n  }\n\n  private onAppCheckRevoked_(statusCode: string, explanation: string) {\n    log('App check token revoked: ' + statusCode + '/' + explanation);\n    this.appCheckToken_ = null;\n    this.forceTokenRefresh_ = true;\n    // Note: We don't close the connection as the developer may not have\n    // enforcement enabled. The backend closes connections with enforcements.\n    if (statusCode === 'invalid_token' || statusCode === 'permission_denied') {\n      // We'll wait a couple times before logging the warning / increasing the\n      // retry period since oauth tokens will report as \"invalid\" if they're\n      // just expired. Plus there may be transient issues that resolve themselves.\n      this.invalidAppCheckTokenCount_++;\n      if (this.invalidAppCheckTokenCount_ >= INVALID_TOKEN_THRESHOLD) {\n        this.appCheckTokenProvider_.notifyForInvalidToken();\n      }\n    }\n  }\n\n  private onSecurityDebugPacket_(body: { [k: string]: unknown }) {\n    if (this.securityDebugCallback_) {\n      this.securityDebugCallback_(body);\n    } else {\n      if ('msg' in body) {\n        console.log(\n          'FIREBASE: ' + (body['msg'] as string).replace('\\n', '\\nFIREBASE: ')\n        );\n      }\n    }\n  }\n\n  private restoreState_() {\n    //Re-authenticate ourselves if we have a credential stored.\n    this.tryAuth();\n    this.tryAppCheck();\n\n    // Puts depend on having received the corresponding data update from the server before they complete, so we must\n    // make sure to send listens before puts.\n    for (const queries of this.listens.values()) {\n      for (const listenSpec of queries.values()) {\n        this.sendListen_(listenSpec);\n      }\n    }\n\n    for (let i = 0; i < this.outstandingPuts_.length; i++) {\n      if (this.outstandingPuts_[i]) {\n        this.sendPut_(i);\n      }\n    }\n\n    while (this.onDisconnectRequestQueue_.length) {\n      const request = this.onDisconnectRequestQueue_.shift();\n      this.sendOnDisconnect_(\n        request.action,\n        request.pathString,\n        request.data,\n        request.onComplete\n      );\n    }\n\n    for (let i = 0; i < this.outstandingGets_.length; i++) {\n      if (this.outstandingGets_[i]) {\n        this.sendGet_(i);\n      }\n    }\n  }\n\n  /**\n   * Sends client stats for first connection\n   */\n  private sendConnectStats_() {\n    const stats: { [k: string]: number } = {};\n\n    let clientName = 'js';\n    if (isNodeSdk()) {\n      if (this.repoInfo_.nodeAdmin) {\n        clientName = 'admin_node';\n      } else {\n        clientName = 'node';\n      }\n    }\n\n    stats['sdk.' + clientName + '.' + SDK_VERSION.replace(/\\./g, '-')] = 1;\n\n    if (isMobileCordova()) {\n      stats['framework.cordova'] = 1;\n    } else if (isReactNative()) {\n      stats['framework.reactnative'] = 1;\n    }\n    this.reportStats(stats);\n  }\n\n  private shouldReconnect_(): boolean {\n    const online = OnlineMonitor.getInstance().currentlyOnline();\n    return isEmpty(this.interruptReasons_) && online;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { each } from '../util/util';\n\nimport { StatsCollection } from './StatsCollection';\n\n/**\n * Returns the delta from the previous call to get stats.\n *\n * @param collection_ - The collection to \"listen\" to.\n */\nexport class StatsListener {\n  private last_: { [k: string]: number } | null = null;\n\n  constructor(private collection_: StatsCollection) {}\n\n  get(): { [k: string]: number } {\n    const newStats = this.collection_.get();\n\n    const delta = { ...newStats };\n    if (this.last_) {\n      each(this.last_, (stat: string, value: number) => {\n        delta[stat] = delta[stat] - value;\n      });\n    }\n    this.last_ = newStats;\n\n    return delta;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { contains } from '@firebase/util';\n\nimport { ServerActions } from '../ServerActions';\nimport { setTimeoutNonBlocking, each } from '../util/util';\n\nimport { StatsCollection } from './StatsCollection';\nimport { StatsListener } from './StatsListener';\n\n// Assuming some apps may have a short amount of time on page, and a bulk of firebase operations probably\n// happen on page load, we try to report our first set of stats pretty quickly, but we wait at least 10\n// seconds to try to ensure the Firebase connection is established / settled.\nconst FIRST_STATS_MIN_TIME = 10 * 1000;\nconst FIRST_STATS_MAX_TIME = 30 * 1000;\n\n// We'll continue to report stats on average every 5 minutes.\nconst REPORT_STATS_INTERVAL = 5 * 60 * 1000;\n\nexport class StatsReporter {\n  private statsListener_: StatsListener;\n  statsToReport_: { [k: string]: boolean } = {};\n\n  constructor(collection: StatsCollection, private server_: ServerActions) {\n    this.statsListener_ = new StatsListener(collection);\n\n    const timeout =\n      FIRST_STATS_MIN_TIME +\n      (FIRST_STATS_MAX_TIME - FIRST_STATS_MIN_TIME) * Math.random();\n    setTimeoutNonBlocking(this.reportStats_.bind(this), Math.floor(timeout));\n  }\n\n  private reportStats_() {\n    const stats = this.statsListener_.get();\n    const reportedStats: typeof stats = {};\n    let haveStatsToReport = false;\n\n    each(stats, (stat: string, value: number) => {\n      if (value > 0 && contains(this.statsToReport_, stat)) {\n        reportedStats[stat] = value;\n        haveStatsToReport = true;\n      }\n    });\n\n    if (haveStatsToReport) {\n      this.server_.reportStats(reportedStats);\n    }\n\n    // queue our next run.\n    setTimeoutNonBlocking(\n      this.reportStats_.bind(this),\n      Math.floor(Math.random() * 2 * REPORT_STATS_INTERVAL)\n    );\n  }\n}\n\nexport function statsReporterIncludeStat(\n  reporter: StatsReporter,\n  stat: string\n) {\n  reporter.statsToReport_[stat] = true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  assert,\n  contains,\n  isEmpty,\n  map,\n  safeGet,\n  stringify\n} from '@firebase/util';\n\nimport { AppCheckTokenProvider } from './AppCheckTokenProvider';\nimport { AuthTokenProvider } from './AuthTokenProvider';\nimport { PersistentConnection } from './PersistentConnection';\nimport { ReadonlyRestClient } from './ReadonlyRestClient';\nimport { RepoInfo } from './RepoInfo';\nimport { ServerActions } from './ServerActions';\nimport { ChildrenNode } from './snap/ChildrenNode';\nimport { Node } from './snap/Node';\nimport { nodeFromJSON } from './snap/nodeFromJSON';\nimport { SnapshotHolder } from './SnapshotHolder';\nimport {\n  newSparseSnapshotTree,\n  SparseSnapshotTree,\n  sparseSnapshotTreeForEachTree,\n  sparseSnapshotTreeForget,\n  sparseSnapshotTreeRemember\n} from './SparseSnapshotTree';\nimport { StatsCollection } from './stats/StatsCollection';\nimport { StatsListener } from './stats/StatsListener';\nimport {\n  statsManagerGetCollection,\n  statsManagerGetOrCreateReporter\n} from './stats/StatsManager';\nimport { StatsReporter, statsReporterIncludeStat } from './stats/StatsReporter';\nimport {\n  SyncTree,\n  syncTreeAckUserWrite,\n  syncTreeAddEventRegistration,\n  syncTreeApplyServerMerge,\n  syncTreeApplyServerOverwrite,\n  syncTreeApplyTaggedQueryMerge,\n  syncTreeApplyTaggedQueryOverwrite,\n  syncTreeApplyUserMerge,\n  syncTreeApplyUserOverwrite,\n  syncTreeCalcCompleteEventCache,\n  syncTreeGetServerValue,\n  syncTreeRemoveEventRegistration\n} from './SyncTree';\nimport { Indexable } from './util/misc';\nimport {\n  newEmptyPath,\n  newRelativePath,\n  Path,\n  pathChild,\n  pathGetFront,\n  pathPopFront\n} from './util/Path';\nimport {\n  generateWithValues,\n  resolveDeferredValueSnapshot,\n  resolveDeferredValueTree\n} from './util/ServerValues';\nimport {\n  Tree,\n  treeForEachAncestor,\n  treeForEachChild,\n  treeForEachDescendant,\n  treeGetPath,\n  treeGetValue,\n  treeHasChildren,\n  treeSetValue,\n  treeSubTree\n} from './util/Tree';\nimport {\n  beingCrawled,\n  each,\n  exceptionGuard,\n  log,\n  LUIDGenerator,\n  warn\n} from './util/util';\nimport { isValidPriority, validateFirebaseData } from './util/validation';\nimport { Event } from './view/Event';\nimport {\n  EventQueue,\n  eventQueueQueueEvents,\n  eventQueueRaiseEventsAtPath,\n  eventQueueRaiseEventsForChangedPath\n} from './view/EventQueue';\nimport { EventRegistration, QueryContext } from './view/EventRegistration';\n\nconst INTERRUPT_REASON = 'repo_interrupt';\n\n/**\n * If a transaction does not succeed after 25 retries, we abort it. Among other\n * things this ensure that if there's ever a bug causing a mismatch between\n * client / server hashes for some data, we won't retry indefinitely.\n */\nconst MAX_TRANSACTION_RETRIES = 25;\n\nconst enum TransactionStatus {\n  // We've run the transaction and updated transactionResultData_ with the result, but it isn't currently sent to the\n  // server. A transaction will go from RUN -> SENT -> RUN if it comes back from the server as rejected due to\n  // mismatched hash.\n  RUN,\n\n  // We've run the transaction and sent it to the server and it's currently outstanding (hasn't come back as accepted\n  // or rejected yet).\n  SENT,\n\n  // Temporary state used to mark completed transactions (whether successful or aborted).  The transaction will be\n  // removed when we get a chance to prune completed ones.\n  COMPLETED,\n\n  // Used when an already-sent transaction needs to be aborted (e.g. due to a conflicting set() call that was made).\n  // If it comes back as unsuccessful, we'll abort it.\n  SENT_NEEDS_ABORT,\n\n  // Temporary state used to mark transactions that need to be aborted.\n  NEEDS_ABORT\n}\n\ninterface Transaction {\n  path: Path;\n  update: (a: unknown) => unknown;\n  onComplete: (\n    error: Error | null,\n    committed: boolean,\n    node: Node | null\n  ) => void;\n  status: TransactionStatus;\n  order: number;\n  applyLocally: boolean;\n  retryCount: number;\n  unwatcher: () => void;\n  abortReason: string | null;\n  currentWriteId: number;\n  currentInputSnapshot: Node | null;\n  currentOutputSnapshotRaw: Node | null;\n  currentOutputSnapshotResolved: Node | null;\n}\n\n/**\n * A connection to a single data repository.\n */\nexport class Repo {\n  /** Key for uniquely identifying this repo, used in RepoManager */\n  readonly key: string;\n\n  dataUpdateCount = 0;\n  infoSyncTree_: SyncTree;\n  serverSyncTree_: SyncTree;\n\n  stats_: StatsCollection;\n  statsListener_: StatsListener | null = null;\n  eventQueue_ = new EventQueue();\n  nextWriteId_ = 1;\n  server_: ServerActions;\n  statsReporter_: StatsReporter;\n  infoData_: SnapshotHolder;\n  interceptServerDataCallback_: ((a: string, b: unknown) => void) | null = null;\n\n  /** A list of data pieces and paths to be set when this client disconnects. */\n  onDisconnect_: SparseSnapshotTree = newSparseSnapshotTree();\n\n  /** Stores queues of outstanding transactions for Firebase locations. */\n  transactionQueueTree_ = new Tree<Transaction[]>();\n\n  // TODO: This should be @private but it's used by test_access.js and internal.js\n  persistentConnection_: PersistentConnection | null = null;\n\n  constructor(\n    public repoInfo_: RepoInfo,\n    public forceRestClient_: boolean,\n    public authTokenProvider_: AuthTokenProvider,\n    public appCheckProvider_: AppCheckTokenProvider\n  ) {\n    // This key is intentionally not updated if RepoInfo is later changed or replaced\n    this.key = this.repoInfo_.toURLString();\n  }\n\n  /**\n   * @returns The URL corresponding to the root of this Firebase.\n   */\n  toString(): string {\n    return (\n      (this.repoInfo_.secure ? 'https://' : 'http://') + this.repoInfo_.host\n    );\n  }\n}\n\nexport function repoStart(\n  repo: Repo,\n  appId: string,\n  authOverride?: object\n): void {\n  repo.stats_ = statsManagerGetCollection(repo.repoInfo_);\n\n  if (repo.forceRestClient_ || beingCrawled()) {\n    repo.server_ = new ReadonlyRestClient(\n      repo.repoInfo_,\n      (\n        pathString: string,\n        data: unknown,\n        isMerge: boolean,\n        tag: number | null\n      ) => {\n        repoOnDataUpdate(repo, pathString, data, isMerge, tag);\n      },\n      repo.authTokenProvider_,\n      repo.appCheckProvider_\n    );\n\n    // Minor hack: Fire onConnect immediately, since there's no actual connection.\n    setTimeout(() => repoOnConnectStatus(repo, /* connectStatus= */ true), 0);\n  } else {\n    // Validate authOverride\n    if (typeof authOverride !== 'undefined' && authOverride !== null) {\n      if (typeof authOverride !== 'object') {\n        throw new Error(\n          'Only objects are supported for option databaseAuthVariableOverride'\n        );\n      }\n      try {\n        stringify(authOverride);\n      } catch (e) {\n        throw new Error('Invalid authOverride provided: ' + e);\n      }\n    }\n\n    repo.persistentConnection_ = new PersistentConnection(\n      repo.repoInfo_,\n      appId,\n      (\n        pathString: string,\n        data: unknown,\n        isMerge: boolean,\n        tag: number | null\n      ) => {\n        repoOnDataUpdate(repo, pathString, data, isMerge, tag);\n      },\n      (connectStatus: boolean) => {\n        repoOnConnectStatus(repo, connectStatus);\n      },\n      (updates: object) => {\n        repoOnServerInfoUpdate(repo, updates);\n      },\n      repo.authTokenProvider_,\n      repo.appCheckProvider_,\n      authOverride\n    );\n\n    repo.server_ = repo.persistentConnection_;\n  }\n\n  repo.authTokenProvider_.addTokenChangeListener(token => {\n    repo.server_.refreshAuthToken(token);\n  });\n\n  repo.appCheckProvider_.addTokenChangeListener(result => {\n    repo.server_.refreshAppCheckToken(result.token);\n  });\n\n  // In the case of multiple Repos for the same repoInfo (i.e. there are multiple Firebase.Contexts being used),\n  // we only want to create one StatsReporter.  As such, we'll report stats over the first Repo created.\n  repo.statsReporter_ = statsManagerGetOrCreateReporter(\n    repo.repoInfo_,\n    () => new StatsReporter(repo.stats_, repo.server_)\n  );\n\n  // Used for .info.\n  repo.infoData_ = new SnapshotHolder();\n  repo.infoSyncTree_ = new SyncTree({\n    startListening: (query, tag, currentHashFn, onComplete) => {\n      let infoEvents: Event[] = [];\n      const node = repo.infoData_.getNode(query._path);\n      // This is possibly a hack, but we have different semantics for .info endpoints. We don't raise null events\n      // on initial data...\n      if (!node.isEmpty()) {\n        infoEvents = syncTreeApplyServerOverwrite(\n          repo.infoSyncTree_,\n          query._path,\n          node\n        );\n        setTimeout(() => {\n          onComplete('ok');\n        }, 0);\n      }\n      return infoEvents;\n    },\n    stopListening: () => {}\n  });\n  repoUpdateInfo(repo, 'connected', false);\n\n  repo.serverSyncTree_ = new SyncTree({\n    startListening: (query, tag, currentHashFn, onComplete) => {\n      repo.server_.listen(query, currentHashFn, tag, (status, data) => {\n        const events = onComplete(status, data);\n        eventQueueRaiseEventsForChangedPath(\n          repo.eventQueue_,\n          query._path,\n          events\n        );\n      });\n      // No synchronous events for network-backed sync trees\n      return [];\n    },\n    stopListening: (query, tag) => {\n      repo.server_.unlisten(query, tag);\n    }\n  });\n}\n\n/**\n * @returns The time in milliseconds, taking the server offset into account if we have one.\n */\nexport function repoServerTime(repo: Repo): number {\n  const offsetNode = repo.infoData_.getNode(new Path('.info/serverTimeOffset'));\n  const offset = (offsetNode.val() as number) || 0;\n  return new Date().getTime() + offset;\n}\n\n/**\n * Generate ServerValues using some variables from the repo object.\n */\nexport function repoGenerateServerValues(repo: Repo): Indexable {\n  return generateWithValues({\n    timestamp: repoServerTime(repo)\n  });\n}\n\n/**\n * Called by realtime when we get new messages from the server.\n */\nfunction repoOnDataUpdate(\n  repo: Repo,\n  pathString: string,\n  data: unknown,\n  isMerge: boolean,\n  tag: number | null\n): void {\n  // For testing.\n  repo.dataUpdateCount++;\n  const path = new Path(pathString);\n  data = repo.interceptServerDataCallback_\n    ? repo.interceptServerDataCallback_(pathString, data)\n    : data;\n  let events = [];\n  if (tag) {\n    if (isMerge) {\n      const taggedChildren = map(\n        data as { [k: string]: unknown },\n        (raw: unknown) => nodeFromJSON(raw)\n      );\n      events = syncTreeApplyTaggedQueryMerge(\n        repo.serverSyncTree_,\n        path,\n        taggedChildren,\n        tag\n      );\n    } else {\n      const taggedSnap = nodeFromJSON(data);\n      events = syncTreeApplyTaggedQueryOverwrite(\n        repo.serverSyncTree_,\n        path,\n        taggedSnap,\n        tag\n      );\n    }\n  } else if (isMerge) {\n    const changedChildren = map(\n      data as { [k: string]: unknown },\n      (raw: unknown) => nodeFromJSON(raw)\n    );\n    events = syncTreeApplyServerMerge(\n      repo.serverSyncTree_,\n      path,\n      changedChildren\n    );\n  } else {\n    const snap = nodeFromJSON(data);\n    events = syncTreeApplyServerOverwrite(repo.serverSyncTree_, path, snap);\n  }\n  let affectedPath = path;\n  if (events.length > 0) {\n    // Since we have a listener outstanding for each transaction, receiving any events\n    // is a proxy for some change having occurred.\n    affectedPath = repoRerunTransactions(repo, path);\n  }\n  eventQueueRaiseEventsForChangedPath(repo.eventQueue_, affectedPath, events);\n}\n\n// TODO: This should be @private but it's used by test_access.js and internal.js\nexport function repoInterceptServerData(\n  repo: Repo,\n  callback: ((a: string, b: unknown) => unknown) | null\n): void {\n  repo.interceptServerDataCallback_ = callback;\n}\n\nfunction repoOnConnectStatus(repo: Repo, connectStatus: boolean): void {\n  repoUpdateInfo(repo, 'connected', connectStatus);\n  if (connectStatus === false) {\n    repoRunOnDisconnectEvents(repo);\n  }\n}\n\nfunction repoOnServerInfoUpdate(repo: Repo, updates: object): void {\n  each(updates, (key: string, value: unknown) => {\n    repoUpdateInfo(repo, key, value);\n  });\n}\n\nfunction repoUpdateInfo(repo: Repo, pathString: string, value: unknown): void {\n  const path = new Path('/.info/' + pathString);\n  const newNode = nodeFromJSON(value);\n  repo.infoData_.updateSnapshot(path, newNode);\n  const events = syncTreeApplyServerOverwrite(\n    repo.infoSyncTree_,\n    path,\n    newNode\n  );\n  eventQueueRaiseEventsForChangedPath(repo.eventQueue_, path, events);\n}\n\nfunction repoGetNextWriteId(repo: Repo): number {\n  return repo.nextWriteId_++;\n}\n\n/**\n * The purpose of `getValue` is to return the latest known value\n * satisfying `query`.\n *\n * This method will first check for in-memory cached values\n * belonging to active listeners. If they are found, such values\n * are considered to be the most up-to-date.\n *\n * If the client is not connected, this method will try to\n * establish a connection and request the value for `query`. If\n * the client is not able to retrieve the query result, it reports\n * an error.\n *\n * @param query - The query to surface a value for.\n */\nexport function repoGetValue(repo: Repo, query: QueryContext): Promise<Node> {\n  // Only active queries are cached. There is no persisted cache.\n  const cached = syncTreeGetServerValue(repo.serverSyncTree_, query);\n  if (cached != null) {\n    return Promise.resolve(cached);\n  }\n  return repo.server_.get(query).then(\n    payload => {\n      const node = nodeFromJSON(payload as string).withIndex(\n        query._queryParams.getIndex()\n      );\n      const events = syncTreeApplyServerOverwrite(\n        repo.serverSyncTree_,\n        query._path,\n        node\n      );\n      eventQueueRaiseEventsAtPath(repo.eventQueue_, query._path, events);\n      return Promise.resolve(node);\n    },\n    err => {\n      repoLog(repo, 'get for query ' + stringify(query) + ' failed: ' + err);\n      return Promise.reject(new Error(err as string));\n    }\n  );\n}\n\nexport function repoSetWithPriority(\n  repo: Repo,\n  path: Path,\n  newVal: unknown,\n  newPriority: number | string | null,\n  onComplete: ((status: Error | null, errorReason?: string) => void) | null\n): void {\n  repoLog(repo, 'set', {\n    path: path.toString(),\n    value: newVal,\n    priority: newPriority\n  });\n\n  // TODO: Optimize this behavior to either (a) store flag to skip resolving where possible and / or\n  // (b) store unresolved paths on JSON parse\n  const serverValues = repoGenerateServerValues(repo);\n  const newNodeUnresolved = nodeFromJSON(newVal, newPriority);\n  const existing = syncTreeCalcCompleteEventCache(repo.serverSyncTree_, path);\n  const newNode = resolveDeferredValueSnapshot(\n    newNodeUnresolved,\n    existing,\n    serverValues\n  );\n\n  const writeId = repoGetNextWriteId(repo);\n  const events = syncTreeApplyUserOverwrite(\n    repo.serverSyncTree_,\n    path,\n    newNode,\n    writeId,\n    true\n  );\n  eventQueueQueueEvents(repo.eventQueue_, events);\n  repo.server_.put(\n    path.toString(),\n    newNodeUnresolved.val(/*export=*/ true),\n    (status, errorReason) => {\n      const success = status === 'ok';\n      if (!success) {\n        warn('set at ' + path + ' failed: ' + status);\n      }\n\n      const clearEvents = syncTreeAckUserWrite(\n        repo.serverSyncTree_,\n        writeId,\n        !success\n      );\n      eventQueueRaiseEventsForChangedPath(repo.eventQueue_, path, clearEvents);\n      repoCallOnCompleteCallback(repo, onComplete, status, errorReason);\n    }\n  );\n  const affectedPath = repoAbortTransactions(repo, path);\n  repoRerunTransactions(repo, affectedPath);\n  // We queued the events above, so just flush the queue here\n  eventQueueRaiseEventsForChangedPath(repo.eventQueue_, affectedPath, []);\n}\n\nexport function repoUpdate(\n  repo: Repo,\n  path: Path,\n  childrenToMerge: { [k: string]: unknown },\n  onComplete: ((status: Error | null, errorReason?: string) => void) | null\n): void {\n  repoLog(repo, 'update', { path: path.toString(), value: childrenToMerge });\n\n  // Start with our existing data and merge each child into it.\n  let empty = true;\n  const serverValues = repoGenerateServerValues(repo);\n  const changedChildren: { [k: string]: Node } = {};\n  each(childrenToMerge, (changedKey: string, changedValue: unknown) => {\n    empty = false;\n    changedChildren[changedKey] = resolveDeferredValueTree(\n      pathChild(path, changedKey),\n      nodeFromJSON(changedValue),\n      repo.serverSyncTree_,\n      serverValues\n    );\n  });\n\n  if (!empty) {\n    const writeId = repoGetNextWriteId(repo);\n    const events = syncTreeApplyUserMerge(\n      repo.serverSyncTree_,\n      path,\n      changedChildren,\n      writeId\n    );\n    eventQueueQueueEvents(repo.eventQueue_, events);\n    repo.server_.merge(\n      path.toString(),\n      childrenToMerge,\n      (status, errorReason) => {\n        const success = status === 'ok';\n        if (!success) {\n          warn('update at ' + path + ' failed: ' + status);\n        }\n\n        const clearEvents = syncTreeAckUserWrite(\n          repo.serverSyncTree_,\n          writeId,\n          !success\n        );\n        const affectedPath =\n          clearEvents.length > 0 ? repoRerunTransactions(repo, path) : path;\n        eventQueueRaiseEventsForChangedPath(\n          repo.eventQueue_,\n          affectedPath,\n          clearEvents\n        );\n        repoCallOnCompleteCallback(repo, onComplete, status, errorReason);\n      }\n    );\n\n    each(childrenToMerge, (changedPath: string) => {\n      const affectedPath = repoAbortTransactions(\n        repo,\n        pathChild(path, changedPath)\n      );\n      repoRerunTransactions(repo, affectedPath);\n    });\n\n    // We queued the events above, so just flush the queue here\n    eventQueueRaiseEventsForChangedPath(repo.eventQueue_, path, []);\n  } else {\n    log(\"update() called with empty data.  Don't do anything.\");\n    repoCallOnCompleteCallback(repo, onComplete, 'ok', undefined);\n  }\n}\n\n/**\n * Applies all of the changes stored up in the onDisconnect_ tree.\n */\nfunction repoRunOnDisconnectEvents(repo: Repo): void {\n  repoLog(repo, 'onDisconnectEvents');\n\n  const serverValues = repoGenerateServerValues(repo);\n  const resolvedOnDisconnectTree = newSparseSnapshotTree();\n  sparseSnapshotTreeForEachTree(\n    repo.onDisconnect_,\n    newEmptyPath(),\n    (path, node) => {\n      const resolved = resolveDeferredValueTree(\n        path,\n        node,\n        repo.serverSyncTree_,\n        serverValues\n      );\n      sparseSnapshotTreeRemember(resolvedOnDisconnectTree, path, resolved);\n    }\n  );\n  let events: Event[] = [];\n\n  sparseSnapshotTreeForEachTree(\n    resolvedOnDisconnectTree,\n    newEmptyPath(),\n    (path, snap) => {\n      events = events.concat(\n        syncTreeApplyServerOverwrite(repo.serverSyncTree_, path, snap)\n      );\n      const affectedPath = repoAbortTransactions(repo, path);\n      repoRerunTransactions(repo, affectedPath);\n    }\n  );\n\n  repo.onDisconnect_ = newSparseSnapshotTree();\n  eventQueueRaiseEventsForChangedPath(repo.eventQueue_, newEmptyPath(), events);\n}\n\nexport function repoOnDisconnectCancel(\n  repo: Repo,\n  path: Path,\n  onComplete: ((status: Error | null, errorReason?: string) => void) | null\n): void {\n  repo.server_.onDisconnectCancel(path.toString(), (status, errorReason) => {\n    if (status === 'ok') {\n      sparseSnapshotTreeForget(repo.onDisconnect_, path);\n    }\n    repoCallOnCompleteCallback(repo, onComplete, status, errorReason);\n  });\n}\n\nexport function repoOnDisconnectSet(\n  repo: Repo,\n  path: Path,\n  value: unknown,\n  onComplete: ((status: Error | null, errorReason?: string) => void) | null\n): void {\n  const newNode = nodeFromJSON(value);\n  repo.server_.onDisconnectPut(\n    path.toString(),\n    newNode.val(/*export=*/ true),\n    (status, errorReason) => {\n      if (status === 'ok') {\n        sparseSnapshotTreeRemember(repo.onDisconnect_, path, newNode);\n      }\n      repoCallOnCompleteCallback(repo, onComplete, status, errorReason);\n    }\n  );\n}\n\nexport function repoOnDisconnectSetWithPriority(\n  repo: Repo,\n  path: Path,\n  value: unknown,\n  priority: unknown,\n  onComplete: ((status: Error | null, errorReason?: string) => void) | null\n): void {\n  const newNode = nodeFromJSON(value, priority);\n  repo.server_.onDisconnectPut(\n    path.toString(),\n    newNode.val(/*export=*/ true),\n    (status, errorReason) => {\n      if (status === 'ok') {\n        sparseSnapshotTreeRemember(repo.onDisconnect_, path, newNode);\n      }\n      repoCallOnCompleteCallback(repo, onComplete, status, errorReason);\n    }\n  );\n}\n\nexport function repoOnDisconnectUpdate(\n  repo: Repo,\n  path: Path,\n  childrenToMerge: { [k: string]: unknown },\n  onComplete: ((status: Error | null, errorReason?: string) => void) | null\n): void {\n  if (isEmpty(childrenToMerge)) {\n    log(\"onDisconnect().update() called with empty data.  Don't do anything.\");\n    repoCallOnCompleteCallback(repo, onComplete, 'ok', undefined);\n    return;\n  }\n\n  repo.server_.onDisconnectMerge(\n    path.toString(),\n    childrenToMerge,\n    (status, errorReason) => {\n      if (status === 'ok') {\n        each(childrenToMerge, (childName: string, childNode: unknown) => {\n          const newChildNode = nodeFromJSON(childNode);\n          sparseSnapshotTreeRemember(\n            repo.onDisconnect_,\n            pathChild(path, childName),\n            newChildNode\n          );\n        });\n      }\n      repoCallOnCompleteCallback(repo, onComplete, status, errorReason);\n    }\n  );\n}\n\nexport function repoAddEventCallbackForQuery(\n  repo: Repo,\n  query: QueryContext,\n  eventRegistration: EventRegistration\n): void {\n  let events;\n  if (pathGetFront(query._path) === '.info') {\n    events = syncTreeAddEventRegistration(\n      repo.infoSyncTree_,\n      query,\n      eventRegistration\n    );\n  } else {\n    events = syncTreeAddEventRegistration(\n      repo.serverSyncTree_,\n      query,\n      eventRegistration\n    );\n  }\n  eventQueueRaiseEventsAtPath(repo.eventQueue_, query._path, events);\n}\n\nexport function repoRemoveEventCallbackForQuery(\n  repo: Repo,\n  query: QueryContext,\n  eventRegistration: EventRegistration\n): void {\n  // These are guaranteed not to raise events, since we're not passing in a cancelError. However, we can future-proof\n  // a little bit by handling the return values anyways.\n  let events;\n  if (pathGetFront(query._path) === '.info') {\n    events = syncTreeRemoveEventRegistration(\n      repo.infoSyncTree_,\n      query,\n      eventRegistration\n    );\n  } else {\n    events = syncTreeRemoveEventRegistration(\n      repo.serverSyncTree_,\n      query,\n      eventRegistration\n    );\n  }\n  eventQueueRaiseEventsAtPath(repo.eventQueue_, query._path, events);\n}\n\nexport function repoInterrupt(repo: Repo): void {\n  if (repo.persistentConnection_) {\n    repo.persistentConnection_.interrupt(INTERRUPT_REASON);\n  }\n}\n\nexport function repoResume(repo: Repo): void {\n  if (repo.persistentConnection_) {\n    repo.persistentConnection_.resume(INTERRUPT_REASON);\n  }\n}\n\nexport function repoStats(repo: Repo, showDelta: boolean = false): void {\n  if (typeof console === 'undefined') {\n    return;\n  }\n\n  let stats: { [k: string]: unknown };\n  if (showDelta) {\n    if (!repo.statsListener_) {\n      repo.statsListener_ = new StatsListener(repo.stats_);\n    }\n    stats = repo.statsListener_.get();\n  } else {\n    stats = repo.stats_.get();\n  }\n\n  const longestName = Object.keys(stats).reduce(\n    (previousValue, currentValue) =>\n      Math.max(currentValue.length, previousValue),\n    0\n  );\n\n  each(stats, (stat: string, value: unknown) => {\n    let paddedStat = stat;\n    // pad stat names to be the same length (plus 2 extra spaces).\n    for (let i = stat.length; i < longestName + 2; i++) {\n      paddedStat += ' ';\n    }\n    console.log(paddedStat + value);\n  });\n}\n\nexport function repoStatsIncrementCounter(repo: Repo, metric: string): void {\n  repo.stats_.incrementCounter(metric);\n  statsReporterIncludeStat(repo.statsReporter_, metric);\n}\n\nfunction repoLog(repo: Repo, ...varArgs: unknown[]): void {\n  let prefix = '';\n  if (repo.persistentConnection_) {\n    prefix = repo.persistentConnection_.id + ':';\n  }\n  log(prefix, ...varArgs);\n}\n\nexport function repoCallOnCompleteCallback(\n  repo: Repo,\n  callback: ((status: Error | null, errorReason?: string) => void) | null,\n  status: string,\n  errorReason?: string | null\n): void {\n  if (callback) {\n    exceptionGuard(() => {\n      if (status === 'ok') {\n        callback(null);\n      } else {\n        const code = (status || 'error').toUpperCase();\n        let message = code;\n        if (errorReason) {\n          message += ': ' + errorReason;\n        }\n\n        const error = new Error(message);\n\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (error as any).code = code;\n        callback(error);\n      }\n    });\n  }\n}\n\n/**\n * Creates a new transaction, adds it to the transactions we're tracking, and\n * sends it to the server if possible.\n *\n * @param path - Path at which to do transaction.\n * @param transactionUpdate - Update callback.\n * @param onComplete - Completion callback.\n * @param unwatcher - Function that will be called when the transaction no longer\n * need data updates for `path`.\n * @param applyLocally - Whether or not to make intermediate results visible\n */\nexport function repoStartTransaction(\n  repo: Repo,\n  path: Path,\n  transactionUpdate: (a: unknown) => unknown,\n  onComplete: ((error: Error, committed: boolean, node: Node) => void) | null,\n  unwatcher: () => void,\n  applyLocally: boolean\n): void {\n  repoLog(repo, 'transaction on ' + path);\n\n  // Initialize transaction.\n  const transaction: Transaction = {\n    path,\n    update: transactionUpdate,\n    onComplete,\n    // One of TransactionStatus enums.\n    status: null,\n    // Used when combining transactions at different locations to figure out\n    // which one goes first.\n    order: LUIDGenerator(),\n    // Whether to raise local events for this transaction.\n    applyLocally,\n    // Count of how many times we've retried the transaction.\n    retryCount: 0,\n    // Function to call to clean up our .on() listener.\n    unwatcher,\n    // Stores why a transaction was aborted.\n    abortReason: null,\n    currentWriteId: null,\n    currentInputSnapshot: null,\n    currentOutputSnapshotRaw: null,\n    currentOutputSnapshotResolved: null\n  };\n\n  // Run transaction initially.\n  const currentState = repoGetLatestState(repo, path, undefined);\n  transaction.currentInputSnapshot = currentState;\n  const newVal = transaction.update(currentState.val());\n  if (newVal === undefined) {\n    // Abort transaction.\n    transaction.unwatcher();\n    transaction.currentOutputSnapshotRaw = null;\n    transaction.currentOutputSnapshotResolved = null;\n    if (transaction.onComplete) {\n      transaction.onComplete(null, false, transaction.currentInputSnapshot);\n    }\n  } else {\n    validateFirebaseData(\n      'transaction failed: Data returned ',\n      newVal,\n      transaction.path\n    );\n\n    // Mark as run and add to our queue.\n    transaction.status = TransactionStatus.RUN;\n    const queueNode = treeSubTree(repo.transactionQueueTree_, path);\n    const nodeQueue = treeGetValue(queueNode) || [];\n    nodeQueue.push(transaction);\n\n    treeSetValue(queueNode, nodeQueue);\n\n    // Update visibleData and raise events\n    // Note: We intentionally raise events after updating all of our\n    // transaction state, since the user could start new transactions from the\n    // event callbacks.\n    let priorityForNode;\n    if (\n      typeof newVal === 'object' &&\n      newVal !== null &&\n      contains(newVal, '.priority')\n    ) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      priorityForNode = safeGet(newVal as any, '.priority');\n      assert(\n        isValidPriority(priorityForNode),\n        'Invalid priority returned by transaction. ' +\n          'Priority must be a valid string, finite number, server value, or null.'\n      );\n    } else {\n      const currentNode =\n        syncTreeCalcCompleteEventCache(repo.serverSyncTree_, path) ||\n        ChildrenNode.EMPTY_NODE;\n      priorityForNode = currentNode.getPriority().val();\n    }\n\n    const serverValues = repoGenerateServerValues(repo);\n    const newNodeUnresolved = nodeFromJSON(newVal, priorityForNode);\n    const newNode = resolveDeferredValueSnapshot(\n      newNodeUnresolved,\n      currentState,\n      serverValues\n    );\n    transaction.currentOutputSnapshotRaw = newNodeUnresolved;\n    transaction.currentOutputSnapshotResolved = newNode;\n    transaction.currentWriteId = repoGetNextWriteId(repo);\n\n    const events = syncTreeApplyUserOverwrite(\n      repo.serverSyncTree_,\n      path,\n      newNode,\n      transaction.currentWriteId,\n      transaction.applyLocally\n    );\n    eventQueueRaiseEventsForChangedPath(repo.eventQueue_, path, events);\n\n    repoSendReadyTransactions(repo, repo.transactionQueueTree_);\n  }\n}\n\n/**\n * @param excludeSets - A specific set to exclude\n */\nfunction repoGetLatestState(\n  repo: Repo,\n  path: Path,\n  excludeSets?: number[]\n): Node {\n  return (\n    syncTreeCalcCompleteEventCache(repo.serverSyncTree_, path, excludeSets) ||\n    ChildrenNode.EMPTY_NODE\n  );\n}\n\n/**\n * Sends any already-run transactions that aren't waiting for outstanding\n * transactions to complete.\n *\n * Externally it's called with no arguments, but it calls itself recursively\n * with a particular transactionQueueTree node to recurse through the tree.\n *\n * @param node - transactionQueueTree node to start at.\n */\nfunction repoSendReadyTransactions(\n  repo: Repo,\n  node: Tree<Transaction[]> = repo.transactionQueueTree_\n): void {\n  // Before recursing, make sure any completed transactions are removed.\n  if (!node) {\n    repoPruneCompletedTransactionsBelowNode(repo, node);\n  }\n\n  if (treeGetValue(node)) {\n    const queue = repoBuildTransactionQueue(repo, node);\n    assert(queue.length > 0, 'Sending zero length transaction queue');\n\n    const allRun = queue.every(\n      (transaction: Transaction) => transaction.status === TransactionStatus.RUN\n    );\n\n    // If they're all run (and not sent), we can send them.  Else, we must wait.\n    if (allRun) {\n      repoSendTransactionQueue(repo, treeGetPath(node), queue);\n    }\n  } else if (treeHasChildren(node)) {\n    treeForEachChild(node, childNode => {\n      repoSendReadyTransactions(repo, childNode);\n    });\n  }\n}\n\n/**\n * Given a list of run transactions, send them to the server and then handle\n * the result (success or failure).\n *\n * @param path - The location of the queue.\n * @param queue - Queue of transactions under the specified location.\n */\nfunction repoSendTransactionQueue(\n  repo: Repo,\n  path: Path,\n  queue: Transaction[]\n): void {\n  // Mark transactions as sent and increment retry count!\n  const setsToIgnore = queue.map(txn => {\n    return txn.currentWriteId;\n  });\n  const latestState = repoGetLatestState(repo, path, setsToIgnore);\n  let snapToSend = latestState;\n  const latestHash = latestState.hash();\n  for (let i = 0; i < queue.length; i++) {\n    const txn = queue[i];\n    assert(\n      txn.status === TransactionStatus.RUN,\n      'tryToSendTransactionQueue_: items in queue should all be run.'\n    );\n    txn.status = TransactionStatus.SENT;\n    txn.retryCount++;\n    const relativePath = newRelativePath(path, txn.path);\n    // If we've gotten to this point, the output snapshot must be defined.\n    snapToSend = snapToSend.updateChild(\n      relativePath /** @type {!Node} */,\n      txn.currentOutputSnapshotRaw\n    );\n  }\n\n  const dataToSend = snapToSend.val(true);\n  const pathToSend = path;\n\n  // Send the put.\n  repo.server_.put(\n    pathToSend.toString(),\n    dataToSend,\n    (status: string) => {\n      repoLog(repo, 'transaction put response', {\n        path: pathToSend.toString(),\n        status\n      });\n\n      let events: Event[] = [];\n      if (status === 'ok') {\n        // Queue up the callbacks and fire them after cleaning up all of our\n        // transaction state, since the callback could trigger more\n        // transactions or sets.\n        const callbacks = [];\n        for (let i = 0; i < queue.length; i++) {\n          queue[i].status = TransactionStatus.COMPLETED;\n          events = events.concat(\n            syncTreeAckUserWrite(repo.serverSyncTree_, queue[i].currentWriteId)\n          );\n          if (queue[i].onComplete) {\n            // We never unset the output snapshot, and given that this\n            // transaction is complete, it should be set\n            callbacks.push(() =>\n              queue[i].onComplete(\n                null,\n                true,\n                queue[i].currentOutputSnapshotResolved\n              )\n            );\n          }\n          queue[i].unwatcher();\n        }\n\n        // Now remove the completed transactions.\n        repoPruneCompletedTransactionsBelowNode(\n          repo,\n          treeSubTree(repo.transactionQueueTree_, path)\n        );\n        // There may be pending transactions that we can now send.\n        repoSendReadyTransactions(repo, repo.transactionQueueTree_);\n\n        eventQueueRaiseEventsForChangedPath(repo.eventQueue_, path, events);\n\n        // Finally, trigger onComplete callbacks.\n        for (let i = 0; i < callbacks.length; i++) {\n          exceptionGuard(callbacks[i]);\n        }\n      } else {\n        // transactions are no longer sent.  Update their status appropriately.\n        if (status === 'datastale') {\n          for (let i = 0; i < queue.length; i++) {\n            if (queue[i].status === TransactionStatus.SENT_NEEDS_ABORT) {\n              queue[i].status = TransactionStatus.NEEDS_ABORT;\n            } else {\n              queue[i].status = TransactionStatus.RUN;\n            }\n          }\n        } else {\n          warn(\n            'transaction at ' + pathToSend.toString() + ' failed: ' + status\n          );\n          for (let i = 0; i < queue.length; i++) {\n            queue[i].status = TransactionStatus.NEEDS_ABORT;\n            queue[i].abortReason = status;\n          }\n        }\n\n        repoRerunTransactions(repo, path);\n      }\n    },\n    latestHash\n  );\n}\n\n/**\n * Finds all transactions dependent on the data at changedPath and reruns them.\n *\n * Should be called any time cached data changes.\n *\n * Return the highest path that was affected by rerunning transactions. This\n * is the path at which events need to be raised for.\n *\n * @param changedPath - The path in mergedData that changed.\n * @returns The rootmost path that was affected by rerunning transactions.\n */\nfunction repoRerunTransactions(repo: Repo, changedPath: Path): Path {\n  const rootMostTransactionNode = repoGetAncestorTransactionNode(\n    repo,\n    changedPath\n  );\n  const path = treeGetPath(rootMostTransactionNode);\n\n  const queue = repoBuildTransactionQueue(repo, rootMostTransactionNode);\n  repoRerunTransactionQueue(repo, queue, path);\n\n  return path;\n}\n\n/**\n * Does all the work of rerunning transactions (as well as cleans up aborted\n * transactions and whatnot).\n *\n * @param queue - The queue of transactions to run.\n * @param path - The path the queue is for.\n */\nfunction repoRerunTransactionQueue(\n  repo: Repo,\n  queue: Transaction[],\n  path: Path\n): void {\n  if (queue.length === 0) {\n    return; // Nothing to do!\n  }\n\n  // Queue up the callbacks and fire them after cleaning up all of our\n  // transaction state, since the callback could trigger more transactions or\n  // sets.\n  const callbacks = [];\n  let events: Event[] = [];\n  // Ignore all of the sets we're going to re-run.\n  const txnsToRerun = queue.filter(q => {\n    return q.status === TransactionStatus.RUN;\n  });\n  const setsToIgnore = txnsToRerun.map(q => {\n    return q.currentWriteId;\n  });\n  for (let i = 0; i < queue.length; i++) {\n    const transaction = queue[i];\n    const relativePath = newRelativePath(path, transaction.path);\n    let abortTransaction = false,\n      abortReason;\n    assert(\n      relativePath !== null,\n      'rerunTransactionsUnderNode_: relativePath should not be null.'\n    );\n\n    if (transaction.status === TransactionStatus.NEEDS_ABORT) {\n      abortTransaction = true;\n      abortReason = transaction.abortReason;\n      events = events.concat(\n        syncTreeAckUserWrite(\n          repo.serverSyncTree_,\n          transaction.currentWriteId,\n          true\n        )\n      );\n    } else if (transaction.status === TransactionStatus.RUN) {\n      if (transaction.retryCount >= MAX_TRANSACTION_RETRIES) {\n        abortTransaction = true;\n        abortReason = 'maxretry';\n        events = events.concat(\n          syncTreeAckUserWrite(\n            repo.serverSyncTree_,\n            transaction.currentWriteId,\n            true\n          )\n        );\n      } else {\n        // This code reruns a transaction\n        const currentNode = repoGetLatestState(\n          repo,\n          transaction.path,\n          setsToIgnore\n        );\n        transaction.currentInputSnapshot = currentNode;\n        const newData = queue[i].update(currentNode.val());\n        if (newData !== undefined) {\n          validateFirebaseData(\n            'transaction failed: Data returned ',\n            newData,\n            transaction.path\n          );\n          let newDataNode = nodeFromJSON(newData);\n          const hasExplicitPriority =\n            typeof newData === 'object' &&\n            newData != null &&\n            contains(newData, '.priority');\n          if (!hasExplicitPriority) {\n            // Keep the old priority if there wasn't a priority explicitly specified.\n            newDataNode = newDataNode.updatePriority(currentNode.getPriority());\n          }\n\n          const oldWriteId = transaction.currentWriteId;\n          const serverValues = repoGenerateServerValues(repo);\n          const newNodeResolved = resolveDeferredValueSnapshot(\n            newDataNode,\n            currentNode,\n            serverValues\n          );\n\n          transaction.currentOutputSnapshotRaw = newDataNode;\n          transaction.currentOutputSnapshotResolved = newNodeResolved;\n          transaction.currentWriteId = repoGetNextWriteId(repo);\n          // Mutates setsToIgnore in place\n          setsToIgnore.splice(setsToIgnore.indexOf(oldWriteId), 1);\n          events = events.concat(\n            syncTreeApplyUserOverwrite(\n              repo.serverSyncTree_,\n              transaction.path,\n              newNodeResolved,\n              transaction.currentWriteId,\n              transaction.applyLocally\n            )\n          );\n          events = events.concat(\n            syncTreeAckUserWrite(repo.serverSyncTree_, oldWriteId, true)\n          );\n        } else {\n          abortTransaction = true;\n          abortReason = 'nodata';\n          events = events.concat(\n            syncTreeAckUserWrite(\n              repo.serverSyncTree_,\n              transaction.currentWriteId,\n              true\n            )\n          );\n        }\n      }\n    }\n    eventQueueRaiseEventsForChangedPath(repo.eventQueue_, path, events);\n    events = [];\n    if (abortTransaction) {\n      // Abort.\n      queue[i].status = TransactionStatus.COMPLETED;\n\n      // Removing a listener can trigger pruning which can muck with\n      // mergedData/visibleData (as it prunes data). So defer the unwatcher\n      // until we're done.\n      (function (unwatcher) {\n        setTimeout(unwatcher, Math.floor(0));\n      })(queue[i].unwatcher);\n\n      if (queue[i].onComplete) {\n        if (abortReason === 'nodata') {\n          callbacks.push(() =>\n            queue[i].onComplete(null, false, queue[i].currentInputSnapshot)\n          );\n        } else {\n          callbacks.push(() =>\n            queue[i].onComplete(new Error(abortReason), false, null)\n          );\n        }\n      }\n    }\n  }\n\n  // Clean up completed transactions.\n  repoPruneCompletedTransactionsBelowNode(repo, repo.transactionQueueTree_);\n\n  // Now fire callbacks, now that we're in a good, known state.\n  for (let i = 0; i < callbacks.length; i++) {\n    exceptionGuard(callbacks[i]);\n  }\n\n  // Try to send the transaction result to the server.\n  repoSendReadyTransactions(repo, repo.transactionQueueTree_);\n}\n\n/**\n * Returns the rootmost ancestor node of the specified path that has a pending\n * transaction on it, or just returns the node for the given path if there are\n * no pending transactions on any ancestor.\n *\n * @param path - The location to start at.\n * @returns The rootmost node with a transaction.\n */\nfunction repoGetAncestorTransactionNode(\n  repo: Repo,\n  path: Path\n): Tree<Transaction[]> {\n  let front;\n\n  // Start at the root and walk deeper into the tree towards path until we\n  // find a node with pending transactions.\n  let transactionNode = repo.transactionQueueTree_;\n  front = pathGetFront(path);\n  while (front !== null && treeGetValue(transactionNode) === undefined) {\n    transactionNode = treeSubTree(transactionNode, front);\n    path = pathPopFront(path);\n    front = pathGetFront(path);\n  }\n\n  return transactionNode;\n}\n\n/**\n * Builds the queue of all transactions at or below the specified\n * transactionNode.\n *\n * @param transactionNode\n * @returns The generated queue.\n */\nfunction repoBuildTransactionQueue(\n  repo: Repo,\n  transactionNode: Tree<Transaction[]>\n): Transaction[] {\n  // Walk any child transaction queues and aggregate them into a single queue.\n  const transactionQueue: Transaction[] = [];\n  repoAggregateTransactionQueuesForNode(\n    repo,\n    transactionNode,\n    transactionQueue\n  );\n\n  // Sort them by the order the transactions were created.\n  transactionQueue.sort((a, b) => a.order - b.order);\n\n  return transactionQueue;\n}\n\nfunction repoAggregateTransactionQueuesForNode(\n  repo: Repo,\n  node: Tree<Transaction[]>,\n  queue: Transaction[]\n): void {\n  const nodeQueue = treeGetValue(node);\n  if (nodeQueue) {\n    for (let i = 0; i < nodeQueue.length; i++) {\n      queue.push(nodeQueue[i]);\n    }\n  }\n\n  treeForEachChild(node, child => {\n    repoAggregateTransactionQueuesForNode(repo, child, queue);\n  });\n}\n\n/**\n * Remove COMPLETED transactions at or below this node in the transactionQueueTree_.\n */\nfunction repoPruneCompletedTransactionsBelowNode(\n  repo: Repo,\n  node: Tree<Transaction[]>\n): void {\n  const queue = treeGetValue(node);\n  if (queue) {\n    let to = 0;\n    for (let from = 0; from < queue.length; from++) {\n      if (queue[from].status !== TransactionStatus.COMPLETED) {\n        queue[to] = queue[from];\n        to++;\n      }\n    }\n    queue.length = to;\n    treeSetValue(node, queue.length > 0 ? queue : undefined);\n  }\n\n  treeForEachChild(node, childNode => {\n    repoPruneCompletedTransactionsBelowNode(repo, childNode);\n  });\n}\n\n/**\n * Aborts all transactions on ancestors or descendants of the specified path.\n * Called when doing a set() or update() since we consider them incompatible\n * with transactions.\n *\n * @param path - Path for which we want to abort related transactions.\n */\nfunction repoAbortTransactions(repo: Repo, path: Path): Path {\n  const affectedPath = treeGetPath(repoGetAncestorTransactionNode(repo, path));\n\n  const transactionNode = treeSubTree(repo.transactionQueueTree_, path);\n\n  treeForEachAncestor(transactionNode, (node: Tree<Transaction[]>) => {\n    repoAbortTransactionsOnNode(repo, node);\n  });\n\n  repoAbortTransactionsOnNode(repo, transactionNode);\n\n  treeForEachDescendant(transactionNode, (node: Tree<Transaction[]>) => {\n    repoAbortTransactionsOnNode(repo, node);\n  });\n\n  return affectedPath;\n}\n\n/**\n * Abort transactions stored in this transaction queue node.\n *\n * @param node - Node to abort transactions for.\n */\nfunction repoAbortTransactionsOnNode(\n  repo: Repo,\n  node: Tree<Transaction[]>\n): void {\n  const queue = treeGetValue(node);\n  if (queue) {\n    // Queue up the callbacks and fire them after cleaning up all of our\n    // transaction state, since the callback could trigger more transactions\n    // or sets.\n    const callbacks = [];\n\n    // Go through queue.  Any already-sent transactions must be marked for\n    // abort, while the unsent ones can be immediately aborted and removed.\n    let events: Event[] = [];\n    let lastSent = -1;\n    for (let i = 0; i < queue.length; i++) {\n      if (queue[i].status === TransactionStatus.SENT_NEEDS_ABORT) {\n        // Already marked.  No action needed.\n      } else if (queue[i].status === TransactionStatus.SENT) {\n        assert(\n          lastSent === i - 1,\n          'All SENT items should be at beginning of queue.'\n        );\n        lastSent = i;\n        // Mark transaction for abort when it comes back.\n        queue[i].status = TransactionStatus.SENT_NEEDS_ABORT;\n        queue[i].abortReason = 'set';\n      } else {\n        assert(\n          queue[i].status === TransactionStatus.RUN,\n          'Unexpected transaction status in abort'\n        );\n        // We can abort it immediately.\n        queue[i].unwatcher();\n        events = events.concat(\n          syncTreeAckUserWrite(\n            repo.serverSyncTree_,\n            queue[i].currentWriteId,\n            true\n          )\n        );\n        if (queue[i].onComplete) {\n          callbacks.push(\n            queue[i].onComplete.bind(null, new Error('set'), false, null)\n          );\n        }\n      }\n    }\n    if (lastSent === -1) {\n      // We're not waiting for any sent transactions.  We can clear the queue.\n      treeSetValue(node, undefined);\n    } else {\n      // Remove the transactions we aborted.\n      queue.length = lastSent + 1;\n    }\n\n    // Now fire the callbacks.\n    eventQueueRaiseEventsForChangedPath(\n      repo.eventQueue_,\n      treeGetPath(node),\n      events\n    );\n    for (let i = 0; i < callbacks.length; i++) {\n      exceptionGuard(callbacks[i]);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  Provider\n} from '@firebase/component';\nimport * as types from '@firebase/database-types';\n\nimport { _repoManagerDatabaseFromApp } from '../../exp/index';\nimport {\n  repoInterceptServerData,\n  repoStats,\n  repoStatsIncrementCounter\n} from '../core/Repo';\nimport { setSDKVersion } from '../core/version';\nimport { BrowserPollConnection } from '../realtime/BrowserPollConnection';\nimport { WebSocketConnection } from '../realtime/WebSocketConnection';\n\nimport { Database } from './Database';\nimport { Reference } from './Reference';\n\n/**\n * INTERNAL methods for internal-use only (tests, etc.).\n *\n * Customers shouldn't use these or else should be aware that they could break at any time.\n */\n\nexport const forceLongPolling = function () {\n  WebSocketConnection.forceDisallow();\n  BrowserPollConnection.forceAllow();\n};\n\nexport const forceWebSockets = function () {\n  BrowserPollConnection.forceDisallow();\n};\n\n/* Used by App Manager */\nexport const isWebSocketsAvailable = function (): boolean {\n  return WebSocketConnection['isAvailable']();\n};\n\nexport const setSecurityDebugCallback = function (\n  ref: Reference,\n  callback: (a: object) => void\n) {\n  const connection = ref._delegate._repo.persistentConnection_;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (connection as any).securityDebugCallback_ = callback;\n};\n\nexport const stats = function (ref: Reference, showDelta?: boolean) {\n  repoStats(ref._delegate._repo, showDelta);\n};\n\nexport const statsIncrementCounter = function (ref: Reference, metric: string) {\n  repoStatsIncrementCounter(ref._delegate._repo, metric);\n};\n\nexport const dataUpdateCount = function (ref: Reference): number {\n  return ref._delegate._repo.dataUpdateCount;\n};\n\nexport const interceptServerData = function (\n  ref: Reference,\n  callback: ((a: string, b: unknown) => void) | null\n) {\n  return repoInterceptServerData(ref._delegate._repo, callback);\n};\n\n/**\n * Used by console to create a database based on the app,\n * passed database URL and a custom auth implementation.\n *\n * @param app - A valid FirebaseApp-like object\n * @param url - A valid Firebase databaseURL\n * @param version - custom version e.g. firebase-admin version\n * @param customAuthImpl - custom auth implementation\n */\nexport function initStandalone<T>({\n  app,\n  url,\n  version,\n  customAuthImpl,\n  namespace,\n  nodeAdmin = false\n}: {\n  app: FirebaseApp;\n  url: string;\n  version: string;\n  customAuthImpl: FirebaseAuthInternal;\n  namespace: T;\n  nodeAdmin?: boolean;\n}): {\n  instance: types.Database;\n  namespace: T;\n} {\n  setSDKVersion(version);\n\n  /**\n   * ComponentContainer('database-standalone') is just a placeholder that doesn't perform\n   * any actual function.\n   */\n  const authProvider = new Provider<FirebaseAuthInternalName>(\n    'auth-internal',\n    new ComponentContainer('database-standalone')\n  );\n  authProvider.setComponent(\n    new Component('auth-internal', () => customAuthImpl, ComponentType.PRIVATE)\n  );\n\n  return {\n    instance: new Database(\n      _repoManagerDatabaseFromApp(\n        app,\n        authProvider,\n        /* appCheckProvider= */ undefined,\n        url,\n        nodeAdmin\n      ),\n      app\n    ) as types.Database,\n    namespace\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { PersistentConnection } from '../core/PersistentConnection';\nimport { RepoInfo } from '../core/RepoInfo';\nimport { repoManagerForceRestClient } from '../exp/Database';\nimport { Connection } from '../realtime/Connection';\n\nimport { Query } from './Reference';\n\nexport const DataConnection = PersistentConnection;\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n(PersistentConnection.prototype as any).simpleListen = function (\n  pathString: string,\n  onComplete: (a: unknown) => void\n) {\n  this.sendRequest('q', { p: pathString }, onComplete);\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n(PersistentConnection.prototype as any).echo = function (\n  data: unknown,\n  onEcho: (a: unknown) => void\n) {\n  this.sendRequest('echo', { d: data }, onEcho);\n};\n\n// RealTimeConnection properties that we use in tests.\nexport const RealTimeConnection = Connection;\n\nexport const hijackHash = function (newHash: () => string) {\n  const oldPut = PersistentConnection.prototype.put;\n  PersistentConnection.prototype.put = function (\n    pathString,\n    data,\n    onComplete,\n    hash\n  ) {\n    if (hash !== undefined) {\n      hash = newHash();\n    }\n    oldPut.call(this, pathString, data, onComplete, hash);\n  };\n  return function () {\n    PersistentConnection.prototype.put = oldPut;\n  };\n};\n\nexport const ConnectionTarget = RepoInfo;\n\nexport const queryIdentifier = function (query: Query) {\n  return query._delegate._queryIdentifier;\n};\n\n/**\n * Forces the RepoManager to create Repos that use ReadonlyRestClient instead of PersistentConnection.\n */\nexport const forceRestClient = function (forceRestClient: boolean) {\n  repoManagerForceRestClient(forceRestClient);\n};\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseNamespace } from '@firebase/app-types';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport { FirebaseAuthInternal } from '@firebase/auth-interop-types';\nimport { Component, ComponentType } from '@firebase/component';\nimport * as types from '@firebase/database-types';\nimport { CONSTANTS, isNodeSdk } from '@firebase/util';\nimport { Client } from 'faye-websocket';\n\nimport { enableLogging } from '../exp/index';\nimport { Database } from '../src/api/Database';\nimport * as INTERNAL from '../src/api/internal';\nimport { DataSnapshot, Query, Reference } from '../src/api/Reference';\nimport * as TEST_ACCESS from '../src/api/test_access';\nimport { setSDKVersion } from '../src/core/version';\nimport { setWebSocketImpl } from '../src/realtime/WebSocketConnection';\n\nimport { name, version } from './package.json';\n\nsetWebSocketImpl(Client);\n\nconst ServerValue = Database.ServerValue;\n\n/**\n * A one off register function which returns a database based on the app and\n * passed database URL. (Used by the Admin SDK)\n *\n * @param app - A valid FirebaseApp-like object\n * @param url - A valid Firebase databaseURL\n * @param version - custom version e.g. firebase-admin version\n * @param nodeAdmin - true if the SDK is being initialized from Firebase Admin.\n */\nexport function initStandalone(\n  app: FirebaseApp,\n  url: string,\n  version: string,\n  nodeAdmin = true\n) {\n  CONSTANTS.NODE_ADMIN = nodeAdmin;\n  return INTERNAL.initStandalone({\n    app,\n    url,\n    version,\n    // firebase-admin-node's app.INTERNAL implements FirebaseAuthInternal interface\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    customAuthImpl: (app as any).INTERNAL as FirebaseAuthInternal,\n    namespace: {\n      Reference,\n      Query,\n      Database,\n      DataSnapshot,\n      enableLogging,\n      INTERNAL,\n      ServerValue,\n      TEST_ACCESS\n    },\n    nodeAdmin\n  });\n}\n\nexport function registerDatabase(instance: FirebaseNamespace) {\n  // set SDK_VERSION\n  setSDKVersion(instance.SDK_VERSION);\n\n  // Register the Database Service with the 'firebase' namespace.\n  const namespace = (instance as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component(\n      'database-compat',\n      (container, { instanceIdentifier: url }) => {\n        /* Dependencies */\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app-compat').getImmediate();\n        const databaseExp = container\n          .getProvider('database-exp')\n          .getImmediate({ identifier: url });\n        return new Database(databaseExp, app);\n      },\n      ComponentType.PUBLIC\n    )\n      .setServiceProps(\n        // firebase.database namespace properties\n        {\n          Reference,\n          Query,\n          Database,\n          DataSnapshot,\n          enableLogging,\n          INTERNAL,\n          ServerValue,\n          TEST_ACCESS\n        }\n      )\n      .setMultipleInstances(true)\n  );\n\n  instance.registerVersion(name, version, 'node');\n\n  if (isNodeSdk()) {\n    module.exports = Object.assign({}, namespace, { initStandalone });\n  }\n}\n\ntry {\n  // If @firebase/app is not present, skip registering database.\n  // It could happen when this package is used in firebase-admin which doesn't depend on @firebase/app.\n  // Previously firebase-admin depends on @firebase/app, which causes version conflict on\n  // @firebase/app when used together with the js sdk. More detail:\n  // https://github.com/firebase/firebase-js-sdk/issues/1696#issuecomment-501546596\n  // eslint-disable-next-line import/no-extraneous-dependencies, @typescript-eslint/no-require-imports\n  const firebase = require('@firebase/app-compat').default;\n  registerDatabase(firebase);\n} catch (err) {\n  // catch and ignore 'MODULE_NOT_FOUND' error in firebase-admin context\n  // we can safely ignore this error because RTDB in firebase-admin works without @firebase/app\n  if (err.code !== 'MODULE_NOT_FOUND') {\n    throw err;\n  }\n}\n\n// Types to export for the admin SDK\nexport { Database, Query, Reference, enableLogging, ServerValue };\n\nexport { OnDisconnect } from '../src/api/onDisconnect';\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    database?: {\n      (app?: FirebaseApp): types.FirebaseDatabase;\n      enableLogging: typeof types.enableLogging;\n      ServerValue: types.ServerValue;\n      Database: typeof types.FirebaseDatabase;\n    };\n  }\n  interface FirebaseApp {\n    database?(): types.FirebaseDatabase;\n  }\n}\nexport { DataSnapshot } from '../src/api/Reference';\n"], "names": ["stringify", "jsonEval", "contains", "<PERSON><PERSON>", "stringToByteArray", "Sha1", "base64", "assert", "LogLevel", "isNodeSdk", "errorPrefixFxn", "MAX_NODE", "__extends", "validateArgCount", "validate<PERSON><PERSON>back", "onValue", "onChildAdded", "onChildRemoved", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChildMoved", "errorPrefix", "validateContextObject", "off", "get", "Deferred", "query", "limitToFirst", "limitToLast", "orderByChild", "orderByKey", "orderByPriority", "orderByValue", "startAt", "startAfter", "endAt", "endBefore", "equalTo", "_ReferenceImpl", "database", "_QueryImpl", "child", "set", "update", "setWithPriority", "remove", "runTransaction", "setPriority", "push", "ExpOnDisconnect", "connectDatabaseEmulator", "refFromURL", "ref", "goOffline", "goOnline", "serverTimestamp", "increment", "deepCopy", "__read", "base64Encode", "__values", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "safeGet", "isAdmin", "isValidFormat", "isEmpty", "isReactNative", "initStandalone", "Provider", "ComponentContainer", "Component", "_repoManagerDatabaseFromApp", "Client", "CONSTANTS", "INTERNAL.initStandalone"], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;AASA;;;;IAOE,2BAAoB,WAAoB;QAApB,gBAAW,GAAX,WAAW,CAAS;;QALhC,YAAO,GAAG,WAAW,CAAC;KAKc;;;;;IAM5C,+BAAG,GAAH,UAAI,GAAW,EAAE,KAAqB;QACpC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;SACtD;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAEA,cAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SACrE;KACF;;;;IAKD,+BAAG,GAAH,UAAI,GAAW;QACb,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAOC,aAAQ,CAAC,SAAS,CAAC,CAAC;SAC5B;KACF;IAED,kCAAM,GAAN,UAAO,GAAW;QAChB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;KACtD;IAID,yCAAa,GAAb,UAAc,IAAY;QACxB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KAC5B;IAED,oCAAQ,GAAR;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;KACpC;IACH,wBAAC;AAAD,CAAC;;AC1ED;;;;;;;;;;;;;;;;AAmBA;;;;AAIA;IAAA;QACU,WAAM,GAA6B,EAAE,CAAC;QAqB9C,sBAAiB,GAAG,IAAI,CAAC;KAC1B;IApBC,2BAAG,GAAH,UAAI,GAAW,EAAE,KAAqB;QACpC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC1B;KACF;IAED,2BAAG,GAAH,UAAI,GAAW;QACb,IAAIC,aAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACzB;QACD,OAAO,IAAI,CAAC;KACb;IAED,8BAAM,GAAN,UAAO,GAAW;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACzB;IAGH,oBAAC;AAAD,CAAC;;AC9CD;;;;;;;;;;;;;;;;AAsBA;;;;;;;;;AASA,IAAM,gBAAgB,GAAG,UACvB,cAAsB;IAEtB,IAAI;;;QAGF,IACE,OAAO,MAAM,KAAK,WAAW;YAC7B,OAAO,MAAM,CAAC,cAAc,CAAC,KAAK,WAAW,EAC7C;;YAEA,IAAM,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;YAC1C,UAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YACjD,UAAU,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YAC3C,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;SAC1C;KACF;IAAC,OAAO,CAAC,EAAE,GAAE;;;IAId,OAAO,IAAI,aAAa,EAAE,CAAC;AAC7B,CAAC,CAAC;AAEF;AACO,IAAM,iBAAiB,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAElE;AACO,IAAM,cAAc,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;;AC1DhE;;;;;;;;;;;;;;;;AAwCA,IAAM,SAAS,GAAG,IAAIC,eAAM,CAAC,oBAAoB,CAAC,CAAC;AAEnD;;;AAGO,IAAM,aAAa,GAAiB,CAAC;IAC1C,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,OAAO;QACL,OAAO,EAAE,EAAE,CAAC;KACb,CAAC;AACJ,CAAC,GAAG,CAAC;AAEL;;;;;AAKO,IAAM,IAAI,GAAG,UAAU,GAAW;IACvC,IAAM,SAAS,GAAGC,sBAAiB,CAAC,GAAG,CAAC,CAAC;IACzC,IAAM,IAAI,GAAG,IAAIC,SAAI,EAAE,CAAC;IACxB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACvB,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAChC,OAAOC,WAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC3C,CAAC,CAAC;AAEF,IAAM,gBAAgB,GAAG;IAAU,iBAAqB;SAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;QAArB,4BAAqB;;IACtD,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,IAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACvB,IACE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;aACjB,GAAG;gBACF,OAAO,GAAG,KAAK,QAAQ;;gBAEvB,OAAQ,GAAW,CAAC,MAAM,KAAK,QAAQ,CAAC,EAC1C;YACA,OAAO,IAAI,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC9C;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAClC,OAAO,IAAIN,cAAS,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAM;YACL,OAAO,IAAI,GAAG,CAAC;SAChB;QACD,OAAO,IAAI,GAAG,CAAC;KAChB;IAED,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF;;;AAGO,IAAI,MAAM,GAAiC,IAAI,CAAC;AAEvD;;;AAGA,IAAI,SAAS,GAAG,IAAI,CAAC;AAErB;;;;;AAKO,IAAM,aAAa,GAAG,UAC3B,OAAgD,EAChD,UAAoB;IAEpBO,WAAM,CACJ,CAAC,UAAU,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,EACpD,4CAA4C,CAC7C,CAAC;IACF,IAAI,OAAO,KAAK,IAAI,EAAE;QACpB,SAAS,CAAC,QAAQ,GAAGC,iBAAQ,CAAC,OAAO,CAAC;QACtC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,UAAU,EAAE;YACd,cAAc,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;SAC7C;KACF;SAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACxC,MAAM,GAAG,OAAO,CAAC;KAClB;SAAM;QACL,MAAM,GAAG,IAAI,CAAC;QACd,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAC1C;AACH,CAAC,CAAC;AAEK,IAAM,GAAG,GAAG;IAAU,iBAAqB;SAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;QAArB,4BAAqB;;IAChD,IAAI,SAAS,KAAK,IAAI,EAAE;QACtB,SAAS,GAAG,KAAK,CAAC;QAClB,IAAI,MAAM,KAAK,IAAI,IAAI,cAAc,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;YACrE,aAAa,CAAC,IAAI,CAAC,CAAC;SACrB;KACF;IAED,IAAI,MAAM,EAAE;QACV,IAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACtD,MAAM,CAAC,OAAO,CAAC,CAAC;KACjB;AACH,CAAC,CAAC;AAEK,IAAM,UAAU,GAAG,UACxB,MAAc;IAEd,OAAO;QAAU,iBAAqB;aAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;YAArB,4BAAqB;;QACpC,GAAG,oCAAC,MAAM,gBAAK,OAAO,IAAE;KACzB,CAAC;AACJ,CAAC,CAAC;AAEK,IAAM,KAAK,GAAG;IAAU,iBAAoB;SAApB,UAAoB,EAApB,qBAAoB,EAApB,IAAoB;QAApB,4BAAoB;;IACjD,IAAM,OAAO,GAAG,2BAA2B,GAAG,gBAAgB,oDAAI,OAAO,GAAC,CAAC;IAC3E,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC3B,CAAC,CAAC;AAQK,IAAM,IAAI,GAAG;IAAU,iBAAqB;SAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;QAArB,4BAAqB;;IACjD,IAAM,OAAO,GAAG,oBAAoB,GAAG,gBAAgB,oDAAI,OAAO,GAAC,CAAC;IACpE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1B,CAAC,CAAC;AA6BF;;;AAGO,IAAM,mBAAmB,GAAG,UAAU,IAAa;IACxD,QACE,OAAO,IAAI,KAAK,QAAQ;SACvB,IAAI,KAAK,IAAI;YACZ,IAAI,KAAK,MAAM,CAAC,iBAAiB;YACjC,IAAI,KAAK,MAAM,CAAC,iBAAiB,CAAC,EACpC;AACJ,CAAC,CAAC;AAEK,IAAM,mBAAmB,GAAG,UAAU,EAAc;IACzD,IAAIC,cAAS,EAAE,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;QACrD,EAAE,EAAE,CAAC;KACN;SAAM;;;QAIL,IAAI,QAAM,GAAG,KAAK,CAAC;QACnB,IAAM,WAAS,GAAG;YAChB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAClB,UAAU,CAAC,WAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtC,OAAO;aACR;YAED,IAAI,CAAC,QAAM,EAAE;gBACX,QAAM,GAAG,IAAI,CAAC;gBACd,EAAE,EAAE,CAAC;aACN;SACF,CAAC;QAEF,IAAI,QAAQ,CAAC,gBAAgB,EAAE;YAC7B,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,WAAS,EAAE,KAAK,CAAC,CAAC;;YAEhE,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAS,EAAE,KAAK,CAAC,CAAC;;SAEnD;aAAM,IAAK,QAAgB,CAAC,WAAW,EAAE;;;YAGvC,QAAgB,CAAC,WAAW,CAAC,oBAAoB,EAAE;gBAClD,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;oBACtC,WAAS,EAAE,CAAC;iBACb;aACF,CAAC,CAAC;;;YAGF,MAAc,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAS,CAAC,CAAC;;;;SAKlD;KACF;AACH,CAAC,CAAC;AAEF;;;AAGO,IAAM,QAAQ,GAAG,YAAY,CAAC;AAErC;;;AAGO,IAAM,QAAQ,GAAG,YAAY,CAAC;AAErC;;;AAGO,IAAM,WAAW,GAAG,UAAU,CAAS,EAAE,CAAS;IACvD,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,CAAC,CAAC;KACV;SAAM,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,EAAE;QAC3C,OAAO,CAAC,CAAC,CAAC;KACX;SAAM,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,EAAE;QAC3C,OAAO,CAAC,CAAC;KACV;SAAM;QACL,IAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,EAC3B,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;aACtE;iBAAM;gBACL,OAAO,CAAC,CAAC,CAAC;aACX;SACF;aAAM,IAAI,MAAM,KAAK,IAAI,EAAE;YAC1B,OAAO,CAAC,CAAC;SACV;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;SACvB;KACF;AACH,CAAC,CAAC;AAeK,IAAM,UAAU,GAAG,UACxB,GAAW,EACX,GAA6B;IAE7B,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;QACrB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;KACjB;SAAM;QACL,MAAM,IAAI,KAAK,CACb,wBAAwB,GAAG,GAAG,GAAG,eAAe,GAAGT,cAAS,CAAC,GAAG,CAAC,CAClE,CAAC;KACH;AACH,CAAC,CAAC;AAEK,IAAM,iBAAiB,GAAG,UAAU,GAAY;IACrD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;QAC3C,OAAOA,cAAS,CAAC,GAAG,CAAC,CAAC;KACvB;IAED,IAAM,IAAI,GAAG,EAAE,CAAC;;IAEhB,KAAK,IAAM,CAAC,IAAI,GAAG,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACd;;IAGD,IAAI,CAAC,IAAI,EAAE,CAAC;IACZ,IAAI,GAAG,GAAG,GAAG,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,KAAK,CAAC,EAAE;YACX,GAAG,IAAI,GAAG,CAAC;SACZ;QACD,GAAG,IAAIA,cAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,GAAG,IAAI,GAAG,CAAC;QACX,GAAG,IAAI,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC;IAED,GAAG,IAAI,GAAG,CAAC;IACX,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF;;;;;;AAMO,IAAM,iBAAiB,GAAG,UAC/B,GAAW,EACX,OAAe;IAEf,IAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IAEvB,IAAI,GAAG,IAAI,OAAO,EAAE;QAClB,OAAO,CAAC,GAAG,CAAC,CAAC;KACd;IAED,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,OAAO,EAAE;QACrC,IAAI,CAAC,GAAG,OAAO,GAAG,GAAG,EAAE;YACrB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;SAC9C;KACF;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEF;;;;;;SAMgB,IAAI,CAAC,GAAW,EAAE,EAAmC;IACnE,KAAK,IAAM,GAAG,IAAI,GAAG,EAAE;QACrB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC3B,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SACnB;KACF;AACH,CAAC;AAeD;;;;;;;AAOO,IAAM,qBAAqB,GAAG,UAAU,CAAS;IACtDO,WAAM,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC;IAEvD,IAAM,KAAK,GAAG,EAAE,EACd,KAAK,GAAG,EAAE,CAAC;IACb,IAAM,IAAI,GAAG,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;;IAInB,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,CAAC,GAAG,CAAC,CAAC;QACN,CAAC,GAAG,CAAC,CAAC;QACN,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;KACjC;SAAM;QACL,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACV,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAEhB,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;;YAE9B,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YACxD,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YACd,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;SAClE;aAAM;;YAEL,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;SACnD;KACF;;IAGD,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACzB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACvB;IACD,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACzB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACvB;IACD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACrB,IAAI,CAAC,OAAO,EAAE,CAAC;IACf,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;IAG1B,IAAI,aAAa,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1B,IAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;SACzB;QACD,aAAa,GAAG,aAAa,GAAG,OAAO,CAAC;KACzC;IACD,OAAO,aAAa,CAAC,WAAW,EAAE,CAAC;AACrC,CAAC,CAAC;AAEF;;;;AAIO,IAAM,8BAA8B,GAAG;IAC5C,OAAO,CAAC,EACN,OAAO,MAAM,KAAK,QAAQ;QAC1B,MAAM,CAAC,QAAQ,CAAC;QAChB,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;QAC7B,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CACtC,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAGO,IAAM,iBAAiB,GAAG;;IAE/B,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,CAAC;AACvE,CAAC,CAAC;AAyBF;;;AAGO,IAAM,eAAe,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAE/D;;;AAGO,IAAM,cAAc,GAAG,CAAC,UAAU,CAAC;AAE1C;;;AAGO,IAAM,cAAc,GAAG,UAAU,CAAC;AAEzC;;;AAGO,IAAM,WAAW,GAAG,UAAU,GAAW;IAC9C,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC7B,IAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,MAAM,IAAI,cAAc,IAAI,MAAM,IAAI,cAAc,EAAE;YACxD,OAAO,MAAM,CAAC;SACf;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;AAiBO,IAAM,cAAc,GAAG,UAAU,EAAc;IACpD,IAAI;QACF,EAAE,EAAE,CAAC;KACN;IAAC,OAAO,CAAC,EAAE;;QAEV,UAAU,CAAC;;;;;YAKT,IAAM,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,CAAC;SACT,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACnB;AACH,CAAC,CAAC;AAsDF;;;;;;;;;AASO,IAAM,qBAAqB,GAAG,UACnC,EAAc,EACd,IAAY;IAEZ,IAAM,OAAO,GAAoB,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;;IAEtD,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAK,OAAe,CAAC,OAAO,CAAC,EAAE;;QAE3D,OAAe,CAAC,OAAO,CAAC,EAAE,CAAC;KAC7B;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;;ACxnBD;;;;;;;;;;;;;;;;AA2BA;;;;;AAMA;;;;;IAQE,cAAY,YAA+B,EAAE,QAAiB;QAC5D,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,OAAO,GAAI,YAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;YAGnD,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACvC,MAAM,EAAE,CAAC;iBACV;aACF;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YAE7B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,YAAwB,CAAC;YACxC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC3B;KACF;IAED,uBAAQ,GAAR;QACE,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1B,UAAU,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACrC;SACF;QAED,OAAO,UAAU,IAAI,GAAG,CAAC;KAC1B;IACH,WAAC;AAAD,CAAC,IAAA;SAMe,YAAY,CAAC,IAAU;IACrC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACzC,OAAO,IAAI,CAAC;KACb;IAED,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtC,CAAC;AAED;;;SAGgB,aAAa,CAAC,IAAU;IACtC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9C,CAAC;SAEe,YAAY,CAAC,IAAU;IACrC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QAClC,QAAQ,EAAE,CAAC;KACZ;IACD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC1C,CAAC;AAgED;;;SAGgB,WAAW,CAAC,IAAU;IACpC,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AAC/C;;ACxKA;;;;;;;;;;;;;;;;AA8CA;;;;AAIO,IAAM,mBAAmB,GAAG,8BAA8B,CAAC;AAa3D,IAAM,iBAAiB,GAAG,UAAU,UAAkB;IAC3D,QACE,OAAO,UAAU,KAAK,QAAQ;QAC9B,UAAU,CAAC,MAAM,KAAK,CAAC;QACvB,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EACrC;AACJ,CAAC,CAAC;AAwPK,IAAM,iBAAiB,GAAG,UAC/B,MAAc,EACd,SAAiB,EACjB,QAAiB;IAEjB,IAAI,QAAQ,IAAI,SAAS,KAAK,SAAS,EAAE;QACvC,OAAO;KACR;IAED,QAAQ,SAAS;QACf,KAAK,OAAO,CAAC;QACb,KAAK,aAAa,CAAC;QACnB,KAAK,eAAe,CAAC;QACrB,KAAK,eAAe,CAAC;QACrB,KAAK,aAAa;YAChB,MAAM;QACR;YACE,MAAM,IAAI,KAAK,CACbG,gBAAc,CAAC,MAAM,EAAE,WAAW,CAAC;gBACjC,wEAAwE;gBACxE,oCAAoC,CACvC,CAAC;KACL;AACH,CAAC,CAAC;AAsBK,IAAM,kBAAkB,GAAG,UAChC,MAAc,EACd,YAAoB,EACpB,UAAkB,EAClB,QAAiB;IAEjB,IAAI,QAAQ,IAAI,UAAU,KAAK,SAAS,EAAE;QACxC,OAAO;KACR;IAED,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CACbA,gBAAc,CAAC,MAAM,EAAE,YAAY,CAAC;YAClC,yBAAyB;YACzB,UAAU;YACV,yCAAyC;YACzC,2CAA2C,CAC9C,CAAC;KACH;AACH,CAAC,CAAC;AAgBK,IAAM,oBAAoB,GAAG,UAAU,MAAc,EAAE,IAAU;IACtE,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,2CAA2C,CAAC,CAAC;KACvE;AACH,CAAC,CAAC;AAuBK,IAAM,eAAe,GAAG,UAC7B,MAAc,EACd,YAAoB,EACpB,IAAa,EACb,QAAiB;IAEjB,IAAI,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;QAClC,OAAO;KACR;IACD,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;QAC7B,MAAM,IAAI,KAAK,CACbA,gBAAc,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,oBAAoB,CAC5D,CAAC;KACH;AACH,CAAC;;ACtbD;;;;;;;;;;;;;;;;AAiJA;IACE,mBAAmB,IAAY,EAAS,IAAU;QAA/B,SAAI,GAAJ,IAAI,CAAQ;QAAS,SAAI,GAAJ,IAAI,CAAM;KAAI;IAE/C,cAAI,GAAX,UAAY,IAAY,EAAE,IAAU;QAClC,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAClC;IACH,gBAAC;AAAD,CAAC;;ACvJD;;;;;;;;;;;;;;;;AAqBA;IAAA;KA+CC;;;;;IAtCC,0BAAU,GAAV;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChC;;;;;;;;IASD,mCAAmB,GAAnB,UAAoB,OAAa,EAAE,OAAa;QAC9C,IAAM,UAAU,GAAG,IAAI,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,IAAM,UAAU,GAAG,IAAI,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;KACnD;;;;;IAMD,uBAAO,GAAP;;QAEE,OAAQ,SAAiB,CAAC,GAAG,CAAC;KAC/B;IAcH,YAAC;AAAD,CAAC;;ACpED;;;;;;;;;;;;;;;;AAwBA,IAAIC,UAAc,CAAC;AAMZ,IAAM,gBAAgB,GAAG,UAAU,QAAyB;IACjE,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,OAAO,SAAS,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;KACpD;SAAM;QACL,OAAO,SAAS,GAAG,QAAQ,CAAC;KAC7B;AACH,CAAC,CAAC;AAEF;;;AAGO,IAAM,oBAAoB,GAAG,UAAU,YAAkB;IAC9D,IAAI,YAAY,CAAC,UAAU,EAAE,EAAE;QAC7B,IAAM,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;QAC/BJ,WAAM,CACJ,OAAO,GAAG,KAAK,QAAQ;YACrB,OAAO,GAAG,KAAK,QAAQ;aACtB,OAAO,GAAG,KAAK,QAAQ,IAAIL,aAAQ,CAAC,GAAgB,EAAE,KAAK,CAAC,CAAC,EAChE,sCAAsC,CACvC,CAAC;KACH;SAAM;QACLK,WAAM,CACJ,YAAY,KAAKI,UAAQ,IAAI,YAAY,CAAC,OAAO,EAAE,EACnD,8BAA8B,CAC/B,CAAC;KACH;;IAEDJ,WAAM,CACJ,YAAY,KAAKI,UAAQ,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,EACjE,oDAAoD,CACrD,CAAC;AACJ,CAAC;;AC7DD;;;;;;;;;;;;;;;;AAkCA,IAAI,yBAAkD,CAAC;AAEvD;;;;;AAKA;;;;;;IAsBE,kBACmB,MAA6C,EACtD,aAAmE;QAAnE,8BAAA,EAAA,gBAAsB,QAAQ,CAAC,yBAAyB,CAAC,UAAU;QAD1D,WAAM,GAAN,MAAM,CAAuC;QACtD,kBAAa,GAAb,aAAa,CAAsD;QATrE,cAAS,GAAkB,IAAI,CAAC;QAWtCJ,WAAM,CACJ,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EACjD,0DAA0D,CAC3D,CAAC;QAEF,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAC1C;IA/BD,sBAAW,qCAAyB;aAIpC;YACE,OAAO,yBAAyB,CAAC;SAClC;aAND,UAAqC,GAA4B;YAC/D,yBAAyB,GAAG,GAAG,CAAC;SACjC;;;OAAA;;IAgCD,6BAAU,GAAV;QACE,OAAO,IAAI,CAAC;KACb;;IAGD,8BAAW,GAAX;QACE,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;IAGD,iCAAc,GAAd,UAAe,eAAqB;QAClC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;KACnD;;IAGD,oCAAiB,GAAjB,UAAkB,SAAiB;;QAEjC,IAAI,SAAS,KAAK,WAAW,EAAE;YAC7B,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;aAAM;YACL,OAAO,QAAQ,CAAC,yBAAyB,CAAC,UAAU,CAAC;SACtD;KACF;;IAGD,2BAAQ,GAAR,UAAS,IAAU;QACjB,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;YAC7C,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;aAAM;YACL,OAAO,QAAQ,CAAC,yBAAyB,CAAC,UAAU,CAAC;SACtD;KACF;IACD,2BAAQ,GAAR;QACE,OAAO,KAAK,CAAC;KACd;;IAGD,0CAAuB,GAAvB,UAAwB,SAAiB,EAAE,SAAe;QACxD,OAAO,IAAI,CAAC;KACb;;IAGD,uCAAoB,GAApB,UAAqB,SAAiB,EAAE,YAAkB;QACxD,IAAI,SAAS,KAAK,WAAW,EAAE;YAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;SAC1C;aAAM,IAAI,YAAY,CAAC,OAAO,EAAE,IAAI,SAAS,KAAK,WAAW,EAAE;YAC9D,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,QAAQ,CAAC,yBAAyB,CAAC,UAAU,CAAC,oBAAoB,CACvE,SAAS,EACT,YAAY,CACb,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACtC;KACF;;IAGD,8BAAW,GAAX,UAAY,IAAU,EAAE,YAAkB;QACxC,IAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,YAAY,CAAC;SACrB;aAAM,IAAI,YAAY,CAAC,OAAO,EAAE,IAAI,KAAK,KAAK,WAAW,EAAE;YAC1D,OAAO,IAAI,CAAC;SACb;aAAM;YACLA,WAAM,CACJ,KAAK,KAAK,WAAW,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAClD,4CAA4C,CAC7C,CAAC;YAEF,OAAO,IAAI,CAAC,oBAAoB,CAC9B,KAAK,EACL,QAAQ,CAAC,yBAAyB,CAAC,UAAU,CAAC,WAAW,CACvD,YAAY,CAAC,IAAI,CAAC,EAClB,YAAY,CACb,CACF,CAAC;SACH;KACF;;IAGD,0BAAO,GAAP;QACE,OAAO,KAAK,CAAC;KACd;;IAGD,8BAAW,GAAX;QACE,OAAO,CAAC,CAAC;KACV;;IAGD,+BAAY,GAAZ,UAAa,KAAY,EAAE,MAAoC;QAC7D,OAAO,KAAK,CAAC;KACd;IACD,sBAAG,GAAH,UAAI,YAAsB;QACxB,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE;YACjD,OAAO;gBACL,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE;aACtC,CAAC;SACH;aAAM;YACL,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;SACxB;KACF;;IAGD,uBAAI,GAAJ;QACE,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;gBACjC,MAAM;oBACJ,WAAW;wBACX,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAqB,CAAC;wBAC7D,GAAG,CAAC;aACP;YAED,IAAM,IAAI,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC;YACrB,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,MAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAgB,CAAC,CAAC;aACxD;iBAAM;gBACL,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;aACvB;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;;;;IAMD,2BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,4BAAS,GAAT,UAAU,KAAW;QACnB,IAAI,KAAK,KAAK,QAAQ,CAAC,yBAAyB,CAAC,UAAU,EAAE;YAC3D,OAAO,CAAC,CAAC;SACV;aAAM,IAAI,KAAK,YAAY,QAAQ,CAAC,yBAAyB,EAAE;YAC9D,OAAO,CAAC,CAAC,CAAC;SACX;aAAM;YACLA,WAAM,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,mBAAmB,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAiB,CAAC,CAAC;SACnD;KACF;;;;IAKO,qCAAkB,GAA1B,UAA2B,SAAmB;QAC5C,IAAM,aAAa,GAAG,OAAO,SAAS,CAAC,MAAM,CAAC;QAC9C,IAAM,YAAY,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC;QACxC,IAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACpE,IAAM,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAClEA,WAAM,CAAC,UAAU,IAAI,CAAC,EAAE,qBAAqB,GAAG,aAAa,CAAC,CAAC;QAC/DA,WAAM,CAAC,SAAS,IAAI,CAAC,EAAE,qBAAqB,GAAG,YAAY,CAAC,CAAC;QAC7D,IAAI,UAAU,KAAK,SAAS,EAAE;;YAE5B,IAAI,YAAY,KAAK,QAAQ,EAAE;;gBAE7B,OAAO,CAAC,CAAC;aACV;iBAAM;;gBAEL,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE;oBAClC,OAAO,CAAC,CAAC,CAAC;iBACX;qBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;oBAC3C,OAAO,CAAC,CAAC;iBACV;qBAAM;oBACL,OAAO,CAAC,CAAC;iBACV;aACF;SACF;aAAM;YACL,OAAO,SAAS,GAAG,UAAU,CAAC;SAC/B;KACF;IACD,4BAAS,GAAT;QACE,OAAO,IAAI,CAAC;KACb;IACD,4BAAS,GAAT;QACE,OAAO,IAAI,CAAC;KACb;IACD,yBAAM,GAAN,UAAO,KAAW;QAChB,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;YAC7B,IAAM,SAAS,GAAG,KAAiB,CAAC;YACpC,QACE,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;gBAChC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,EAClD;SACH;aAAM;YACL,OAAO,KAAK,CAAC;SACd;KACF;;;;;IAvNM,yBAAgB,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAwNtE,eAAC;CArOD;;ACzCA;;;;;;;;;;;;;;;;AAuBA,IAAI,YAAkC,CAAC;AACvC,IAAI,QAAc,CAAC;AAUnB;IAAmCK,uCAAK;IAAxC;;KAoCC;IAnCC,+BAAO,GAAP,UAAQ,CAAY,EAAE,CAAY;QAChC,IAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QACvC,IAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QACvC,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,QAAQ,KAAK,CAAC,EAAE;YAClB,OAAO,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACL,OAAO,QAAQ,CAAC;SACjB;KACF;IACD,mCAAW,GAAX,UAAY,IAAU;QACpB,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;KACtC;IACD,2CAAmB,GAAnB,UAAoB,OAAa,EAAE,OAAa;QAC9C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;KAC7D;IACD,+BAAO,GAAP;;QAEE,OAAQ,SAAiB,CAAC,GAAG,CAAC;KAC/B;IACD,+BAAO,GAAP;QACE,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC3E;IAED,gCAAQ,GAAR,UAAS,UAAmB,EAAE,IAAY;QACxC,IAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;QAC9C,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC;KAC3E;;;;IAKD,gCAAQ,GAAR;QACE,OAAO,WAAW,CAAC;KACpB;IACH,oBAAC;AAAD,CApCA,CAAmC,KAAK,GAoCvC;AAEM,IAAM,cAAc,GAAG,IAAI,aAAa,EAAE;;ACxEjD;;;;;;;;;;;;;;;;AA6DA;;;;;AAKA;IAAA;QACE,cAAS,GAAG,KAAK,CAAC;QAClB,cAAS,GAAG,KAAK,CAAC;QAClB,kBAAa,GAAG,KAAK,CAAC;QACtB,mBAAc,GAAG,KAAK,CAAC;QACvB,YAAO,GAAG,KAAK,CAAC;QAChB,gBAAW,GAAG,KAAK,CAAC;QACpB,kBAAa,GAAG,KAAK,CAAC;QACtB,WAAM,GAAG,CAAC,CAAC;QACX,cAAS,GAAG,EAAE,CAAC;QACf,qBAAgB,GAAmB,IAAI,CAAC;QACxC,oBAAe,GAAG,EAAE,CAAC;QACrB,mBAAc,GAAmB,IAAI,CAAC;QACtC,kBAAa,GAAG,EAAE,CAAC;QACnB,WAAM,GAAkB,cAAc,CAAC;KA0HxC;IAxHC,8BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,mCAAa,GAAb;QACE,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IAED,kCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;;;IAKD,oCAAc,GAAd;QACE,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;;;;;YAKzB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;aAAM;YACL,OAAO,IAAI,CAAC,SAAS,8BAA4C;SAClE;KACF;;;;IAKD,wCAAkB,GAAlB;QACEL,WAAM,CAAC,IAAI,CAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;;;;;IAMD,uCAAiB,GAAjB;QACEA,WAAM,CAAC,IAAI,CAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;aAAM;YACL,OAAO,QAAQ,CAAC;SACjB;KACF;IAED,4BAAM,GAAN;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;;;IAKD,sCAAgB,GAAhB;QACEA,WAAM,CAAC,IAAI,CAAC,OAAO,EAAE,gCAAgC,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;;;;;IAMD,qCAAe,GAAf;QACEA,WAAM,CAAC,IAAI,CAAC,OAAO,EAAE,gCAAgC,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;aAAM;YACL,OAAO,QAAQ,CAAC;SACjB;KACF;IAED,8BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;;;IAKD,sCAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC;KAChD;;;;IAKD,8BAAQ,GAAR;QACEA,WAAM,CAAC,IAAI,CAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,8BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,kCAAY,GAAZ;QACE,OAAO,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;KAC5D;IAED,+BAAS,GAAT;QACE,OAAO,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC;KAC9D;IAED,0BAAI,GAAJ;QACE,IAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,IAAI,CAAC;KACb;IACH,kBAAC;AAAD,CAAC;;AC1MD;;;;;;;;;;;;;;;;;IA+BE,sBAAqB,SAA0B;QAA1B,cAAS,GAAT,SAAS,CAAiB;KAAI;IAEnD,6BAAM,GAAN,UAAO,UAAsC;QAC3CM,qBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChEC,qBAAgB,CAAC,qBAAqB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QACvC,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,6BAAM,GAAN,UAAO,UAAsC;QAC3CD,qBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChEC,qBAAgB,CAAC,qBAAqB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QACvC,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,0BAAG,GAAH,UAAI,KAAc,EAAE,UAAsC;QACxDD,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7DC,qBAAgB,CAAC,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACrE,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,sCAAe,GAAf,UACE,KAAc,EACd,QAAgC,EAChC,UAAsC;QAEtCD,qBAAgB,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACzEC,qBAAgB,CACd,8BAA8B,EAC9B,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;QACF,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,6BAAM,GAAN,UACE,aAAwB,EACxB,UAAsC;QAEtCD,qBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YAChC,IAAM,gBAAgB,GAA6B,EAAE,CAAC;YACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC7C,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;aAC7C;YACD,aAAa,GAAG,gBAAgB,CAAC;YACjC,IAAI,CACF,sHAAsH;gBACpH,0GAA0G,CAC7G,CAAC;SACH;QACDC,qBAAgB,CAAC,qBAAqB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IACH,mBAAC;AAAD,CAAC;;ACxHD;;;;;;;;;;;;;;;;AAqBA;;;;IAIE,2BAAmB,SAAkB,EAAS,QAAsB;QAAjD,cAAS,GAAT,SAAS,CAAS;QAAS,aAAQ,GAAR,QAAQ,CAAc;KAAI;;;IAIxE,kCAAM,GAAN;QACED,qBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;KACxE;IACH,wBAAC;AAAD,CAAC;;ACjCD;;;;;;;;;;;;;;;;AAqFA;AAEA;;;;;IAKE,sBACW,SAAmB,EACnB,SAA0B;QAD1B,cAAS,GAAT,SAAS,CAAU;QACnB,cAAS,GAAT,SAAS,CAAiB;KACjC;;;;;;;IAQJ,0BAAG,GAAH;QACEA,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;KAC7B;;;;;;IAOD,gCAAS,GAAT;QACEA,qBAAgB,CAAC,wBAAwB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;KACnC;;;IAID,6BAAM,GAAN;;QAEEA,qBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;;;;;;IAOD,6BAAM,GAAN;QACEA,qBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;;;;;;;IAQD,4BAAK,GAAL,UAAM,IAAY;QAChBA,qBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;;QAE/D,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACpB,kBAAkB,CAAC,oBAAoB,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KACrE;;;;;;;IAQD,+BAAQ,GAAR,UAAS,IAAY;QACnBA,qBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClE,kBAAkB,CAAC,uBAAuB,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACtC;;;;;;IAOD,kCAAW,GAAX;QACEA,qBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;;;;;;;;;IAUD,8BAAO,GAAP,UAAQ,MAAkD;QAA1D,iBAMC;QALCA,qBAAgB,CAAC,sBAAsB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACjEC,qBAAgB,CAAC,sBAAsB,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,eAAe;YAC3C,OAAA,MAAM,CAAC,IAAI,YAAY,CAAC,KAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;SAAA,CAC1D,CAAC;KACH;;;;;IAMD,kCAAW,GAAX;QACED,qBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;KACrC;IAED,sBAAI,6BAAG;aAAP;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;SAC3B;;;OAAA;;;;;IAMD,kCAAW,GAAX;QACEA,qBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;;;;;IAMD,6BAAM,GAAN;QACEA,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KAC1D;IAED,sBAAI,6BAAG;aAAP;YACE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;SACtB;;;OAAA;IACH,mBAAC;AAAD,CAAC,IAAA;AAMD;;;;;;;IAOE,eAAqB,QAAkB,EAAW,SAAmB;QAAhD,aAAQ,GAAR,QAAQ,CAAU;QAAW,cAAS,GAAT,SAAS,CAAU;KAAI;IAEzE,kBAAE,GAAF,UACE,SAAiB,EACjB,QAA0B,EAC1B,uBAAiE,EACjE,OAAuB;QAJzB,iBAgDC;;QA1CCA,qBAAgB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrDC,qBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAE1D,IAAM,GAAG,GAAG,KAAK,CAAC,wBAAwB,CACxC,UAAU,EACV,uBAAuB,EACvB,OAAO,CACR,CAAC;QACF,IAAM,aAAa,GAAiB,UAAC,WAAW,EAAE,iBAAkB;YAClE,QAAQ,CAAC,IAAI,CACX,GAAG,CAAC,OAAO,EACX,IAAI,YAAY,CAAC,KAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,EAC5C,iBAAiB,CAClB,CAAC;SACH,CAAC;QACF,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;QACtC,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QACpC,IAAM,cAAc,GAAG,MAAA,GAAG,CAAC,MAAM,0CAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAErD,QAAQ,SAAS;YACf,KAAK,OAAO;gBACVC,gBAAO,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;gBACvD,OAAO,QAAQ,CAAC;YAClB,KAAK,aAAa;gBAChBC,qBAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;gBAC5D,OAAO,QAAQ,CAAC;YAClB,KAAK,eAAe;gBAClBC,uBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;gBAC9D,OAAO,QAAQ,CAAC;YAClB,KAAK,eAAe;gBAClBC,uBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;gBAC9D,OAAO,QAAQ,CAAC;YAClB,KAAK,aAAa;gBAChBC,qBAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;gBAC5D,OAAO,QAAQ,CAAC;YAClB;gBACE,MAAM,IAAI,KAAK,CACbC,gBAAW,CAAC,UAAU,EAAE,WAAW,CAAC;oBAClC,wEAAwE;oBACxE,oCAAoC,CACvC,CAAC;SACL;KACF;IAED,mBAAG,GAAH,UACE,SAAkB,EAClB,QAA2B,EAC3B,OAAuB;QAEvBP,qBAAgB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACtD,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAChDC,qBAAgB,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1DO,0BAAqB,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC7D,IAAI,QAAQ,EAAE;YACZ,IAAM,aAAa,GAAiB,eAAQ,CAAC;YAC7C,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;YACtC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;YAChCC,YAAG,CAAC,IAAI,CAAC,SAAS,EAAE,SAAsB,EAAE,aAAa,CAAC,CAAC;SAC5D;aAAM;YACLA,YAAG,CAAC,IAAI,CAAC,SAAS,EAAE,SAAkC,CAAC,CAAC;SACzD;KACF;;;;IAKD,mBAAG,GAAH;QAAA,iBAIC;QAHC,OAAOC,YAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAA,WAAW;YACzC,OAAO,IAAI,YAAY,CAAC,KAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;SACrD,CAAC,CAAC;KACJ;;;;IAKD,oBAAI,GAAJ,UACE,SAAiB,EACjB,QAA2B,EAC3B,wBAA+D,EAC/D,OAAuB;QAJzB,iBAkEC;QA5DCV,qBAAgB,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACvDC,qBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAE3D,IAAM,GAAG,GAAG,KAAK,CAAC,wBAAwB,CACxC,YAAY,EACZ,wBAAwB,EACxB,OAAO,CACR,CAAC;QACF,IAAM,QAAQ,GAAG,IAAIU,aAAQ,EAAgB,CAAC;QAC9C,IAAM,aAAa,GAAiB,UAAC,WAAW,EAAE,iBAAkB;YAClE,IAAM,MAAM,GAAG,IAAI,YAAY,CAAC,KAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;aACvD;YACD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC1B,CAAC;QACF,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;QACtC,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QACpC,IAAM,cAAc,GAAG,UAAC,KAAY;YAClC,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACrC;YACD,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACxB,CAAC;QAEF,QAAQ,SAAS;YACf,KAAK,OAAO;gBACVT,gBAAO,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;oBACrD,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,aAAa;gBAChBC,qBAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;oBAC1D,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,eAAe;gBAClBC,uBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;oBAC5D,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,eAAe;gBAClBC,uBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;oBAC5D,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,aAAa;gBAChBC,qBAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;oBAC1D,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CACbC,gBAAW,CAAC,YAAY,EAAE,WAAW,CAAC;oBACpC,wEAAwE;oBACxE,oCAAoC,CACvC,CAAC;SACL;QAED,OAAO,QAAQ,CAAC,OAAO,CAAC;KACzB;;;;IAKD,4BAAY,GAAZ,UAAa,KAAa;QACxBP,qBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEC,qBAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC7E;;;;IAKD,2BAAW,GAAX,UAAY,KAAa;QACvBb,qBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9D,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEE,oBAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5E;;;;IAKD,4BAAY,GAAZ,UAAa,IAAY;QACvBd,qBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEG,qBAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAC5E;;;;IAKD,0BAAU,GAAV;QACEf,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEI,mBAAU,EAAE,CAAC,CAAC,CAAC;KACtE;;;;IAKD,+BAAe,GAAf;QACEhB,qBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClE,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEK,wBAAe,EAAE,CAAC,CAAC,CAAC;KAC3E;;;;IAKD,4BAAY,GAAZ;QACEjB,qBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEM,qBAAY,EAAE,CAAC,CAAC,CAAC;KACxE;IAED,uBAAO,GAAP,UACE,KAA8C,EAC9C,IAAoB;QADpB,sBAAA,EAAA,YAA8C;QAG9ClB,qBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACbY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEO,gBAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC5C,CAAC;KACH;IAED,0BAAU,GAAV,UACE,KAA8C,EAC9C,IAAoB;QADpB,sBAAA,EAAA,YAA8C;QAG9CnB,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACbY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEQ,mBAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC/C,CAAC;KACH;IAED,qBAAK,GAAL,UACE,KAA8C,EAC9C,IAAoB;QADpB,sBAAA,EAAA,YAA8C;QAG9CpB,qBAAgB,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACxD,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAEY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAES,cAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;KAC5E;IAED,yBAAS,GAAT,UACE,KAA8C,EAC9C,IAAoB;QADpB,sBAAA,EAAA,YAA8C;QAG9CrB,qBAAgB,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACbY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEU,kBAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC9C,CAAC;KACH;;;;;IAMD,uBAAO,GAAP,UAAQ,KAAuC,EAAE,IAAa;QAC5DtB,qBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACbY,cAAK,CAAC,IAAI,CAAC,SAAS,EAAEW,gBAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC5C,CAAC;KACH;;;;IAKD,wBAAQ,GAAR;QACEvB,qBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAClC;;;IAID,sBAAM,GAAN;;QAEEA,qBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;;;;IAKD,uBAAO,GAAP,UAAQ,KAAY;QAClBA,qBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE;YAC7B,IAAM,KAAK,GACT,sFAAsF,CAAC;YACzF,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;SACxB;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KAChD;;;;;;IAOc,8BAAwB,GAAvC,UACE,MAAc,EACd,eAAsD,EACtD,OAAuB;QAEvB,IAAM,GAAG,GAGL,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAC9C,IAAI,eAAe,IAAI,OAAO,EAAE;YAC9B,GAAG,CAAC,MAAM,GAAG,eAAqC,CAAC;YACnDC,qBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAErD,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;YACtBO,0BAAqB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC7D;aAAM,IAAI,eAAe,EAAE;;YAE1B,IAAI,OAAO,eAAe,KAAK,QAAQ,IAAI,eAAe,KAAK,IAAI,EAAE;;gBAEnE,GAAG,CAAC,OAAO,GAAG,eAAe,CAAC;aAC/B;iBAAM,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;gBAChD,GAAG,CAAC,MAAM,GAAG,eAAqC,CAAC;aACpD;iBAAM;gBACL,MAAM,IAAI,KAAK,CACbD,gBAAW,CAAC,MAAM,EAAE,iBAAiB,CAAC;oBACpC,wDAAwD,CAC3D,CAAC;aACH;SACF;QACD,OAAO,GAAG,CAAC;KACZ;IAED,sBAAI,sBAAG;aAAP;YACE,OAAO,IAAI,SAAS,CAClB,IAAI,CAAC,QAAQ,EACb,IAAIiB,uBAAc,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAC/D,CAAC;SACH;;;OAAA;IACH,YAAC;AAAD,CAAC,IAAA;;IAE8BzB,mCAAK;;;;;;;;IAWlC,mBAAqB0B,UAAkB,EAAW,SAAuB;QAAzE,YACE,kBACEA,UAAQ,EACR,IAAIC,mBAAU,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC,CAC3E,SACF;QALoB,cAAQ,GAARD,UAAQ,CAAU;QAAW,eAAS,GAAT,SAAS,CAAc;;KAKxE;;IAGD,0BAAM,GAAN;QACEzB,qBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;KAC3B;IAED,yBAAK,GAAL,UAAM,UAAkB;QACtBA,qBAAgB,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YAClC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;SACjC;QACD,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE2B,cAAK,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;KACxE;;IAGD,6BAAS,GAAT;QACE3B,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACrC,OAAO,MAAM,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;KAC7D;;IAGD,2BAAO,GAAP;QACEA,qBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3D,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC1D;IAED,uBAAG,GAAH,UACE,MAAe,EACf,UAA0C;QAE1CA,qBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1DC,qBAAgB,CAAC,eAAe,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAClE,IAAM,MAAM,GAAG2B,YAAG,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,0BAAM,GAAN,UACE,MAAc,EACd,UAAsC;QAEtC5B,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAE7D,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,IAAM,gBAAgB,GAA6B,EAAE,CAAC;YACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACtC,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;aACtC;YACD,MAAM,GAAG,gBAAgB,CAAC;YAC1B,IAAI,CACF,uDAAuD;gBACrD,2DAA2D;gBAC3D,uDAAuD;gBACvD,mCAAmC,CACtC,CAAC;SACH;QACD,oBAAoB,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/DC,qBAAgB,CAAC,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAErE,IAAM,MAAM,GAAG4B,eAAM,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC9C,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,mCAAe,GAAf,UACE,MAAe,EACf,WAAmC,EACnC,UAAsC;QAEtC7B,qBAAgB,CAAC,2BAA2B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACtEC,qBAAgB,CACd,2BAA2B,EAC3B,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;QAEF,IAAM,MAAM,GAAG6B,wBAAe,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QACpE,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,0BAAM,GAAN,UAAO,UAAsC;QAC3C9B,qBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7DC,qBAAgB,CAAC,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAErE,IAAM,MAAM,GAAG8B,eAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,+BAAW,GAAX,UACE,iBAAoD,EACpD,UAIS,EACT,YAAsB;QAPxB,iBA6CC;QApCC/B,qBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClEC,qBAAgB,CACd,uBAAuB,EACvB,mBAAmB,EACnB,iBAAiB,EACjB,KAAK,CACN,CAAC;QACFA,qBAAgB,CAAC,uBAAuB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1E,eAAe,CACb,uBAAuB,EACvB,cAAc,EACd,YAAY,EACZ,IAAI,CACL,CAAC;QAEF,IAAM,MAAM,GAAG+B,uBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,iBAAiB,EAAE;YAC/D,YAAY,cAAA;SACb,CAAC,CAAC,IAAI,CACL,UAAA,iBAAiB;YACf,OAAA,IAAI,iBAAiB,CACnB,iBAAiB,CAAC,SAAS,EAC3B,IAAI,YAAY,CAAC,KAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAC5D;SAAA,CACJ,CAAC;QACF,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,UAAA,iBAAiB;gBACf,OAAA,UAAU,CACR,IAAI,EACJ,iBAAiB,CAAC,SAAS,EAC3B,iBAAiB,CAAC,QAAQ,CAC3B;aAAA,EACH,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,GAAA,CACxC,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,+BAAW,GAAX,UACE,QAAgC,EAChC,UAAsC;QAEtChC,qBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClEC,qBAAgB,CAAC,uBAAuB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAE1E,IAAM,MAAM,GAAGgC,oBAAW,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QACD,OAAO,MAAM,CAAC;KACf;IAED,wBAAI,GAAJ,UAAK,KAAe,EAAE,UAAsC;QAA5D,iBAoBC;QAnBCjC,qBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3DC,qBAAgB,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEnE,IAAM,UAAU,GAAGiC,aAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAA0B,CAAC;QACxE,IAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAC7B,UAAA,MAAM,IAAI,OAAA,IAAI,SAAS,CAAC,KAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAA,CAC/C,CAAC;QAEF,IAAI,UAAU,EAAE;YACd,OAAO,CAAC,IAAI,CACV,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,GAAA,EACtB,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAA,CAC3B,CAAC;SACH;QAED,IAAM,MAAM,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC;KACf;IAED,gCAAY,GAAZ;QACE,oBAAoB,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACrE,OAAO,IAAI,YAAY,CACrB,IAAIC,qBAAe,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAChE,CAAC;KACH;IAED,sBAAI,0BAAG;aAAP;YACE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;SACtB;;;OAAA;IAED,sBAAI,6BAAM;aAAV;YACE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;SACzB;;;OAAA;IAED,sBAAI,2BAAI;aAAR;YACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;SACvB;;;OAAA;IACH,gBAAC;AAAD,CA1OA,CAA+B,KAAK;;ACjjBpC;;;;;;;;;;;;;;;;AA8CA;;;;;;;IAYE,kBAAqB,SAAsB,EAAW,GAAgB;QAAtE,iBAA0E;QAArD,cAAS,GAAT,SAAS,CAAa;QAAW,QAAG,GAAH,GAAG,CAAa;QAEtE,aAAQ,GAAG;YACT,MAAM,EAAE,cAAM,OAAA,KAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAA;SACvC,CAAC;KAJwE;;;;;;;;;;IAe1E,8BAAW,GAAX,UACE,IAAY,EACZ,IAAY,EACZ,OAEM;QAFN,wBAAA,EAAA,YAEM;QAENC,gCAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KAC9D;IAcD,sBAAG,GAAH,UAAI,IAAyB;QAC3BpC,qBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,IAAI,YAAY,SAAS,EAAE;YAC7B,IAAM,QAAQ,GAAGqC,mBAAU,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACtC;aAAM;YACL,IAAM,QAAQ,GAAGC,YAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC3C,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACtC;KACF;;;;;;;IAQD,6BAAU,GAAV,UAAW,GAAW;QACpB,IAAM,OAAO,GAAG,qBAAqB,CAAC;QACtCtC,qBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClD,IAAM,QAAQ,GAAGqC,mBAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACjD,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACtC;;IAGD,4BAAS,GAAT;QACErC,qBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAOuC,kBAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAClC;IAED,2BAAQ,GAAR;QACEvC,qBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9D,OAAOwC,iBAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACjC;IA9Ee,oBAAW,GAAG;QAC5B,SAAS,EAAEC,wBAAe,EAAE;QAC5B,SAAS,EAAE,UAAC,KAAa,IAAK,OAAAC,kBAAS,CAAC,KAAK,CAAC,GAAA;KAC/C,CAAC;IA4EJ,eAAC;CAhFD;;ACjDA;;;;;;;;;;;;;;;;AAiBO,IAAM,gBAAgB,GAAG,GAAG,CAAC;AAE7B,IAAM,aAAa,GAAG,GAAG,CAAC;AAE1B,IAAM,uBAAuB,GAAG,GAAG,CAAC;AAEpC,IAAM,aAAa,GAAG,GAAG,CAAC;AAE1B,IAAM,SAAS,GAAG,GAAG,CAAC;AAE7B;AACA;AACO,IAAM,eAAe,GAAG,4EAA4E,CAAC;AAErG,IAAM,kBAAkB,GAAG,IAAI,CAAC;AAEhC,IAAM,oBAAoB,GAAG,GAAG,CAAC;AAEjC,IAAM,qBAAqB,GAAG,IAAI,CAAC;AAEnC,IAAM,SAAS,GAAG,WAAW,CAAC;AAE9B,IAAM,YAAY,GAAG,cAAc;;ACvC1C;;;;;;;;;;;;;;;;AAwBA;;;AAGA;;;;;;;;;IAaE,kBACE,IAAY,EACI,MAAe,EACf,SAAiB,EACjB,aAAsB,EACtB,SAA0B,EAC1B,cAA2B,EAC3B,6BAA8C;QAF9C,0BAAA,EAAA,iBAA0B;QAC1B,+BAAA,EAAA,mBAA2B;QAC3B,8CAAA,EAAA,qCAA8C;QAL9C,WAAM,GAAN,MAAM,CAAS;QACf,cAAS,GAAT,SAAS,CAAQ;QACjB,kBAAa,GAAb,aAAa,CAAS;QACtB,cAAS,GAAT,SAAS,CAAiB;QAC1B,mBAAc,GAAd,cAAc,CAAa;QAC3B,kCAA6B,GAA7B,6BAA6B,CAAiB;QAE9D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY;YACd,iBAAiB,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAY,IAAI,IAAI,CAAC,KAAK,CAAC;KACnE;IAED,kCAAe,GAAf;QACE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;KAChD;IAED,+BAAY,GAAZ;QACE,QACE,IAAI,CAAC,OAAO,KAAK,gBAAgB;YACjC,IAAI,CAAC,OAAO,KAAK,qBAAqB,EACtC;KACH;IAED,sBAAI,0BAAI;aAAR;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;aAED,UAAS,OAAe;YACtB,IAAI,OAAO,KAAK,IAAI,CAAC,YAAY,EAAE;gBACjC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;gBAC5B,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;oBAC1B,iBAAiB,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAChE;aACF;SACF;;;OATA;IAWD,2BAAQ,GAAR;QACE,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;SACxC;QACD,OAAO,GAAG,CAAC;KACZ;IAED,8BAAW,GAAX;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,SAAS,CAAC;QACtD,IAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B;cAC5C,SAAO,IAAI,CAAC,SAAW;cACvB,EAAE,CAAC;QACP,OAAO,KAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,SAAI,KAAO,CAAC;KAC3C;IACH,eAAC;AAAD,CAAC,IAAA;AAED,SAAS,uBAAuB,CAAC,QAAkB;IACjD,QACE,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,YAAY;QACvC,QAAQ,CAAC,YAAY,EAAE;QACvB,QAAQ,CAAC,6BAA6B,EACtC;AACJ,CAAC;AAED;;;;;;;SAOgB,qBAAqB,CACnC,QAAkB,EAClB,IAAY,EACZ,MAA+B;IAE/BhD,WAAM,CAAC,OAAO,IAAI,KAAK,QAAQ,EAAE,4BAA4B,CAAC,CAAC;IAC/DA,WAAM,CAAC,OAAO,MAAM,KAAK,QAAQ,EAAE,8BAA8B,CAAC,CAAC;IAEnE,IAAI,OAAe,CAAC;IACpB,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO;YACL,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO,CAAC;KAC5E;SAAM,IAAI,IAAI,KAAK,YAAY,EAAE;QAChC,OAAO;YACL,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAU,GAAG,SAAS;gBACzC,QAAQ,CAAC,YAAY;gBACrB,OAAO,CAAC;KACX;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC,CAAC;KACrD;IACD,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;KACnC;IAED,IAAM,KAAK,GAAa,EAAE,CAAC;IAE3B,IAAI,CAAC,MAAM,EAAE,UAAC,GAAW,EAAE,KAAa;QACtC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;KAC/B,CAAC,CAAC;IAEH,OAAO,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC;;AC9IA;;;;;;;;;;;;;;;;AAmBA;;;AAGA;IAAA;QACU,cAAS,GAA4B,EAAE,CAAC;KAajD;IAXC,0CAAgB,GAAhB,UAAiB,IAAY,EAAE,MAAkB;QAAlB,uBAAA,EAAA,UAAkB;QAC/C,IAAI,CAACL,aAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;YACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC;KAChC;IAED,6BAAG,GAAH;QACE,OAAOsD,aAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACjC;IACH,sBAAC;AAAD,CAAC;;ACpCD;;;;;;;;;;;;;;;;AAqBA,IAAM,WAAW,GAAqC,EAAE,CAAC;SAGzC,yBAAyB,CAAC,QAAkB;IAC1D,IAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAEvC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;QAC5B,WAAW,CAAC,UAAU,CAAC,GAAG,IAAI,eAAe,EAAE,CAAC;KACjD;IAED,OAAO,WAAW,CAAC,UAAU,CAAC,CAAC;AACjC;;AChCA;;;;;;;;;;;;;;;;AAmBA;;;;AAIA;;;;IASE,wBAAoB,UAA2B;QAA3B,eAAU,GAAV,UAAU,CAAiB;QAR/C,qBAAgB,GAAc,EAAE,CAAC;QACjC,uBAAkB,GAAG,CAAC,CAAC;QACvB,uBAAkB,GAAG,CAAC,CAAC,CAAC;QACxB,YAAO,GAAwB,IAAI,CAAC;KAKe;IAEnD,mCAAU,GAAV,UAAW,WAAmB,EAAE,QAAoB;QAClD,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;QACxB,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE;YACrD,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;KACF;;;;;;IAOD,uCAAc,GAAd,UAAe,UAAkB,EAAE,IAAe;QAAlD,iBAuBC;QAtBC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;;YAEvC,IAAM,SAAS,GAAG,OAAK,gBAAgB,CACrC,OAAK,kBAAkB,CACX,CAAC;YACf,OAAO,OAAK,gBAAgB,CAAC,OAAK,kBAAkB,CAAC,CAAC;oCAC7C,CAAC;gBACR,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;oBAChB,cAAc,CAAC;wBACb,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC/B,CAAC,CAAC;iBACJ;;YALH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;wBAAhC,CAAC;aAMT;YACD,IAAI,OAAK,kBAAkB,KAAK,OAAK,kBAAkB,EAAE;gBACvD,IAAI,OAAK,OAAO,EAAE;oBAChB,OAAK,OAAO,EAAE,CAAC;oBACf,OAAK,OAAO,GAAG,IAAI,CAAC;iBACrB;;aAEF;YACD,OAAK,kBAAkB,EAAE,CAAC;;;QAnB5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC;;;;SAoBpD;KACF;IACH,qBAAC;AAAD,CAAC;;ACxED;;;;;;;;;;;;;;;;AA+CA;AACO,IAAM,6BAA6B,GAAG,OAAO,CAAC;AAC9C,IAAM,+BAA+B,GAAG,OAAO,CAAC;AAChD,IAAM,iCAAiC,GAAG,YAAY,CAAC;AACvD,IAAM,8BAA8B,GAAG,SAAS,CAAC;AACjD,IAAM,0BAA0B,GAAG,IAAI,CAAC;AACxC,IAAM,0BAA0B,GAAG,IAAI,CAAC;AACxC,IAAM,8BAA8B,GAAG,KAAK,CAAC;AAC7C,IAAM,mCAAmC,GAAG,IAAI,CAAC;AACjD,IAAM,mCAAmC,GAAG,KAAK,CAAC;AAClD,IAAM,oCAAoC,GAAG,IAAI,CAAC;AAClD,IAAM,4BAA4B,GAAG,GAAG,CAAC;AAEzC,IAAM,6CAA6C,GAAG,QAAQ,CAAC;AAEtE;AACA;AACA;AACA,IAAM,iBAAiB,GAAG,IAAI,CAAC;AAC/B,IAAM,eAAe,GAAG,EAAE,CAAC;AAC3B,IAAM,gBAAgB,GAAG,iBAAiB,GAAG,eAAe,CAAC;AAE7D;;;;;AAKA,IAAM,0BAA0B,GAAG,KAAK,CAAC;AAEzC;;;AAGA,IAAM,kBAAkB,GAAG,KAAK,CAAC;AAEjC;;;AAGA;;;;;;;;;;;;IA4BE,+BACS,MAAc,EACd,QAAkB,EACjB,aAAsB,EACtB,aAAsB,EACtB,SAAkB,EACnB,kBAA2B,EAC3B,aAAsB;QAP/B,iBAkBC;QAjBQ,WAAM,GAAN,MAAM,CAAQ;QACd,aAAQ,GAAR,QAAQ,CAAU;QACjB,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;QACtB,cAAS,GAAT,SAAS,CAAS;QACnB,uBAAkB,GAAlB,kBAAkB,CAAS;QAC3B,kBAAa,GAAb,aAAa,CAAS;QAlC/B,cAAS,GAAG,CAAC,CAAC;QACd,kBAAa,GAAG,CAAC,CAAC;QAUV,mBAAc,GAAG,KAAK,CAAC;QAyB7B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,GAAG,UAAC,MAA+B;;YAE3C,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,MAAM,CAAC,qBAAqB,CAAC,GAAG,KAAI,CAAC,aAAa,CAAC;aACpD;YACD,OAAO,qBAAqB,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;SAC9D,CAAC;KACH;;;;;IAMD,oCAAI,GAAJ,UAAK,SAA4B,EAAE,YAAmC;QAAtE,iBAyGC;QAxGC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC;YACrC,KAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;;YAE1C,KAAI,CAAC,SAAS,EAAE,CAAC;YACjB,KAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;;SAElC,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAQ,CAAC;;QAG1C,mBAAmB,CAAC;YAClB,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,OAAO;aACR;;YAGD,KAAI,CAAC,eAAe,GAAG,IAAI,0BAA0B,CACnD;gBAAC,cAAO;qBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;oBAAP,yBAAO;;oBACA,KAAAC,aAAoC,IAAI,IAAA,EAAvC,OAAO,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAAA,OAAM,QAAe;gBAC/C,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,KAAI,CAAC,eAAe,EAAE;oBACzB,OAAO;iBACR;gBAED,IAAI,KAAI,CAAC,oBAAoB,EAAE;oBAC7B,YAAY,CAAC,KAAI,CAAC,oBAAoB,CAAC,CAAC;oBACxC,KAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;iBAClC;gBACD,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,OAAO,KAAK,6BAA6B,EAAE;oBAC7C,KAAI,CAAC,EAAE,GAAG,IAAc,CAAC;oBACzB,KAAI,CAAC,QAAQ,GAAG,IAAc,CAAC;iBAChC;qBAAM,IAAI,OAAO,KAAK,+BAA+B,EAAE;;oBAEtD,IAAI,IAAI,EAAE;;;wBAGR,KAAI,CAAC,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;;;wBAI1C,KAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAc,EAAE;4BAC9C,KAAI,CAAC,SAAS,EAAE,CAAC;yBAClB,CAAC,CAAC;qBACJ;yBAAM;wBACL,KAAI,CAAC,SAAS,EAAE,CAAC;qBAClB;iBACF;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,OAAO,CAAC,CAAC;iBAC9D;aACF,EACD;gBAAC,cAAO;qBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;oBAAP,yBAAO;;gBACA,IAAA,KAAAA,aAAa,IAAI,IAAA,EAAhB,EAAE,QAAA,EAAE,IAAI,QAAQ,CAAC;gBACxB,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBACnC,KAAI,CAAC,eAAe,CAAC,cAAc,CAAC,EAAY,EAAE,IAAiB,CAAC,CAAC;aACtE,EACD;gBACE,KAAI,CAAC,SAAS,EAAE,CAAC;aAClB,EACD,KAAI,CAAC,KAAK,CACX,CAAC;;;YAIF,IAAM,SAAS,GAAqC,EAAE,CAAC;YACvD,SAAS,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC;YAC/C,SAAS,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC,KAAK,CACpD,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAC1B,CAAC;YACF,IAAI,KAAI,CAAC,eAAe,CAAC,wBAAwB,EAAE;gBACjD,SAAS,CACP,mCAAmC,CACpC,GAAG,KAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC;aACnD;YACD,SAAS,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAC5C,IAAI,KAAI,CAAC,kBAAkB,EAAE;gBAC3B,SAAS,CAAC,uBAAuB,CAAC,GAAG,KAAI,CAAC,kBAAkB,CAAC;aAC9D;YACD,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,SAAS,CAAC,kBAAkB,CAAC,GAAG,KAAI,CAAC,aAAa,CAAC;aACpD;YACD,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,SAAS,CAAC,oBAAoB,CAAC,GAAG,KAAI,CAAC,aAAa,CAAC;aACtD;YACD,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,SAAS,CAAC,qBAAqB,CAAC,GAAG,KAAI,CAAC,aAAa,CAAC;aACvD;YACD,IACE,OAAO,QAAQ,KAAK,WAAW;gBAC/B,QAAQ,CAAC,QAAQ;gBACjB,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACvC;gBACA,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;aACtC;YACD,IAAM,UAAU,GAAG,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACzC,KAAI,CAAC,IAAI,CAAC,8BAA8B,GAAG,UAAU,CAAC,CAAC;YACvD,KAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,EAAE;;aAEvC,CAAC,CAAC;SACJ,CAAC,CAAC;KACJ;;;;IAKD,qCAAK,GAAL;QACE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACrD;;;;IAOM,gCAAU,GAAjB;QACE,qBAAqB,CAAC,WAAW,GAAG,IAAI,CAAC;KAC1C;;;;IAOM,mCAAa,GAApB;QACE,qBAAqB,CAAC,cAAc,GAAG,IAAI,CAAC;KAC7C;;IAGM,iCAAW,GAAlB;QACE,IAAIhD,cAAS,EAAE,EAAE;YACf,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,qBAAqB,CAAC,WAAW,EAAE;YAC5C,OAAO,IAAI,CAAC;SACb;aAAM;;;YAGL,QACE,CAAC,qBAAqB,CAAC,cAAc;gBACrC,OAAO,QAAQ,KAAK,WAAW;gBAC/B,QAAQ,CAAC,aAAa,IAAI,IAAI;gBAC9B,CAAC,8BAA8B,EAAE;gBACjC,CAAC,iBAAiB,EAAE,EACpB;SACH;KACF;;;;IAKD,qDAAqB,GAArB,eAA0B;;;;IAKlB,yCAAS,GAAjB;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;;QAGD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACxC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;KACF;;;;IAKO,yCAAS,GAAjB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,EAAE,CAAC;YAEjB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC3B;SACF;KACF;;;;;IAMD,qCAAK,GAAL;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACvC,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;KACF;;;;;;IAOD,oCAAI,GAAJ,UAAK,IAAQ;QACX,IAAM,OAAO,GAAGT,cAAS,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;;QAG3D,IAAM,UAAU,GAAG0D,iBAAY,CAAC,OAAO,CAAC,CAAC;;;QAIzC,IAAM,QAAQ,GAAG,iBAAiB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;;;QAIjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,eAAe,CAAC,cAAc,CACjC,IAAI,CAAC,aAAa,EAClB,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,CAAC,CAAC,CACZ,CAAC;YACF,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;;;;;;IAOD,sDAAsB,GAAtB,UAAuB,EAAU,EAAE,EAAU;QAC3C,IAAIjD,cAAS,EAAE,EAAE;YACf,OAAO;SACR;QACD,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAM,SAAS,GAA4B,EAAE,CAAC;QAC9C,SAAS,CAAC,6CAA6C,CAAC,GAAG,GAAG,CAAC;QAC/D,SAAS,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC;QAC3C,SAAS,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC;QAC3C,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QAE3C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KAChD;;;;IAKO,uDAAuB,GAA/B,UAAgC,IAAa;;QAE3C,IAAM,aAAa,GAAGT,cAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAC7C,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;KAC/D;IACH,4BAAC;AAAD,CAAC,IAAA;AAOD;;;AAGA;;;;;;;IAiCE,oCACE,SAAwD,EACxD,WAAyC,EAClC,YAAwB,EACxB,KAA4B;QAD5B,iBAAY,GAAZ,YAAY,CAAY;QACxB,UAAK,GAAL,KAAK,CAAuB;;;QAlCrC,wBAAmB,GAAG,IAAI,GAAG,EAAU,CAAC;;QAGxC,gBAAW,GAAmD,EAAE,CAAC;;;;;;QAOjE,kBAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC;;;QAItD,iBAAY,GAAG,IAAI,CAAC;QAsBlB,IAAI,CAACS,cAAS,EAAE,EAAE;;;;;YAKhB,IAAI,CAAC,wBAAwB,GAAG,aAAa,EAAE,CAAC;YAChD,MAAM,CACJ,iCAAiC,GAAG,IAAI,CAAC,wBAAwB,CAClE,GAAG,SAAS,CAAC;YACd,MAAM,CACJ,8BAA8B,GAAG,IAAI,CAAC,wBAAwB,CAC/D,GAAG,WAAW,CAAC;;YAGhB,IAAI,CAAC,QAAQ,GAAG,0BAA0B,CAAC,aAAa,EAAE,CAAC;;YAG3D,IAAI,MAAM,GAAG,EAAE,CAAC;;;YAGhB,IACE,IAAI,CAAC,QAAQ,CAAC,GAAG;gBACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,aAAa,EACnE;gBACA,IAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;gBACtC,MAAM,GAAG,2BAA2B,GAAG,aAAa,GAAG,aAAa,CAAC;aACtE;YACD,IAAM,cAAc,GAAG,cAAc,GAAG,MAAM,GAAG,gBAAgB,CAAC;YAClE,IAAI;gBACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;aAC3B;YAAC,OAAO,CAAC,EAAE;gBACV,GAAG,CAAC,yBAAyB,CAAC,CAAC;gBAC/B,IAAI,CAAC,CAAC,KAAK,EAAE;oBACX,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;iBACd;gBACD,GAAG,CAAC,CAAC,CAAC,CAAC;aACR;SACF;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;SAChC;KACF;;;;;IAMc,wCAAa,GAA5B;QACE,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAkB,CAAC;QACjE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;;QAG9B,IAAI,QAAQ,CAAC,IAAI,EAAE;YACjB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI;;;;gBAIF,IAAM,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACxC,IAAI,CAAC,CAAC,EAAE;;oBAEN,GAAG,CAAC,+BAA+B,CAAC,CAAC;iBACtC;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;gBAC/B,MAAM,CAAC,GAAG;oBACR,+DAA+D;wBAC/D,MAAM;wBACN,0BAA0B,CAAC;aAC9B;SACF;aAAM;;;YAGL,MAAM,mGAAmG,CAAC;SAC3G;;QAGD,IAAI,MAAM,CAAC,eAAe,EAAE;YAC1B,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC;SACrC;aAAM,IAAI,MAAM,CAAC,aAAa,EAAE;YAC/B,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;;SAE5C;aAAM,IAAK,MAAc,CAAC,QAAQ,EAAE;;YAEnC,MAAM,CAAC,GAAG,GAAI,MAAc,CAAC,QAAQ,CAAC;SACvC;QAED,OAAO,MAAM,CAAC;KACf;;;;IAKD,0CAAK,GAAL;QAAA,iBAuBC;;QArBC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,IAAI,CAAC,QAAQ,EAAE;;;;YAIjB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACtC,UAAU,CAAC;gBACT,IAAI,KAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;oBAC1B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;oBACzC,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACtB;aACF,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACnB;;QAGD,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,YAAY,EAAE,CAAC;SAChB;KACF;;;;;;IAOD,kDAAa,GAAb,UAAc,EAAU,EAAE,EAAU;QAClC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;QAGlB,OAAO,IAAI,CAAC,WAAW,EAAE,EAAE,GAAE;KAC9B;;;;;;;;IASO,gDAAW,GAAnB;;;;QAIE,IACE,IAAI,CAAC,KAAK;YACV,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EACrE;;YAEA,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAM,SAAS,GAAqC,EAAE,CAAC;YACvD,SAAS,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;YAClD,SAAS,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;YAClD,SAAS,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;YAC/D,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;YAEnC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,CAAC;YAEV,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;;gBAElC,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACpC,IACG,OAAO,CAAC,CAAe,CAAC,MAAM;oBAC7B,eAAe;oBACf,aAAa,CAAC,MAAM;oBACtB,iBAAiB,EACjB;;oBAEA,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBACxC,aAAa;wBACX,aAAa;4BACb,GAAG;4BACH,mCAAmC;4BACnC,CAAC;4BACD,GAAG;4BACH,MAAM,CAAC,GAAG;4BACV,GAAG;4BACH,oCAAoC;4BACpC,CAAC;4BACD,GAAG;4BACH,MAAM,CAAC,EAAE;4BACT,GAAG;4BACH,4BAA4B;4BAC5B,CAAC;4BACD,GAAG;4BACH,MAAM,CAAC,CAAC,CAAC;oBACX,CAAC,EAAE,CAAC;iBACL;qBAAM;oBACL,MAAM;iBACP;aACF;YAED,MAAM,GAAG,MAAM,GAAG,aAAa,CAAC;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAEjD,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,KAAK,CAAC;SACd;KACF;;;;;;;IAQD,mDAAc,GAAd,UAAe,MAAc,EAAE,SAAiB,EAAE,IAAa;;QAE7D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;;;QAI/D,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;KACF;;;;;;IAOO,oDAAe,GAAvB,UAAwB,GAAW,EAAE,MAAc;QAAnD,iBAyBC;;QAvBC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAErC,IAAM,YAAY,GAAG;YACnB,KAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxC,KAAI,CAAC,WAAW,EAAE,CAAC;SACpB,CAAC;;;QAIF,IAAM,gBAAgB,GAAG,UAAU,CACjC,YAAY,EACZ,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CACvC,CAAC;QAEF,IAAM,YAAY,GAAG;;YAEnB,YAAY,CAAC,gBAAgB,CAAC,CAAC;;YAG/B,YAAY,EAAE,CAAC;SAChB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KAChC;;;;;;IAOD,2CAAM,GAAN,UAAO,GAAW,EAAE,MAAkB;QAAtC,iBAuCC;QAtCC,IAAIA,cAAS,EAAE,EAAE;;YAEd,IAAY,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;SAC3C;aAAM;YACL,UAAU,CAAC;gBACT,IAAI;;oBAEF,IAAI,CAAC,KAAI,CAAC,YAAY,EAAE;wBACtB,OAAO;qBACR;oBACD,IAAM,WAAS,GAAG,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC5D,WAAS,CAAC,IAAI,GAAG,iBAAiB,CAAC;oBACnC,WAAS,CAAC,KAAK,GAAG,IAAI,CAAC;oBACvB,WAAS,CAAC,GAAG,GAAG,GAAG,CAAC;;oBAEpB,WAAS,CAAC,MAAM,GAAI,WAAiB,CAAC,kBAAkB,GAAG;;wBAEzD,IAAM,MAAM,GAAI,WAAiB,CAAC,UAAU,CAAC;wBAC7C,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,UAAU,EAAE;;4BAE3D,WAAS,CAAC,MAAM,GAAI,WAAiB,CAAC,kBAAkB,GAAG,IAAI,CAAC;4BAChE,IAAI,WAAS,CAAC,UAAU,EAAE;gCACxB,WAAS,CAAC,UAAU,CAAC,WAAW,CAAC,WAAS,CAAC,CAAC;6BAC7C;4BACD,MAAM,EAAE,CAAC;yBACV;qBACF,CAAC;oBACF,WAAS,CAAC,OAAO,GAAG;wBAClB,GAAG,CAAC,mCAAmC,GAAG,GAAG,CAAC,CAAC;wBAC/C,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;wBAC1B,KAAI,CAAC,KAAK,EAAE,CAAC;qBACd,CAAC;oBACF,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,WAAS,CAAC,CAAC;iBAC/C;gBAAC,OAAO,CAAC,EAAE;;iBAEX;aACF,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACnB;KACF;IACH,iCAAC;AAAD,CAAC;;AC3uBD;;;;;;;;;;;;;;;;AAiBA;AACO,IAAI,WAAW,GAAG,EAAE,CAAC;AAE5B;SACgB,aAAa,CAAC,OAAe;IAC3C,WAAW,GAAG,OAAO,CAAC;AACxB;;ACvBA;;;;;;;;;;;;;;;;AA0CA,IAAM,wBAAwB,GAAG,KAAK,CAAC;AACvC,IAAM,4BAA4B,GAAG,KAAK,CAAC;AAE3C,IAAI,aAAa,GAAG,IAAI,CAAC;AACzB,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;IACvC,aAAa,GAAG,YAAY,CAAC;CAC9B;KAAM,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;IAC3C,aAAa,GAAG,SAAS,CAAC;CAC3B;SAEe,gBAAgB,CAAC,IAAI;IACnC,aAAa,GAAG,IAAI,CAAC;AACvB,CAAC;AAED;;;AAGA;;;;;;;;;;;;IA2BE,6BACS,MAAc,EACrB,QAAkB,EACV,aAAsB,EACtB,aAAsB,EACtB,SAAkB,EAC1B,kBAA2B,EAC3B,aAAsB;QANf,WAAM,GAAN,MAAM,CAAQ;QAEb,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;QACtB,cAAS,GAAT,SAAS,CAAS;QA/B5B,mBAAc,GAAkB,IAAI,CAAC;QACrC,WAAM,GAAoB,IAAI,CAAC;QAC/B,gBAAW,GAAG,CAAC,CAAC;QAChB,cAAS,GAAG,CAAC,CAAC;QACd,kBAAa,GAAG,CAAC,CAAC;QA+BhB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,cAAc,CAC/C,QAAQ,EACR,kBAAkB,EAClB,aAAa,EACb,aAAa,CACd,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;KACrC;;;;;;;;IASc,kCAAc,GAA7B,UACE,QAAkB,EAClB,kBAA2B,EAC3B,aAAsB,EACtB,aAAsB;QAEtB,IAAM,SAAS,GAA4B,EAAE,CAAC;QAC9C,SAAS,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAE5C,IACE,CAACA,cAAS,EAAE;YACZ,OAAO,QAAQ,KAAK,WAAW;YAC/B,QAAQ,CAAC,QAAQ;YACjB,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACvC;YACA,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;SACtC;QACD,IAAI,kBAAkB,EAAE;YACtB,SAAS,CAAC,uBAAuB,CAAC,GAAG,kBAAkB,CAAC;SACzD;QACD,IAAI,aAAa,EAAE;YACjB,SAAS,CAAC,kBAAkB,CAAC,GAAG,aAAa,CAAC;SAC/C;QACD,IAAI,aAAa,EAAE;YACjB,SAAS,CAAC,qBAAqB,CAAC,GAAG,aAAa,CAAC;SAClD;QAED,OAAO,qBAAqB,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;KAC9D;;;;;IAMD,kCAAI,GAAJ,UAAK,SAA4B,EAAE,YAAmC;QAAtE,iBAwFC;QAvFC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAErD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;;QAE5B,iBAAiB,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;QAE1D,IAAI;YACF,IAAIA,cAAS,EAAE,EAAE;gBACf,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,WAAW,GAAG,MAAM,CAAC;;gBAErD,IAAM,OAAO,GAA4B;oBACvC,OAAO,EAAE;wBACP,YAAY,EAAE,cAAY,gBAAgB,SAAI,WAAW,SAAI,OAAO,CAAC,QAAQ,SAAI,MAAQ;wBACzF,kBAAkB,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;qBAC7C;iBACF,CAAC;;;;;;gBAOF,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,YAAU,IAAI,CAAC,SAAW,CAAC;iBAC/D;gBACD,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;iBAC7D;;gBAGD,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC3B,IAAM,KAAK,GACT,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;sBAChC,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC;sBACxC,GAAG,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;gBAE7C,IAAI,KAAK,EAAE;oBACT,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;iBACtC;gBAED,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;aAC5D;iBAAM;gBACL,IAAM,OAAO,GAA4B;oBACvC,OAAO,EAAE;wBACP,kBAAkB,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;wBAC5C,qBAAqB,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;qBAChD;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;aAC5D;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC5C,IAAM,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC;YAClC,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClB;YACD,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;YACnB,KAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClC,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG;YACpB,KAAI,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACpD,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,KAAI,CAAC,SAAS,EAAE,CAAC;SAClB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,UAAA,CAAC;YACvB,KAAI,CAAC,mBAAmB,CAAC,CAAO,CAAC,CAAC;SACnC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,UAAA,CAAC;YACrB,KAAI,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;;YAEnD,IAAM,KAAK,GAAI,CAAS,CAAC,OAAO,IAAK,CAAS,CAAC,IAAI,CAAC;YACpD,IAAI,KAAK,EAAE;gBACT,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClB;YACD,KAAI,CAAC,SAAS,EAAE,CAAC;SAClB,CAAC;KACH;;;;IAKD,mCAAK,GAAL,eAAU;IAIH,iCAAa,GAApB;QACE,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC;KAC3C;IAEM,+BAAW,GAAlB;QACE,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,SAAS,EAAE;YAC3D,IAAM,eAAe,GAAG,gCAAgC,CAAC;YACzD,IAAM,eAAe,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YACnE,IAAI,eAAe,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjD,IAAI,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;oBACxC,YAAY,GAAG,IAAI,CAAC;iBACrB;aACF;SACF;QAED,QACE,CAAC,YAAY;YACb,aAAa,KAAK,IAAI;YACtB,CAAC,mBAAmB,CAAC,cAAc,EACnC;KACH;;;;IAeM,oCAAgB,GAAvB;;;QAGE,QACE,iBAAiB,CAAC,iBAAiB;YACnC,iBAAiB,CAAC,GAAG,CAAC,4BAA4B,CAAC,KAAK,IAAI,EAC5D;KACH;IAED,mDAAqB,GAArB;QACE,iBAAiB,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;KACxD;IAEO,0CAAY,GAApB,UAAqB,IAAY;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE;YAC3C,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAM,QAAQ,GAAGR,aAAQ,CAAC,QAAQ,CAAW,CAAC;;YAG9C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;KACF;;;;IAKO,kDAAoB,GAA5B,UAA6B,UAAkB;QAC7C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;KAClB;;;;;IAMO,gDAAkB,GAA1B,UAA2B,IAAY;QACrCM,WAAM,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,gCAAgC,CAAC,CAAC;;;QAG/D,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;YACpB,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;gBACtB,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC;aACb;SACF;QACD,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;KACb;;;;;IAMD,iDAAmB,GAAnB,UAAoB,IAA8B;QAChD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACxB,OAAO;SACR;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAW,CAAC;QACpC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE5D,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;;YAExB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACzB;aAAM;;YAEL,IAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,aAAa,KAAK,IAAI,EAAE;gBAC1B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;aAClC;SACF;KACF;;;;;IAMD,kCAAI,GAAJ,UAAK,IAAQ;QACX,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAM,OAAO,GAAGP,cAAS,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;;;QAK3D,IAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAAC;;QAGtE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;SAC3C;;QAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/B;KACF;IAEO,uCAAS,GAAjB;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;KACF;IAEO,uCAAS,GAAjB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,EAAE,CAAC;;YAGjB,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC1B;SACF;KACF;;;;;IAMD,mCAAK,GAAL;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACvC,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;KACF;;;;;IAMD,4CAAc,GAAd;QAAA,iBAUC;QATC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;;YAEhC,IAAI,KAAI,CAAC,MAAM,EAAE;gBACf,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aACvB;YACD,KAAI,CAAC,cAAc,EAAE,CAAC;;SAEvB,EAAE,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAQ,CAAC;KACrD;;;;;;IAOO,yCAAW,GAAnB,UAAoB,GAAW;;;;QAI7B,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACvB;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,IAAI,CACP,yCAAyC,EACzC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,EACnB,qBAAqB,CACtB,CAAC;YACF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1C;KACF;;;;IA3LM,gDAA4B,GAAG,CAAC,CAAC;;;;IAKjC,kCAAc,GAAG,KAAK,CAAC;IAuLhC,0BAAC;CA/YD;;AC3DA;;;;;;;;;;;;;;;;AAwBA;;;;;;;AAOA;;;;IAUE,0BAAY,QAAkB;QAC5B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;KAChC;IATD,sBAAW,kCAAc;aAAzB;YACE,OAAO,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;SACrD;;;OAAA;IASO,0CAAe,GAAvB,UAAwB,QAAkB;;QACxC,IAAM,qBAAqB,GACzB,mBAAmB,IAAI,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;QAC9D,IAAI,oBAAoB,GACtB,qBAAqB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;QAEnE,IAAI,QAAQ,CAAC,aAAa,EAAE;YAC1B,IAAI,CAAC,qBAAqB,EAAE;gBAC1B,IAAI,CACF,iFAAiF,CAClF,CAAC;aACH;YAED,oBAAoB,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,oBAAoB,EAAE;YACxB,IAAI,CAAC,WAAW,GAAG,CAAC,mBAAmB,CAAC,CAAC;SAC1C;aAAM;YACL,IAAM,UAAU,IAAI,IAAI,CAAC,WAAW,GAAG,EAA4B,CAAC,CAAC;;gBACrE,KAAwB,IAAA,KAAA2D,eAAA,gBAAgB,CAAC,cAAc,CAAA,gBAAA,4BAAE;oBAApD,IAAM,SAAS,WAAA;oBAClB,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE;wBAC3C,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAC5B;iBACF;;;;;;;;;SACF;KACF;;;;IAKD,2CAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC5B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;KACF;;;;IAKD,2CAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC5B;aAAM;YACL,OAAO,IAAI,CAAC;SACb;KACF;IACH,uBAAC;AAAD,CAAC;;AC9FD;;;;;;;;;;;;;;;;AAgCA;AACA,IAAM,eAAe,GAAG,KAAK,CAAC;AAE9B;AACA;AACA,IAAM,mCAAmC,GAAG,IAAI,CAAC;AAEjD;AACA;AACA;AACA,IAAM,2BAA2B,GAAG,EAAE,GAAG,IAAI,CAAC;AAC9C,IAAM,+BAA+B,GAAG,GAAG,GAAG,IAAI,CAAC;AAQnD,IAAM,YAAY,GAAG,GAAG,CAAC;AACzB,IAAM,YAAY,GAAG,GAAG,CAAC;AACzB,IAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,IAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,IAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,IAAM,YAAY,GAAG,GAAG,CAAC;AACzB,IAAM,UAAU,GAAG,GAAG,CAAC;AACvB,IAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,IAAM,IAAI,GAAG,GAAG,CAAC;AAEjB,IAAM,YAAY,GAAG,GAAG,CAAC;AAEzB;;;;AAIA;;;;;;;;;;;;;IA6BE,oBACS,EAAU,EACT,SAAmB,EACnB,cAAkC,EAClC,cAAkC,EAClC,UAA8B,EAC9B,UAA2B,EAC3B,QAAwC,EACxC,aAAyB,EACzB,OAA4B,EAC7B,aAAsB;QATtB,OAAE,GAAF,EAAE,CAAQ;QACT,cAAS,GAAT,SAAS,CAAU;QACnB,mBAAc,GAAd,cAAc,CAAoB;QAClC,mBAAc,GAAd,cAAc,CAAoB;QAClC,eAAU,GAAV,UAAU,CAAoB;QAC9B,eAAU,GAAV,UAAU,CAAiB;QAC3B,aAAQ,GAAR,QAAQ,CAAgC;QACxC,kBAAa,GAAb,aAAa,CAAY;QACzB,YAAO,GAAP,OAAO,CAAqB;QAC7B,kBAAa,GAAb,aAAa,CAAS;QAtC/B,oBAAe,GAAG,CAAC,CAAC;QACpB,wBAAmB,GAAc,EAAE,CAAC;QAW5B,WAAM,sBAA4B;QA4BxC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,EAAE,CAAC;KACf;;;;IAKO,2BAAM,GAAd;QAAA,iBAqEC;QApEC,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;QACvD,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CACnB,IAAI,CAAC,gBAAgB,EAAE,EACvB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,UAAU,EACf,IAAI,EACJ,IAAI,CAAC,aAAa,CACnB,CAAC;;;QAIF,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;QAE3E,IAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;;;;;;;QAQxB,UAAU,CAAC;;YAET,KAAI,CAAC,KAAK,IAAI,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;SACpE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAElB,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,gBAAgB,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,eAAe,GAAG,qBAAqB,CAAC;gBAC3C,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE;oBACpB,IACE,KAAI,CAAC,KAAK;wBACV,KAAI,CAAC,KAAK,CAAC,aAAa,GAAG,+BAA+B,EAC1D;wBACA,KAAI,CAAC,IAAI,CACP,uDAAuD;4BACrD,KAAI,CAAC,KAAK,CAAC,aAAa;4BACxB,sCAAsC,CACzC,CAAC;wBACF,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;wBACvB,KAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;qBACpC;yBAAM,IACL,KAAI,CAAC,KAAK;wBACV,KAAI,CAAC,KAAK,CAAC,SAAS,GAAG,2BAA2B,EAClD;wBACA,KAAI,CAAC,IAAI,CACP,mDAAmD;4BACjD,KAAI,CAAC,KAAK,CAAC,SAAS;4BACpB,oCAAoC,CACvC,CAAC;;;qBAGH;yBAAM;wBACL,KAAI,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;wBACzD,KAAI,CAAC,KAAK,EAAE,CAAC;qBACd;iBACF;;aAEF,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAQ,CAAC;SACzC;KACF;IAEO,qCAAgB,GAAxB;QACE,OAAO,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;KACtD;IAEO,qCAAgB,GAAxB,UAAyB,IAAI;QAA7B,iBAWC;QAVC,OAAO,UAAA,aAAa;YAClB,IAAI,IAAI,KAAK,KAAI,CAAC,KAAK,EAAE;gBACvB,KAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;aACvC;iBAAM,IAAI,IAAI,KAAK,KAAI,CAAC,cAAc,EAAE;gBACvC,KAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBACxC,KAAI,CAAC,0BAA0B,EAAE,CAAC;aACnC;iBAAM;gBACL,KAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;aACxC;SACF,CAAC;KACH;IAEO,kCAAa,GAArB,UAAsB,IAAe;QAArC,iBAYC;QAXC,OAAO,UAAC,OAAkB;YACxB,IAAI,KAAI,CAAC,MAAM,2BAAiC;gBAC9C,IAAI,IAAI,KAAK,KAAI,CAAC,GAAG,EAAE;oBACrB,KAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;iBACzC;qBAAM,IAAI,IAAI,KAAK,KAAI,CAAC,cAAc,EAAE;oBACvC,KAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;iBAC3C;qBAAM;oBACL,KAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;iBACxC;aACF;SACF,CAAC;KACH;;;;IAKD,gCAAW,GAAX,UAAY,OAAe;;QAEzB,IAAM,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KACrB;IAED,yCAAoB,GAApB;QACE,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,cAAc,EAAE;YACxE,IAAI,CAAC,IAAI,CACP,0CAA0C,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CACxE,CAAC;YACF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;SAE5B;KACF;IAEO,wCAAmB,GAA3B,UAA4B,WAAqC;QAC/D,IAAI,YAAY,IAAI,WAAW,EAAE;YAC/B,IAAM,GAAG,GAAG,WAAW,CAAC,YAAY,CAAW,CAAC;YAChD,IAAI,GAAG,KAAK,UAAU,EAAE;gBACtB,IAAI,CAAC,0BAA0B,EAAE,CAAC;aACnC;iBAAM,IAAI,GAAG,KAAK,aAAa,EAAE;;gBAEhC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;gBAClD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;;gBAE5B,IACE,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,cAAc;oBAChC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,cAAc,EAChC;oBACA,IAAI,CAAC,KAAK,EAAE,CAAC;iBACd;aACF;iBAAM,IAAI,GAAG,KAAK,YAAY,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACpC,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACnC,IAAI,CAAC,0BAA0B,EAAE,CAAC;aACnC;SACF;KACF;IAEO,gDAA2B,GAAnC,UAAoC,UAAqB;QACvD,IAAM,KAAK,GAAW,UAAU,CAAC,GAAG,EAAE,UAAU,CAAW,CAAC;QAC5D,IAAM,IAAI,GAAY,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAClD,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,IAAI,CAAC,mBAAmB,CAAC,IAAiB,CAAC,CAAC;SAC7C;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE;;YAExB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,KAAK,CAAC,CAAC;SACrD;KACF;IAEO,+CAA0B,GAAlC;QACE,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC;YAC5C,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;aAAM;;YAEL,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SAC7D;KACF;IAEO,wCAAmB,GAA3B;;QAEE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;;QAE5B,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;;;QAIlE,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAE/B,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;IAEO,8CAAyB,GAAjC,UAAkC,UAAoC;;QAEpE,IAAM,KAAK,GAAW,UAAU,CAAC,GAAG,EAAE,UAAU,CAAW,CAAC;QAC5D,IAAM,IAAI,GAAY,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAClD,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAgC,CAAC,CAAC;SACnD;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE;YACxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC3B;KACF;IAEO,mCAAc,GAAtB,UAAuB,OAAgB;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;;QAG1B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC1B;IAEO,uCAAkB,GAA1B;QACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,yBAAyB,IAAI,CAAC,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;aACpC;SACF;KACF;IAEO,+BAAU,GAAlB,UAAmB,WAAqC;QACtD,IAAM,GAAG,GAAW,UAAU,CAAC,YAAY,EAAE,WAAW,CAAW,CAAC;QACpE,IAAI,YAAY,IAAI,WAAW,EAAE;YAC/B,IAAM,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;YAC1C,IAAI,GAAG,KAAK,YAAY,EAAE;gBACxB,IAAI,CAAC,YAAY,CACf,OAKC,CACF,CAAC;aACH;iBAAM,IAAI,GAAG,KAAK,gBAAgB,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAC/C,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;gBAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACxD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;iBAClD;gBACD,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;iBAAM,IAAI,GAAG,KAAK,gBAAgB,EAAE;;;gBAGnC,IAAI,CAAC,qBAAqB,CAAC,OAAiB,CAAC,CAAC;aAC/C;iBAAM,IAAI,GAAG,KAAK,aAAa,EAAE;;gBAEhC,IAAI,CAAC,QAAQ,CAAC,OAAiB,CAAC,CAAC;aAClC;iBAAM,IAAI,GAAG,KAAK,aAAa,EAAE;gBAChC,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,CAAC;aACnC;iBAAM,IAAI,GAAG,KAAK,YAAY,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,6BAA6B,EAAE,CAAC;aACtC;iBAAM;gBACL,KAAK,CAAC,kCAAkC,GAAG,GAAG,CAAC,CAAC;aACjD;SACF;KACF;;;;IAKO,iCAAY,GAApB,UAAqB,SAKpB;QACC,IAAM,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC;QAC/B,IAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;;QAE3B,IAAI,IAAI,CAAC,MAAM,yBAA+B;YAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACrD,IAAI,gBAAgB,KAAK,OAAO,EAAE;gBAChC,IAAI,CAAC,oCAAoC,CAAC,CAAC;aAC5C;;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;KACF;IAEO,qCAAgB,GAAxB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;QACvD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC1B;KACF;IAEO,kCAAa,GAArB,UAAsB,IAA0B;QAAhD,iBAyBC;QAxBC,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,CAC5B,IAAI,CAAC,gBAAgB,EAAE,EACvB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,CACf,CAAC;;;QAGF,IAAI,CAAC,2BAA2B;YAC9B,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1D,IAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;;QAGlD,qBAAqB,CAAC;YACpB,IAAI,KAAI,CAAC,cAAc,EAAE;gBACvB,KAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC1C,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;aAC7B;SACF,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;KACjC;IAEO,6BAAQ,GAAhB,UAAiB,IAAY;QAC3B,IAAI,CAAC,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;;;QAG3B,IAAI,IAAI,CAAC,MAAM,wBAA8B;YAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;aAAM;;YAEL,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;KACF;IAEO,6CAAwB,GAAhC,UAAiC,IAAe,EAAE,SAAiB;QAAnE,iBAoBC;QAnBC,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,qBAA2B;QAEtC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;;;QAID,IAAI,IAAI,CAAC,yBAAyB,KAAK,CAAC,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;aAAM;YACL,qBAAqB,CAAC;gBACpB,KAAI,CAAC,6BAA6B,EAAE,CAAC;aACtC,EAAE,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;SACrD;KACF;IAEO,kDAA6B,GAArC;;QAEE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,wBAA8B;YAC/D,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACnD;KACF;IAEO,+CAA0B,GAAlC;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;;YAE1C,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;KACF;;;;;IAMO,sCAAiB,GAAzB,UAA0B,aAAsB;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;;QAIlB,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,yBAA+B;YAC9D,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;;YAEzC,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE;gBACpC,iBAAiB,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;;gBAExD,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACnD;SACF;aAAM,IAAI,IAAI,CAAC,MAAM,wBAA8B;YAClD,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;IAEO,0CAAqB,GAA7B,UAA8B,MAAc;QAC1C,IAAI,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QAEpE,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;;;QAID,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;IAEO,8BAAS,GAAjB,UAAkB,IAAY;QAC5B,IAAI,IAAI,CAAC,MAAM,wBAA8B;YAC3C,MAAM,6BAA6B,CAAC;SACrC;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrB;KACF;;;;IAKD,0BAAK,GAAL;QACE,IAAI,IAAI,CAAC,MAAM,2BAAiC;YAC9C,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC1C,IAAI,CAAC,MAAM,wBAA8B;YAEzC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC3B;SACF;KACF;IAEO,sCAAiB,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACnB;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;KACF;IACH,iBAAC;AAAD,CAAC;;ACxjBD;;;;;;;;;;;;;;;;AAmBA;;;;;;AAMA;IAAA;KA8DC;IA5CC,2BAAG,GAAH,UACE,UAAkB,EAClB,IAAa,EACb,UAA2C,EAC3C,IAAa,KACX;IAEJ,6BAAK,GAAL,UACE,UAAkB,EAClB,IAAa,EACb,UAAiD,EACjD,IAAa,KACX;;;;;IAMJ,wCAAgB,GAAhB,UAAiB,KAAa,KAAI;;;;;IAMlC,4CAAoB,GAApB,UAAqB,KAAa,KAAI;IAEtC,uCAAe,GAAf,UACE,UAAkB,EAClB,IAAa,EACb,UAA2C,KACzC;IAEJ,yCAAiB,GAAjB,UACE,UAAkB,EAClB,IAAa,EACb,UAA2C,KACzC;IAEJ,0CAAkB,GAAlB,UACE,UAAkB,EAClB,UAA2C,KACzC;IAEJ,mCAAW,GAAX,UAAY,KAA+B,KAAI;IACjD,oBAAC;AAAD,CAAC;;ACvFD;;;;;;;;;;;;;;;;AAmBA;;;;AAIA;IAQE,sBAAoB,cAAwB;QAAxB,mBAAc,GAAd,cAAc,CAAU;QAPpC,eAAU,GAKd,EAAE,CAAC;QAGLpD,WAAM,CACJ,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAC1D,4BAA4B,CAC7B,CAAC;KACH;;;;IAaS,8BAAO,GAAjB,UAAkB,SAAiB;QAAE,iBAAqB;aAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;YAArB,gCAAqB;;QACxD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE;;YAE7C,IAAM,SAAS,wCAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAC,CAAC;YAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC5D;SACF;KACF;IAED,yBAAE,GAAF,UAAG,SAAiB,EAAE,QAA8B,EAAE,OAAgB;QACpE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC9D,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QAEvD,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,SAAS,EAAE;YACb,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;SACpC;KACF;IAED,0BAAG,GAAH,UAAI,SAAiB,EAAE,QAA8B,EAAE,OAAgB;QACrE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACnC,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IACE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ;iBACjC,CAAC,OAAO,IAAI,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAC9C;gBACA,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvB,OAAO;aACR;SACF;KACF;IAEO,yCAAkB,GAA1B,UAA2B,SAAiB;QAC1CA,WAAM,CACJ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAA,EAAE;YACzB,OAAO,EAAE,KAAK,SAAS,CAAC;SACzB,CAAC,EACF,iBAAiB,GAAG,SAAS,CAC9B,CAAC;KACH;IACH,mBAAC;AAAD,CAAC;;AC7FD;;;;;;;;;;;;;;;;AAqBA;;;;;;;AAOA;IAAmCK,uCAAY;IAO7C;QAAA,YACE,kBAAM,CAAC,QAAQ,CAAC,CAAC,SAiClB;QAxCO,aAAO,GAAG,IAAI,CAAC;;;;;QAarB,IACE,OAAO,MAAM,KAAK,WAAW;YAC7B,OAAO,MAAM,CAAC,gBAAgB,KAAK,WAAW;YAC9C,CAACgD,oBAAe,EAAE,EAClB;YACA,MAAM,CAAC,gBAAgB,CACrB,QAAQ,EACR;gBACE,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE;oBACjB,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;iBAC9B;aACF,EACD,KAAK,CACN,CAAC;YAEF,MAAM,CAAC,gBAAgB,CACrB,SAAS,EACT;gBACE,IAAI,KAAI,CAAC,OAAO,EAAE;oBAChB,KAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;iBAC/B;aACF,EACD,KAAK,CACN,CAAC;SACH;;KACF;IAtCM,yBAAW,GAAlB;QACE,OAAO,IAAI,aAAa,EAAE,CAAC;KAC5B;IAsCD,uCAAe,GAAf,UAAgB,SAAiB;QAC/BrD,WAAM,CAAC,SAAS,KAAK,QAAQ,EAAE,sBAAsB,GAAG,SAAS,CAAC,CAAC;QACnE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACvB;IAED,uCAAe,GAAf;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IACH,oBAAC;AAAD,CAnDA,CAAmC,YAAY;;AC5B/C;;;;;;;;;;;;;;;;AAuBA;IAAuCK,2CAAY;IAOjD;QAAA,YACE,kBAAM,CAAC,SAAS,CAAC,CAAC,SA0CnB;QAzCC,IAAI,MAAc,CAAC;QACnB,IAAI,gBAAwB,CAAC;QAC7B,IACE,OAAO,QAAQ,KAAK,WAAW;YAC/B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,WAAW,EAChD;YACA,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,WAAW,EAAE;;gBAE7C,gBAAgB,GAAG,kBAAkB,CAAC;gBACtC,MAAM,GAAG,QAAQ,CAAC;aACnB;iBAAM,IAAI,OAAO,QAAQ,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBACvD,gBAAgB,GAAG,qBAAqB,CAAC;gBACzC,MAAM,GAAG,WAAW,CAAC;aACtB;iBAAM,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE;gBACtD,gBAAgB,GAAG,oBAAoB,CAAC;gBACxC,MAAM,GAAG,UAAU,CAAC;aACrB;iBAAM,IAAI,OAAO,QAAQ,CAAC,cAAc,CAAC,KAAK,WAAW,EAAE;gBAC1D,gBAAgB,GAAG,wBAAwB,CAAC;gBAC5C,MAAM,GAAG,cAAc,CAAC;aACzB;SACF;;;;;QAMD,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,gBAAgB,EAAE;YACpB,QAAQ,CAAC,gBAAgB,CACvB,gBAAgB,EAChB;gBACE,IAAM,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAClC,IAAI,OAAO,KAAK,KAAI,CAAC,QAAQ,EAAE;oBAC7B,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBACxB,KAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;iBAClC;aACF,EACD,KAAK,CACN,CAAC;SACH;;KACF;IA/CM,6BAAW,GAAlB;QACE,OAAO,IAAI,iBAAiB,EAAE,CAAC;KAChC;IA+CD,2CAAe,GAAf,UAAgB,SAAiB;QAC/BL,WAAM,CAAC,SAAS,KAAK,SAAS,EAAE,sBAAsB,GAAG,SAAS,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACxB;IACH,wBAAC;AAAD,CAxDA,CAAuC,YAAY;;ACvBnD;;;;;;;;;;;;;;;;AA4CA,IAAM,mBAAmB,GAAG,IAAI,CAAC;AACjC,IAAM,2BAA2B,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;AAClD,IAAM,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAAC;AACrC,IAAM,8BAA8B,GAAG,EAAE,GAAG,IAAI,CAAC;AACjD,IAAM,0BAA0B,GAAG,GAAG,CAAC;AACvC,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,4BAA4B,GAAG,aAAa,CAAC;AAEnD;AACA,IAAM,uBAAuB,GAAG,CAAC,CAAC;AA8BlC;;;;;;AAMA;IAA0CK,8CAAa;;;;;;IAwDrD,8BACU,SAAmB,EACnB,cAAsB,EACtB,aAKC,EACD,gBAAsC,EACtC,mBAAyC,EACzC,kBAAqC,EACrC,sBAA6C,EAC7C,aAA6B;QAbvC,YAeE,iBAAO,SAaR;QA3BS,eAAS,GAAT,SAAS,CAAU;QACnB,oBAAc,GAAd,cAAc,CAAQ;QACtB,mBAAa,GAAb,aAAa,CAKZ;QACD,sBAAgB,GAAhB,gBAAgB,CAAsB;QACtC,yBAAmB,GAAnB,mBAAmB,CAAsB;QACzC,wBAAkB,GAAlB,kBAAkB,CAAmB;QACrC,4BAAsB,GAAtB,sBAAsB,CAAuB;QAC7C,mBAAa,GAAb,aAAa,CAAgB;;QAnEvC,QAAE,GAAG,oBAAoB,CAAC,2BAA2B,EAAE,CAAC;QAChD,UAAI,GAAG,UAAU,CAAC,IAAI,GAAG,KAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;QAExC,uBAAiB,GAAkC,EAAE,CAAC;QAC7C,aAAO,GAGpB,IAAI,GAAG,EAAE,CAAC;QACN,sBAAgB,GAAqB,EAAE,CAAC;QACxC,sBAAgB,GAAqB,EAAE,CAAC;QACxC,0BAAoB,GAAG,CAAC,CAAC;QACzB,0BAAoB,GAAG,CAAC,CAAC;QACzB,+BAAyB,GAA0B,EAAE,CAAC;QACtD,gBAAU,GAAG,KAAK,CAAC;QACnB,qBAAe,GAAG,mBAAmB,CAAC;QACtC,wBAAkB,GAAG,2BAA2B,CAAC;QACjD,4BAAsB,GAAiC,IAAI,CAAC;QACpE,mBAAa,GAAkB,IAAI,CAAC;QAE5B,+BAAyB,GAAkB,IAAI,CAAC;QAEhD,cAAQ,GAAY,KAAK,CAAC;;QAG1B,oBAAc,GAA0C,EAAE,CAAC;QAC3D,oBAAc,GAAG,CAAC,CAAC;QAEnB,eAAS,GAGN,IAAI,CAAC;QAER,gBAAU,GAAkB,IAAI,CAAC;QACjC,oBAAc,GAAkB,IAAI,CAAC;QACrC,wBAAkB,GAAG,KAAK,CAAC;QAC3B,4BAAsB,GAAG,CAAC,CAAC;QAC3B,gCAA0B,GAAG,CAAC,CAAC;QAE/B,sBAAgB,GAAG,IAAI,CAAC;QACxB,gCAA0B,GAAkB,IAAI,CAAC;QACjD,oCAA8B,GAAkB,IAAI,CAAC;QA+B3D,IAAI,aAAa,IAAI,CAACH,cAAS,EAAE,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,gFAAgF,CACjF,CAAC;SACH;QAED,iBAAiB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,CAAC;QAErE,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5C,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAI,CAAC,SAAS,EAAE,KAAI,CAAC,CAAC;SAChE;;KACF;IAES,0CAAW,GAArB,UACE,MAAc,EACd,IAAa,EACb,UAAiC;QAEjC,IAAM,SAAS,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC;QAExC,IAAM,GAAG,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;QACjD,IAAI,CAAC,IAAI,CAACT,cAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1BO,WAAM,CACJ,IAAI,CAAC,UAAU,EACf,wDAAwD,CACzD,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;SAC7C;KACF;IAED,kCAAG,GAAH,UAAI,KAAmB;QAAvB,iBAmDC;QAlDC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAM,QAAQ,GAAG,IAAIiB,aAAQ,EAAU,CAAC;QACxC,IAAM,OAAO,GAAG;YACd,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;YACzB,CAAC,EAAE,KAAK,CAAC,YAAY;SACtB,CAAC;QACF,IAAM,cAAc,GAAG;YACrB,MAAM,EAAE,GAAG;YACX,OAAO,SAAA;YACP,UAAU,EAAE,UAAC,OAAiC;gBAC5C,IAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAW,CAAC;gBACvC,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;oBACzB,KAAI,CAAC,aAAa,CAChB,OAAO,CAAC,GAAG,CAAC,EACZ,OAAO;gCACK,KAAK;4BACT,IAAI,CACb,CAAC;oBACF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC3B;qBAAM;oBACL,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;iBAC1B;aACF;SACF,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,UAAU,CAAC;gBACT,IAAM,GAAG,GAAG,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAI,GAAG,KAAK,SAAS,IAAI,cAAc,KAAK,GAAG,EAAE;oBAC/C,OAAO;iBACR;gBACD,OAAO,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACpC,KAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,KAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;oBACnC,KAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;iBAC5B;gBACD,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,0BAA0B,CAAC,CAAC;gBACvD,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;aAClD,EAAE,mBAAmB,CAAC,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACtB;QAED,OAAO,QAAQ,CAAC,OAAO,CAAC;KACzB;IAED,qCAAM,GAAN,UACE,KAAmB,EACnB,aAA2B,EAC3B,GAAkB,EAClB,UAA2C;QAE3C,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC;QACvC,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,oBAAoB,GAAG,UAAU,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;SACzC;QACDjB,WAAM,CACJ,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,EACpE,oDAAoD,CACrD,CAAC;QACFA,WAAM,CACJ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAC3C,8CAA8C,CAC/C,CAAC;QACF,IAAM,UAAU,GAAe;YAC7B,UAAU,YAAA;YACV,MAAM,EAAE,aAAa;YACrB,KAAK,OAAA;YACL,GAAG,KAAA;SACJ,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;SAC9B;KACF;IAEO,uCAAQ,GAAhB,UAAiB,KAAa;QAA9B,iBAYC;QAXC,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,EAAE,UAAC,OAAiC;YACnE,OAAO,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACpC,KAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,KAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;gBACnC,KAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;aAC5B;YACD,IAAI,GAAG,CAAC,UAAU,EAAE;gBAClB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;aACzB;SACF,CAAC,CAAC;KACJ;IAEO,0CAAW,GAAnB,UAAoB,UAAsB;QAA1C,iBAwCC;QAvCC,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC/B,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC1C,IAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC,CAAC;QACzD,IAAM,GAAG,GAA6B,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC;QAEjE,IAAM,MAAM,GAAG,GAAG,CAAC;;QAGnB,IAAI,UAAU,CAAC,GAAG,EAAE;YAClB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC;YAC9B,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;SAC3B;QAED,GAAG,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QAExC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,UAAC,OAAiC;YAC9D,IAAM,OAAO,GAAY,OAAO,UAAU,GAAG,CAAC,CAAC;YAC/C,IAAM,MAAM,GAAG,OAAO,YAAY,GAAG,CAAW,CAAC;;YAGjD,oBAAoB,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAE3D,IAAM,iBAAiB,GACrB,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;gBAC5B,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;YAE7C,IAAI,iBAAiB,KAAK,UAAU,EAAE;gBACpC,KAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBAEtC,IAAI,MAAM,KAAK,IAAI,EAAE;oBACnB,KAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;iBACzC;gBAED,IAAI,UAAU,CAAC,UAAU,EAAE;oBACzB,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;iBACxC;aACF;SACF,CAAC,CAAC;KACJ;IAEc,0CAAqB,GAApC,UAAqC,OAAgB,EAAE,KAAmB;QACxE,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAIL,aAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;;YAEpE,IAAM,QAAQ,GAAG2D,YAAO,CAAC,OAAc,EAAE,GAAG,CAAC,CAAC;YAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC5D,IAAM,SAAS,GACb,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC;gBACnE,IAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACzC,IAAI,CACF,+DAA+D;qBAC7D,6CAA2C,SAAS,SAAM,CAAA;qBACvD,SAAS,oDAAiD,CAAA,CAChE,CAAC;aACH;SACF;KACF;IAED,+CAAgB,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;aAAM;;;YAGL,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,EAAE,eAAQ,CAAC,CAAC;aAC1C;SACF;QAED,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC;KACpD;IAEO,qEAAsC,GAA9C,UAA+C,UAAkB;;;QAG/D,IAAM,gBAAgB,GAAG,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,CAAC;QAChE,IAAI,gBAAgB,IAAIC,YAAO,CAAC,UAAU,CAAC,EAAE;YAC3C,IAAI,CAAC,IAAI,CACP,+DAA+D,CAChE,CAAC;YACF,IAAI,CAAC,kBAAkB,GAAG,8BAA8B,CAAC;SAC1D;KACF;IAED,mDAAoB,GAApB,UAAqB,KAAoB;QACvC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;aAAM;;;;YAIL,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,EAAE,eAAQ,CAAC,CAAC;aAC5C;SACF;KACF;;;;;IAMD,sCAAO,GAAP;QAAA,iBA4BC;QA3BC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE;YACtC,IAAM,OAAK,GAAG,IAAI,CAAC,UAAU,CAAC;YAC9B,IAAM,UAAU,GAAGC,kBAAa,CAAC,OAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;YAC3D,IAAM,WAAW,GAA6B,EAAE,IAAI,EAAE,OAAK,EAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;gBAC/B,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;aAC9B;iBAAM,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;gBACjD,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;aAC7C;YACD,IAAI,CAAC,WAAW,CACd,UAAU,EACV,WAAW,EACX,UAAC,GAA6B;gBAC5B,IAAM,MAAM,GAAG,GAAG,YAAY,GAAG,CAAW,CAAC;gBAC7C,IAAM,IAAI,GAAI,GAAG,UAAU,GAAG,CAAY,IAAI,OAAO,CAAC;gBAEtD,IAAI,KAAI,CAAC,UAAU,KAAK,OAAK,EAAE;oBAC7B,IAAI,MAAM,KAAK,IAAI,EAAE;wBACnB,KAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;qBACjC;yBAAM;;wBAEL,KAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;qBACnC;iBACF;aACF,CACF,CAAC;SACH;KACF;;;;;;IAOD,0CAAW,GAAX;QAAA,iBAgBC;QAfC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;YAC1C,IAAI,CAAC,WAAW,CACd,UAAU,EACV,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,EAChC,UAAC,GAA6B;gBAC5B,IAAM,MAAM,GAAG,GAAG,YAAY,GAAG,CAAW,CAAC;gBAC7C,IAAM,IAAI,GAAI,GAAG,UAAU,GAAG,CAAY,IAAI,OAAO,CAAC;gBACtD,IAAI,MAAM,KAAK,IAAI,EAAE;oBACnB,KAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC;iBACrC;qBAAM;oBACL,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBACvC;aACF,CACF,CAAC;SACH;KACF;;;;IAKD,uCAAQ,GAAR,UAAS,KAAmB,EAAE,GAAkB;QAC9C,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC1C,IAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC;QAEvC,IAAI,CAAC,IAAI,CAAC,sBAAsB,GAAG,UAAU,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC;QAE/DxD,WAAM,CACJ,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,EACpE,sDAAsD,CACvD,CAAC;QACF,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YAC7B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;SAClE;KACF;IAEO,4CAAa,GAArB,UACE,UAAkB,EAClB,OAAe,EACf,QAAgB,EAChB,GAAkB;QAElB,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC,CAAC;QAE3D,IAAM,GAAG,GAA6B,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC;QACjE,IAAM,MAAM,GAAG,GAAG,CAAC;;QAEnB,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YACpB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAC/B;IAED,8CAAe,GAAf,UACE,UAAkB,EAClB,IAAa,EACb,UAA2C;QAE3C,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;SAC3D;aAAM;YACL,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;gBAClC,UAAU,YAAA;gBACV,MAAM,EAAE,GAAG;gBACX,IAAI,MAAA;gBACJ,UAAU,YAAA;aACX,CAAC,CAAC;SACJ;KACF;IAED,gDAAiB,GAAjB,UACE,UAAkB,EAClB,IAAa,EACb,UAA2C;QAE3C,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;SAC5D;aAAM;YACL,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;gBAClC,UAAU,YAAA;gBACV,MAAM,EAAE,IAAI;gBACZ,IAAI,MAAA;gBACJ,UAAU,YAAA;aACX,CAAC,CAAC;SACJ;KACF;IAED,iDAAkB,GAAlB,UACE,UAAkB,EAClB,UAA2C;QAE3C,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;SAC5D;aAAM;YACL,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;gBAClC,UAAU,YAAA;gBACV,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE,IAAI;gBACV,UAAU,YAAA;aACX,CAAC,CAAC;SACJ;KACF;IAEO,gDAAiB,GAAzB,UACE,MAAc,EACd,UAAkB,EAClB,IAAa,EACb,UAA0C;QAE1C,IAAM,OAAO,GAAG,WAAW,CAAC,EAAE,UAAU,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,UAAC,QAAkC;YACnE,IAAI,UAAU,EAAE;gBACd,UAAU,CAAC;oBACT,UAAU,CACR,QAAQ,YAAY,GAAG,CAAW,EAClC,QAAQ,YAAY,GAAG,CAAW,CACnC,CAAC;iBACH,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACnB;SACF,CAAC,CAAC;KACJ;IAED,kCAAG,GAAH,UACE,UAAkB,EAClB,IAAa,EACb,UAA2C,EAC3C,IAAa;QAEb,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;KAC3D;IAED,oCAAK,GAAL,UACE,UAAkB,EAClB,IAAa,EACb,UAAiD,EACjD,IAAa;QAEb,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;KAC3D;IAED,0CAAW,GAAX,UACE,MAAc,EACd,UAAkB,EAClB,IAAa,EACb,UAAiD,EACjD,IAAa;QAEb,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAM,OAAO,GAA6B;qBAC/B,CAAC,EAAE,UAAU;qBACb,CAAC,EAAE,IAAI;SACjB,CAAC;QAEF,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC;SAC9B;;QAGD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACzB,MAAM,QAAA;YACN,OAAO,SAAA;YACP,UAAU,YAAA;SACX,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,CAAC;SAC3C;KACF;IAEO,uCAAQ,GAAhB,UAAiB,KAAa;QAA9B,iBAwBC;QAvBC,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACnD,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;QACrD,IAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC;QAC3D,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAEtD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,UAAC,OAAiC;YAClE,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC;YAEzC,OAAO,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACpC,KAAI,CAAC,oBAAoB,EAAE,CAAC;;YAG5B,IAAI,KAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;gBACnC,KAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;aAC5B;YAED,IAAI,UAAU,EAAE;gBACd,UAAU,CACR,OAAO,YAAY,GAAG,CAAW,EACjC,OAAO,YAAY,GAAG,CAAW,CAClC,CAAC;aACH;SACF,CAAC,CAAC;KACJ;IAED,0CAAW,GAAX,UAAY,KAA+B;QAA3C,iBAcC;;QAZC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAM,OAAO,GAAG,eAAe,CAAC,EAAE,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAElC,IAAI,CAAC,WAAW,WAAW,GAAG,EAAE,OAAO,EAAE,UAAA,MAAM;gBAC7C,IAAM,MAAM,GAAG,MAAM,YAAY,GAAG,CAAC,CAAC;gBACtC,IAAI,MAAM,KAAK,IAAI,EAAE;oBACnB,IAAM,WAAW,GAAG,MAAM,YAAY,GAAG,CAAC,CAAC;oBAC3C,KAAI,CAAC,IAAI,CAAC,aAAa,EAAE,uBAAuB,GAAG,WAAW,CAAC,CAAC;iBACjE;aACF,CAAC,CAAC;SACJ;KACF;IAEO,6CAAc,GAAtB,UAAuB,OAAiC;QACtD,IAAI,GAAG,IAAI,OAAO,EAAE;;YAElB,IAAI,CAAC,IAAI,CAAC,eAAe,GAAGP,cAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAChD,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAW,CAAC;YACtC,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,UAAU,EAAE;gBACd,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACnC,UAAU,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC,CAAC;aACnC;SACF;aAAM,IAAI,OAAO,IAAI,OAAO,EAAE;YAC7B,MAAM,oCAAoC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;SAC/D;aAAM,IAAI,GAAG,IAAI,OAAO,EAAE;;YAEzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAW,EAAE,OAAO,CAAC,GAAG,CAAO,CAAC,CAAC;SAC9D;KACF;IAEO,0CAAW,GAAnB,UAAoB,MAAc,EAAE,IAA8B;QAChE,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/C,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,IAAI,CAAC,aAAa,CAChB,IAAI,UAAU,GAAG,CAAW,EAC5B,IAAI,UAAU,GAAG,CAAC;wBACN,KAAK,EACjB,IAAI,CAAC,GAAG,CAAW,CACpB,CAAC;SACH;aAAM,IAAI,MAAM,KAAK,GAAG,EAAE;YACzB,IAAI,CAAC,aAAa,CAChB,IAAI,UAAU,GAAG,CAAW,EAC5B,IAAI,UAAU,GAAG,CAAC;yBACL,IAAI,EACjB,IAAI,CAAC,GAAG,CAAW,CACpB,CAAC;SACH;aAAM,IAAI,MAAM,KAAK,GAAG,EAAE;YACzB,IAAI,CAAC,gBAAgB,CACnB,IAAI,UAAU,GAAG,CAAW,EAC5B,IAAI,WAAW,GAAG,CAAc,CACjC,CAAC;SACH;aAAM,IAAI,MAAM,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,cAAc,CACjB,IAAI,iBAAiB,GAAG,CAAW,EACnC,IAAI,mBAAmB,GAAG,CAAW,CACtC,CAAC;SACH;aAAM,IAAI,MAAM,KAAK,KAAK,EAAE;YAC3B,IAAI,CAAC,kBAAkB,CACrB,IAAI,iBAAiB,GAAG,CAAW,EACnC,IAAI,mBAAmB,GAAG,CAAW,CACtC,CAAC;SACH;aAAM,IAAI,MAAM,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SACnC;aAAM;YACL,KAAK,CACH,4CAA4C;gBAC1CA,cAAS,CAAC,MAAM,CAAC;gBACjB,oCAAoC,CACvC,CAAC;SACH;KACF;IAEO,uCAAQ,GAAhB,UAAiB,SAAiB,EAAE,SAAiB;QACnD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,8BAA8B,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QAC3D,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC1B;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAC7B;IAEO,+CAAgB,GAAxB,UAAyB,OAAe;QAAxC,iBAkBC;QAjBCO,WAAM,CACJ,CAAC,IAAI,CAAC,SAAS,EACf,wDAAwD,CACzD,CAAC;QAEF,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;SAC9C;;;QAKD,IAAI,CAAC,yBAAyB,GAAG,UAAU,CAAC;YAC1C,KAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACtC,KAAI,CAAC,oBAAoB,EAAE,CAAC;;SAE7B,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAQ,CAAC;KAChC;IAEO,8CAAe,GAAvB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC5C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC1B;KACF;IAEO,yCAAU,GAAlB,UAAmB,OAAgB;;QAEjC,IACE,OAAO;YACP,CAAC,IAAI,CAAC,QAAQ;YACd,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,kBAAkB,EAChD;YACA,IAAI,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACrD,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;YAE3C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;KACzB;IAEO,wCAAS,GAAjB,UAAkB,MAAe;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClC,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YACxD,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;aACxB;SACF;KACF;IAEO,oDAAqB,GAA7B;QACE,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;QAGtB,IAAI,CAAC,uBAAuB,EAAE,CAAC;;QAG/B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;gBACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAC/C,IAAI,CAAC,0BAA0B,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;aACxD;iBAAM,IAAI,IAAI,CAAC,8BAA8B,EAAE;;gBAE9C,IAAM,6BAA6B,GACjC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,8BAA8B,CAAC;gBAC7D,IAAI,6BAA6B,GAAG,6BAA6B,EAAE;oBACjE,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;iBAC5C;gBACD,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;aAC5C;YAED,IAAM,2BAA2B,GAC/B,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACzD,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAC3B,CAAC,EACD,IAAI,CAAC,eAAe,GAAG,2BAA2B,CACnD,CAAC;YACF,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC;YAEhD,IAAI,CAAC,IAAI,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;;YAGtC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAC7B,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,eAAe,GAAG,0BAA0B,CAClD,CAAC;SACH;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAC9B;IAEa,mDAAoB,GAAlC;;;;;;;6BACM,IAAI,CAAC,gBAAgB,EAAE,EAAvB,wBAAuB;wBACzB,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;wBACzC,IAAI,CAAC,0BAA0B,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;wBACvD,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;wBACrC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC/C,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACnC,iBAAe,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACrD,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,oBAAoB,CAAC,iBAAiB,EAAE,CAAC;wBAClE,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;wBACrC,aAAW,KAAK,CAAC;wBACjB,eAAgC,IAAI,CAAC;wBACnC,OAAO,GAAG;4BACd,IAAI,YAAU,EAAE;gCACd,YAAU,CAAC,KAAK,EAAE,CAAC;6BACpB;iCAAM;gCACL,UAAQ,GAAG,IAAI,CAAC;gCAChB,cAAY,EAAE,CAAC;6BAChB;yBACF,CAAC;wBACI,aAAa,GAAG,UAAU,GAAW;4BACzCA,WAAM,CACJ,YAAU,EACV,wDAAwD,CACzD,CAAC;4BACF,YAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;yBAC7B,CAAC;wBAEF,IAAI,CAAC,SAAS,GAAG;4BACf,KAAK,EAAE,OAAO;4BACd,WAAW,EAAE,aAAa;yBAC3B,CAAC;wBAEI,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;wBAC7C,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;;;;wBAKK,qBAAM,OAAO,CAAC,GAAG,CAAC;gCACnD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC;gCAC9C,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,YAAY,CAAC;6BACnD,CAAC,EAAA;;wBAHI,KAAAkD,4BAA6B,SAGjC,KAAA,EAHK,SAAS,QAAA,EAAE,aAAa,QAAA;wBAK/B,IAAI,CAAC,UAAQ,EAAE;4BACb,GAAG,CAAC,4CAA4C,CAAC,CAAC;4BAClD,IAAI,CAAC,UAAU,GAAG,SAAS,IAAI,SAAS,CAAC,WAAW,CAAC;4BACrD,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,aAAa,CAAC,KAAK,CAAC;4BAC3D,YAAU,GAAG,IAAI,UAAU,CACzB,MAAM,EACN,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,UAAU,EACf,aAAa,EACb,OAAO,EACP,cAAY;0CACE,UAAA,MAAM;gCAClB,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC;gCACtD,KAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;6BAC9C,EACD,aAAa,CACd,CAAC;yBACH;6BAAM;4BACL,GAAG,CAAC,uCAAuC,CAAC,CAAC;yBAC9C;;;;wBAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,GAAG,OAAK,CAAC,CAAC;wBAC3C,IAAI,CAAC,UAAQ,EAAE;4BACb,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;;;;gCAI5B,IAAI,CAAC,OAAK,CAAC,CAAC;6BACb;4BACD,OAAO,EAAE,CAAC;yBACX;;;;;;KAGN;IAED,wCAAS,GAAT,UAAU,MAAc;QACtB,GAAG,CAAC,sCAAsC,GAAG,MAAM,CAAC,CAAC;QACrD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;aAAM;YACL,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAClC,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC7C,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;aACvC;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAC9B;SACF;KACF;IAED,qCAAM,GAAN,UAAO,MAAc;QACnB,GAAG,CAAC,kCAAkC,GAAG,MAAM,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtC,IAAIO,YAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;YACnC,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;KACF;IAEO,+CAAgB,GAAxB,UAAyB,SAAiB;QACxC,IAAM,KAAK,GAAG,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAC;KACvD;IAEO,sDAAuB,GAA/B;QACE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,GAAG,aAAa,GAAG,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,EAAE;gBACpD,IAAI,GAAG,CAAC,UAAU,EAAE;oBAClB,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;iBAC9B;gBAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;SACF;;QAGD,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;YACnC,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;SAC5B;KACF;IAEO,+CAAgB,GAAxB,UAAyB,UAAkB,EAAE,KAAiB;;QAE5D,IAAI,OAAO,CAAC;QACZ,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,GAAG,SAAS,CAAC;SACrB;aAAM;YACL,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,iBAAiB,CAAC,CAAC,CAAC,GAAA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC1D;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE;YAC/B,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;SACxC;KACF;IAEO,4CAAa,GAArB,UAAsB,UAAkB,EAAE,OAAe;QACvD,IAAM,oBAAoB,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7D,IAAI,MAAM,CAAC;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE;YAC1C,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAE,CAAC;YACpD,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1B,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;gBAClB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;aAC3C;SACF;aAAM;;YAEL,MAAM,GAAG,SAAS,CAAC;SACpB;QACD,OAAO,MAAM,CAAC;KACf;IAEO,6CAAc,GAAtB,UAAuB,UAAkB,EAAE,WAAmB;QAC5D,GAAG,CAAC,sBAAsB,GAAG,UAAU,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,UAAU,KAAK,eAAe,IAAI,UAAU,KAAK,mBAAmB,EAAE;;;;YAIxE,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,sBAAsB,IAAI,uBAAuB,EAAE;;gBAE1D,IAAI,CAAC,eAAe,GAAG,8BAA8B,CAAC;;;gBAItD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;aACjD;SACF;KACF;IAEO,iDAAkB,GAA1B,UAA2B,UAAkB,EAAE,WAAmB;QAChE,GAAG,CAAC,2BAA2B,GAAG,UAAU,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;;;QAG/B,IAAI,UAAU,KAAK,eAAe,IAAI,UAAU,KAAK,mBAAmB,EAAE;;;;YAIxE,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,0BAA0B,IAAI,uBAAuB,EAAE;gBAC9D,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,EAAE,CAAC;aACrD;SACF;KACF;IAEO,qDAAsB,GAA9B,UAA+B,IAA8B;QAC3D,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SACnC;aAAM;YACL,IAAI,KAAK,IAAI,IAAI,EAAE;gBACjB,OAAO,CAAC,GAAG,CACT,YAAY,GAAI,IAAI,CAAC,KAAK,CAAY,CAAC,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CACrE,CAAC;aACH;SACF;KACF;IAEO,4CAAa,GAArB;;;QAEE,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;;;;YAInB,KAAsB,IAAA,KAAAL,eAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAxC,IAAM,OAAO,WAAA;;oBAChB,KAAyB,IAAA,oBAAAA,eAAA,OAAO,CAAC,MAAM,EAAE,CAAA,CAAA,gBAAA,4BAAE;wBAAtC,IAAM,UAAU,WAAA;wBACnB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;qBAC9B;;;;;;;;;aACF;;;;;;;;;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;gBAC5B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAClB;SACF;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE;YAC5C,IAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;YACvD,IAAI,CAAC,iBAAiB,CACpB,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,UAAU,CACnB,CAAC;SACH;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;gBAC5B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAClB;SACF;KACF;;;;IAKO,gDAAiB,GAAzB;QACE,IAAM,KAAK,GAA4B,EAAE,CAAC;QAE1C,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAIlD,cAAS,EAAE,EAAE;YACf,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;gBAC5B,UAAU,GAAG,YAAY,CAAC;aAC3B;iBAAM;gBACL,UAAU,GAAG,MAAM,CAAC;aACrB;SACF;QAED,KAAK,CAAC,MAAM,GAAG,UAAU,GAAG,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAEvE,IAAImD,oBAAe,EAAE,EAAE;YACrB,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;SAChC;aAAM,IAAIK,kBAAa,EAAE,EAAE;YAC1B,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;SACpC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACzB;IAEO,+CAAgB,GAAxB;QACE,IAAM,MAAM,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,EAAE,CAAC;QAC7D,OAAOD,YAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,MAAM,CAAC;KAClD;IA19Bc,gDAA2B,GAAG,CAAC,CAAC;;;;IAKhC,sCAAiB,GAAG,CAAC,CAAC;IAs9BvC,2BAAC;CAAA,CAvgCyC,aAAa;;ACzFvD;;;;;;;;;;;;;;;;AAqBA;;;;;AAKA;IAGE,uBAAoB,WAA4B;QAA5B,gBAAW,GAAX,WAAW,CAAiB;QAFxC,UAAK,GAAmC,IAAI,CAAC;KAED;IAEpD,2BAAG,GAAH;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAExC,IAAM,KAAK,sBAAQ,QAAQ,CAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,IAAY,EAAE,KAAa;gBAC3C,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;aACnC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QAEtB,OAAO,KAAK,CAAC;KACd;IACH,oBAAC;AAAD,CAAC;;AC5CD;;;;;;;;;;;;;;;;SAuEgB,wBAAwB,CACtC,QAAuB,EACvB,IAAY;IAEZ,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACvC;;AC5EA;;;;;;;;;;;;;;;;AAwZA;SACgB,uBAAuB,CACrC,IAAU,EACV,QAAqD;IAErD,IAAI,CAAC,4BAA4B,GAAG,QAAQ,CAAC;AAC/C,CAAC;SA6Xe,SAAS,CAAC,IAAU,EAAE,SAA0B;IAA1B,0BAAA,EAAA,iBAA0B;IAC9D,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAClC,OAAO;KACR;IAED,IAAI,KAA+B,CAAC;IACpC,IAAI,SAAS,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtD;QACD,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;KACnC;SAAM;QACL,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;KAC3B;IAED,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAC3C,UAAC,aAAa,EAAE,YAAY;QAC1B,OAAA,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC;KAAA,EAC9C,CAAC,CACF,CAAC;IAEF,IAAI,CAAC,KAAK,EAAE,UAAC,IAAY,EAAE,KAAc;QACvC,IAAI,UAAU,GAAG,IAAI,CAAC;;QAEtB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClD,UAAU,IAAI,GAAG,CAAC;SACnB;QACD,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;KACjC,CAAC,CAAC;AACL,CAAC;SAEe,yBAAyB,CAAC,IAAU,EAAE,MAAc;IAClE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACrC,wBAAwB,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AACxD;;AC7zBA;;;;;;;;;;;;;;;;AA2CA;;;;;AAMO,IAAM,gBAAgB,GAAG;IAC9B,mBAAmB,CAAC,aAAa,EAAE,CAAC;IACpC,qBAAqB,CAAC,UAAU,EAAE,CAAC;AACrC,CAAC,CAAC;AAEK,IAAM,eAAe,GAAG;IAC7B,qBAAqB,CAAC,aAAa,EAAE,CAAC;AACxC,CAAC,CAAC;AAEF;AACO,IAAM,qBAAqB,GAAG;IACnC,OAAO,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9C,CAAC,CAAC;AAEK,IAAM,wBAAwB,GAAG,UACtC,GAAc,EACd,QAA6B;IAE7B,IAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,qBAAqB,CAAC;;IAE5D,UAAkB,CAAC,sBAAsB,GAAG,QAAQ,CAAC;AACxD,CAAC,CAAC;AAEK,IAAM,KAAK,GAAG,UAAU,GAAc,EAAE,SAAmB;IAChE,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEK,IAAM,qBAAqB,GAAG,UAAU,GAAc,EAAE,MAAc;IAC3E,yBAAyB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzD,CAAC,CAAC;AAEK,IAAM,eAAe,GAAG,UAAU,GAAc;IACrD,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC;AAC7C,CAAC,CAAC;AAEK,IAAM,mBAAmB,GAAG,UACjC,GAAc,EACd,QAAkD;IAElD,OAAO,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAChE,CAAC,CAAC;AAEF;;;;;;;;;SASgBE,gBAAc,CAAI,EAcjC;QAbC,GAAG,SAAA,EACH,GAAG,SAAA,EACH,OAAO,aAAA,EACP,cAAc,oBAAA,EACd,SAAS,eAAA,EACT,iBAAiB,EAAjB,SAAS,mBAAG,KAAK,KAAA;IAYjB,aAAa,CAAC,OAAO,CAAC,CAAC;;;;;IAMvB,IAAM,YAAY,GAAG,IAAIC,kBAAQ,CAC/B,eAAe,EACf,IAAIC,4BAAkB,CAAC,qBAAqB,CAAC,CAC9C,CAAC;IACF,YAAY,CAAC,YAAY,CACvB,IAAIC,mBAAS,CAAC,eAAe,EAAE,cAAM,OAAA,cAAc,GAAA,0BAAwB,CAC5E,CAAC;IAEF,OAAO;QACL,QAAQ,EAAE,IAAI,QAAQ,CACpBC,oCAA2B,CACzB,GAAG,EACH,YAAY;gCACY,SAAS,EACjC,GAAG,EACH,SAAS,CACV,EACD,GAAG,CACc;QACnB,SAAS,WAAA;KACV,CAAC;AACJ;;;;;;;;;;;;;;;ACjJA;;;;;;;;;;;;;;;;AAwBO,IAAM,cAAc,GAAG,oBAAoB,CAAC;AAEnD;AACC,oBAAoB,CAAC,SAAiB,CAAC,YAAY,GAAG,UACrD,UAAkB,EAClB,UAAgC;IAEhC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,UAAU,CAAC,CAAC;AACvD,CAAC,CAAC;AAEF;AACC,oBAAoB,CAAC,SAAiB,CAAC,IAAI,GAAG,UAC7C,IAAa,EACb,MAA4B;IAE5B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF;AACO,IAAM,kBAAkB,GAAG,UAAU,CAAC;AAEtC,IAAM,UAAU,GAAG,UAAU,OAAqB;IACvD,IAAM,MAAM,GAAG,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC;IAClD,oBAAoB,CAAC,SAAS,CAAC,GAAG,GAAG,UACnC,UAAU,EACV,IAAI,EACJ,UAAU,EACV,IAAI;QAEJ,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,GAAG,OAAO,EAAE,CAAC;SAClB;QACD,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;KACvD,CAAC;IACF,OAAO;QACL,oBAAoB,CAAC,SAAS,CAAC,GAAG,GAAG,MAAM,CAAC;KAC7C,CAAC;AACJ,CAAC,CAAC;AAEK,IAAM,gBAAgB,GAAG,QAAQ,CAAC;AAElC,IAAM,eAAe,GAAG,UAAU,KAAY;IACnD,OAAO,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC;AAC1C,CAAC,CAAC;AAEF;;;AAGO,IAAM,eAAe,GAAG,UAAU,eAAwB;AAEjE,CAAC;;;;;;;;;;;;;;;AC1ED;;;;;;;;;;;;;;;;AAmCA,gBAAgB,CAACC,oBAAM,CAAC,CAAC;IAEnB,WAAW,GAAG,QAAQ,CAAC,YAAY;AAEzC;;;;;;;;;SASgB,cAAc,CAC5B,GAAgB,EAChB,GAAW,EACX,OAAe,EACf,SAAgB;IAAhB,0BAAA,EAAA,gBAAgB;IAEhBC,cAAS,CAAC,UAAU,GAAG,SAAS,CAAC;IACjC,OAAOC,gBAAuB,CAAC;QAC7B,GAAG,KAAA;QACH,GAAG,KAAA;QACH,OAAO,SAAA;;;QAGP,cAAc,EAAG,GAAW,CAAC,QAAgC;QAC7D,SAAS,EAAE;YACT,SAAS,WAAA;YACT,KAAK,OAAA;YACL,QAAQ,UAAA;YACR,YAAY,cAAA;YACZ,aAAa,wBAAA;YACb,QAAQ,UAAA;YACR,WAAW,aAAA;YACX,WAAW,aAAA;SACZ;QACD,SAAS,WAAA;KACV,CAAC,CAAC;AACL,CAAC;SAEe,gBAAgB,CAAC,QAA2B;;IAE1D,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;;IAGpC,IAAM,SAAS,GAAI,QAA+B,CAAC,QAAQ,CAAC,iBAAiB,CAC3E,IAAIJ,mBAAS,CACX,iBAAiB,EACjB,UAAC,SAAS,EAAE,EAA2B;YAAL,GAAG,wBAAA;;;QAGnC,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;QAC/D,IAAM,WAAW,GAAG,SAAS;aAC1B,WAAW,CAAC,cAAc,CAAC;aAC3B,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;QACrC,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;KACvC,wBAEF;SACE,eAAe;;IAEd;QACE,SAAS,WAAA;QACT,KAAK,OAAA;QACL,QAAQ,UAAA;QACR,YAAY,cAAA;QACZ,aAAa,wBAAA;QACb,QAAQ,UAAA;QACR,WAAW,aAAA;QACX,WAAW,aAAA;KACZ,CACF;SACA,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAC;IAEF,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAEhD,IAAI5D,cAAS,EAAE,EAAE;QACf,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,cAAc,gBAAA,EAAE,CAAC,CAAC;KACnE;AACH,CAAC;AAED,IAAI;;;;;;;IAOF,IAAM,QAAQ,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC;IACzD,gBAAgB,CAAC,QAAQ,CAAC,CAAC;CAC5B;AAAC,OAAO,GAAG,EAAE;;;IAGZ,IAAI,GAAG,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACnC,MAAM,GAAG,CAAC;KACX;;;;;;;;;;;;;;;;;;"}