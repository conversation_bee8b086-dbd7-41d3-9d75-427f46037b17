<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Profile report</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Profile report</h1>
<div id="header">
<p>Profiled build: assembledebug </p>
<p>Started on: 2022/04/27 - 14:28:48</p>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Summary</a>
</li>
<li>
<a href="#tab1">Configuration</a>
</li>
<li>
<a href="#tab2">Dependency Resolution</a>
</li>
<li>
<a href="#tab3">Artifact Transforms</a>
</li>
<li>
<a href="#tab4">Task Execution</a>
</li>
</ul>
<div class="tab" id="tab0">
<h2>Summary</h2>
<table>
<thead>
<tr>
<th>Description</th>
<th class="numeric">Duration</th>
</tr>
</thead>
<tr>
<td>Total Build Time</td>
<td class="numeric">1.499s</td>
</tr>
<tr>
<td>Startup</td>
<td class="numeric">1.190s</td>
</tr>
<tr>
<td>Settings and buildSrc</td>
<td class="numeric">0.014s</td>
</tr>
<tr>
<td>Loading Projects</td>
<td class="numeric">0.063s</td>
</tr>
<tr>
<td>Configuring Projects</td>
<td class="numeric">0.216s</td>
</tr>
<tr>
<td>Artifact Transforms</td>
<td class="numeric">0s</td>
</tr>
<tr>
<td>Task Execution</td>
<td class="numeric">0s</td>
</tr>
</table>
</div>
<div class="tab" id="tab1">
<h2>Configuration</h2>
<table>
<thead>
<tr>
<th>Project</th>
<th class="numeric">Duration</th>
</tr>
</thead>
<tr>
<td>All projects</td>
<td class="numeric">0.216s</td>
</tr>
<tr>
<td>:</td>
<td class="numeric">0.211s</td>
</tr>
<tr>
<td>:app</td>
<td class="numeric">0.005s</td>
</tr>
</table>
</div>
<div class="tab" id="tab2">
<h2>Dependency Resolution</h2>
<table>
<thead>
<tr>
<th>Dependencies</th>
<th class="numeric">Duration</th>
</tr>
</thead>
<tr>
<td>All dependencies</td>
<td class="numeric">0.119s</td>
</tr>
<tr>
<td>:classpath</td>
<td class="numeric">0.119s</td>
</tr>
</table>
</div>
<div class="tab" id="tab3">
<h2>Artifact Transforms</h2>
<table>
<thead>
<tr>
<th>Transform</th>
<th class="numeric">Duration</th>
</tr>
</thead>
<tr>
<td>All transforms</td>
<td class="numeric">0s</td>
</tr>
</table>
</div>
<div class="tab" id="tab4">
<h2>Task Execution</h2>
<table>
<thead>
<tr>
<th>Task</th>
<th class="numeric">Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td>:</td>
<td class="numeric">0s</td>
<td>(total)</td>
</tr>
<tr>
<td>:app</td>
<td class="numeric">0s</td>
<td>(total)</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 7.0.1</a> at 27/04/2022 14:28:48</p>
</div>
</div>
</body>
</html>
